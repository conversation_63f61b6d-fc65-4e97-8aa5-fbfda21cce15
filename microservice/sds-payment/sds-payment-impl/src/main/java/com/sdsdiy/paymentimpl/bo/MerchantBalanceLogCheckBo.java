package com.sdsdiy.paymentimpl.bo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MerchantBalanceLogCheckBo {

    private Long nowMerchantBalanceLogId;
    private String nowTradeNo;
    private Long merchantId;

    private BigDecimal changedBalance;
    private BigDecimal changedGift;

    private Boolean checked = false;

    private Boolean originBalanceCheck = false;
    private Boolean disposeBalanceCheck = false;

    private String frontTradeNo;
    private Long frontMerchantBalanceLogId;
    private Long nextMerchantBalanceLogId;
    private String nextTradeNo;
}
