package com.sdsdiy.paymentimpl.bo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderFinanceAmountBo {
    
    private Integer billPeriod;

    private Boolean audited = true;

    private BigDecimal cost = BigDecimal.ZERO;

    private BigDecimal factoryCost = BigDecimal.ZERO;

    private BigDecimal logisticsCost = BigDecimal.ZERO;

    private BigDecimal income = BigDecimal.ZERO;

    private BigDecimal profit = BigDecimal.ZERO;

    public OrderFinanceAmountBo addAmount(OrderFinanceAmountBo amountBo) {
        this.cost = this.cost.add(amountBo.getCost());
        this.factoryCost = this.factoryCost.add(amountBo.getFactoryCost());
        this.logisticsCost = this.logisticsCost.add(amountBo.getLogisticsCost());
        this.income = this.income.add(amountBo.getIncome());
        this.profit = this.profit.add(amountBo.getProfit());
        return this;
    }
}
