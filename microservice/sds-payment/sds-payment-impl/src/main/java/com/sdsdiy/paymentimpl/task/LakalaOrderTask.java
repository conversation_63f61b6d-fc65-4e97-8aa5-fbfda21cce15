package com.sdsdiy.paymentimpl.task;

import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.paymentimpl.entity.po.LakalaOrder;
import com.sdsdiy.paymentimpl.manager.LakalaOrderManage;
import com.sdsdiy.paymentimpl.service.pay.LakalaPaymentImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LakalaOrderTask {

    private final LakalaOrderManage lakalaOrderManage;
    private final LakalaPaymentImpl lakalaPayment;

    @XxlJob("lakala-order-process-ledger-task")
    @LogTraceId
    public ReturnT<String> ledgerOrderProcessLedgerTask(String param) {
        XxlJobLogger.log("begin ledgerOrderProcessLedgerTask");
        log.info("begin lakala-order-process-ledger-task");
        List<LakalaOrder> allNotProcessLedger = lakalaOrderManage.findAllNotProcessLedger();
        for (LakalaOrder lakalaOrder : allNotProcessLedger) {
            lakalaPayment.sendLedgerMsg(lakalaOrder);
        }
        log.info("end lakala-order-process-ledger-task");
        XxlJobLogger.log("end ledgerOrderProcessLedgerTask");
        return ReturnT.SUCCESS;
    }
}
