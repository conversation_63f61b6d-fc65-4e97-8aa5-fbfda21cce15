package com.sdsdiy.paymentimpl.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 订单财务月份统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@TableName("monthly_order_finance")
@ApiModel(value = "MonthlyOrderFinance对象", description = "订单财务月份统计")
public class MonthlyOrderFinanceCalculateAmountResultBo {


    @ApiModelProperty(value = "月份，yyyyMM，如 202301")
    private Integer monthly;

    @ApiModelProperty(value = "总收入")
    private BigDecimal income = BigDecimal.ZERO;

    @ApiModelProperty(value = "总成本")
    private BigDecimal cost = BigDecimal.ZERO;

    @ApiModelProperty(value = "工程成本")
    private BigDecimal factoryCost = BigDecimal.ZERO;

    @ApiModelProperty(value = "物流总成本")
    private BigDecimal logisticsCost = BigDecimal.ZERO;

    @ApiModelProperty(value = "利润")
    private BigDecimal profit = BigDecimal.ZERO;

    public void addCost(BigDecimal cost) {
        this.cost = this.cost.add(cost);
    }

    public void addProfit(BigDecimal profit) {
        this.profit = this.profit.add(profit);
    }

    public void addIncome(BigDecimal income) {
        this.income = this.income.add(income);
    }

    public void addFactoryCost(BigDecimal factoryCost) {
        this.factoryCost = this.factoryCost.add(factoryCost);
    }

    public void addLogisticsCost(BigDecimal logistics) {
        this.logisticsCost = this.logisticsCost.add(logistics);
    }

    public MonthlyOrderFinanceCalculateAmountResultBo addAmount(MonthlyOrderFinanceCalculateAmountResultBo result) {
        this.addCost(result.getCost());
        this.addIncome(result.getIncome());
        this.addFactoryCost(result.getFactoryCost());
        this.addLogisticsCost(result.getLogisticsCost());
        this.addProfit(result.getProfit());
        return this;
    }
}
