package com.sdsdiy.paymentimpl.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 订单财务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@NoArgsConstructor
@ApiModel(value = "OrderFinanceExportAmountBo", description = "订单财务Vo")
public class OrderFinanceExportProductVo {

    @ExcelProperty(value = "客户订单号", index = 0)
    private String orderNo;

    @ExcelProperty(value = "第三方订单号", index = 1)
    private String outOrderNo;

    @ExcelProperty(value = "租户", index = 2)
    private String tenantName;

    @ExcelProperty(value = "商户", index = 3)
    private String merchantName;

    @ExcelProperty(value = "开单账号", index = 4)
    private String merchantSysUserName;

    @ExcelProperty(value = "平台赔付金额", index = 5)
    private String platformPayoutAmount;

    @ExcelProperty(value = "物流渠道", index = 6)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String logisticsName;

    @ExcelProperty(value = "物流单号", index = 7)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String carriageNo;

    @ExcelProperty(value = "产品名称", index = 8)
    private String productName;

    @ExcelProperty(value = "规格型号", index = 9)
    private String colorAndSize;

    @ExcelProperty(value = "类型", index = 10)
    private String billPeriodTypeDesc;

    @ExcelProperty(value = "数量", index = 11)
    private Integer num;

    @ExcelProperty(value = "工厂名称", index = 12)
    private String factoryName;

    @ExcelProperty(value = "生产订单号", index = 13)
    private String factoryOrderNo;

    @ExcelProperty(value = "产品供货单价", index = 14)
    private BigDecimal productSupplyUnitPrice;

    @ExcelProperty(value = "产品供货总价", index = 15)
    private BigDecimal productSupplyAmount;

    @ExcelProperty(value = "产品赔付", index = 16)
    private BigDecimal productPayout;

    @ExcelProperty(value = "产品收入", index = 17)
    private BigDecimal productIncome;

    @ExcelProperty(value = "工厂物流", index = 18)
    private BigDecimal factoryLogisticsAmount;

    @ExcelProperty(value = "结算价", index = 19)
    private BigDecimal settlementAmount;

    @ExcelProperty(value = "素材服务费", index = 20)
    private BigDecimal materialServiceAmount;

    @ExcelProperty(value = "服务费", index = 21)
    private BigDecimal serviceAmount;

    @ExcelProperty(value = "客户物流收入", index = 22)
    private BigDecimal customLogisticsIncome;

    @ExcelProperty(value = "客户物流成本", index = 23)
    private BigDecimal customLogisticsCost;

    @ExcelProperty(value = "支付渠道", index = 24)
    private String paymentMethod;

    @ExcelProperty(value = "下单时间", index = 25)
    private String orderTime;

    @ExcelProperty(value = "超期时间", index = 26)
    private String overdueTime;

    @ExcelProperty(value = "完成时间", index = 27)
    private String finishTime;

    @ExcelProperty(value = "账期", index = 28)
    private Integer transferSettleBillPeriod;
}
