package com.sdsdiy.paymentimpl.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 订单财务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@NoArgsConstructor
@ApiModel(value = "OrderFinanceExportAmountBo", description = "订单财务Vo")
public class OrderFinanceExportAmountBo {

    @ExcelProperty(value = "客户订单号", index = 0)
    private String orderNo;

    @ExcelProperty(value = "第三方订单号", index = 1)
    private String outOrderNo;

    @ExcelProperty(value = "租户", index = 2)
    private String tenantName;

    @ExcelProperty(value = "商户", index = 3)
    private String merchantName;

    @ExcelProperty(value = "开单账号", index = 4)
    private String merchantSysUserName;

    @ExcelProperty(value = "订单额度", index = 5)
    private BigDecimal orderQuota;

    @ExcelProperty(value = "账期", index = 6)
    private Integer billPeriod;

    @ExcelProperty(value = "本月收入", index = 7)
    private BigDecimal monthlyIncome = BigDecimal.ZERO;

    @ExcelProperty(value = "本月支出", index = 8)
    private BigDecimal monthlyCost = BigDecimal.ZERO;

    @ExcelProperty(value = "本月利润", index = 9)
    private BigDecimal monthlyProfit = BigDecimal.ZERO;

    @ExcelProperty(value = "总收入", index = 10)
    private BigDecimal income = BigDecimal.ZERO;

    @ExcelProperty(value = "总成本", index = 11)
    private BigDecimal cost = BigDecimal.ZERO;

    @ExcelProperty(value = "总利润", index = 12)
    private BigDecimal profit = BigDecimal.ZERO;
}
