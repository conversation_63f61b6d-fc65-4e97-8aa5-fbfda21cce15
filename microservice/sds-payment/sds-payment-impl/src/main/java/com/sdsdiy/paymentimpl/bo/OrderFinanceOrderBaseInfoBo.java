package com.sdsdiy.paymentimpl.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderFinanceOrderBaseInfoBo {

    private Long tenantId;
    private Long merchantId;

    private Long orderId;
    private String orderNo;

    private String originOrderNo;
    private Long originOrderId;

    private String billPeriodType;
}
