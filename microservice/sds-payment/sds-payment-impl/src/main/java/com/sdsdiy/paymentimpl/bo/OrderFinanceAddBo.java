package com.sdsdiy.paymentimpl.bo;

import com.sdsdiy.paymentimpl.entity.OrderFinance;
import com.sdsdiy.paymentimpl.entity.OrderFinanceBillPeriod;
import com.sdsdiy.paymentimpl.entity.OrderFinanceBillPeriodItem;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderFinanceAddBo {

    private OrderFinance orderFinance;

    private OrderFinanceBillPeriod billPeriod;

    private List<OrderFinanceBillPeriodItem> billPeriodItems;
}
