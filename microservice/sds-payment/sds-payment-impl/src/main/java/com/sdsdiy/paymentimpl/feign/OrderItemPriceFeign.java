package com.sdsdiy.paymentimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.orderapi.api.OrderItemPriceApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "service-order", contextId = "OrderItemPriceFeign", url = MicroServiceEndpointConstant.SERVICE_ORDER)
public interface OrderItemPriceFeign extends OrderItemPriceApi {
}
