package com.sdsdiy.paymentimpl.bo;

import com.sdsdiy.paymentapi.vo.IMerchantName;
import com.sdsdiy.paymentapi.vo.IMerchantSysUserName;
import com.sdsdiy.paymentapi.vo.ITenantName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 订单财务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@NoArgsConstructor
@ApiModel(value = "OrderFinanceExportAmountBo", description = "订单财务Vo")
public class OrderFinanceExportProductBo implements IMerchantName, ITenantName, IMerchantSysUserName {

    private String orderNo;

    private String outOrderNo;

    private String tenantName;

    private String merchantName;

    private String merchantSysUserName;

    private String platformPayoutAmount;

    private String logisticsName;
    private String carriageNo;

    private String productName;

    private String colorAndSize;

    private String billPeriodTypeDesc;

    private Integer num;

    private String factoryName;

    private String factoryOrderNo;

    private BigDecimal productSupplyUnitPrice;

    private BigDecimal productSupplyAmount;

    private BigDecimal productPayout;
    private BigDecimal productIncome;

    private BigDecimal factoryLogisticsAmount;

    private BigDecimal settlementAmount;

    private BigDecimal materialServiceAmount;

    private BigDecimal serviceAmount;


    private BigDecimal customLogisticsIncome;

    private BigDecimal customLogisticsCost;

    private String paymentMethod;

    private String orderTime;

    private String overdueTime;

    private String finishTime;

    private Long tenantId;
    private Long merchantId;
    private Long merchantSysUserId;

    private Integer transferSettleBillPeriod;
}
