package com.sdsdiy.ecommerceapi.api.shein;

import com.sds.platform.sdk.shein.order.*;
import com.sdsdiy.common.annotation.FeignRequest;
import com.sdsdiy.common.base.entity.dto.ResDTO;
import com.sdsdiy.ecommerceapi.dto.EcommerceReqResultDto;
import com.sdsdiy.ecommercedata.dto.SheinOrderDto;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@RequestMapping("/microservice/ecommerce/SheinOrderApi")
public interface SheinOrderApi {
    @PostMapping("orderUploadExpress")
    EcommerceReqResultDto<List<OrderUploadExpressApi.Resp>> orderUploadExpress(@RequestParam String supplierId,
                                                                               @RequestBody OrderUploadExpressApi.Req req);

    @PostMapping("orderPrintExpress")
    EcommerceReqResultDto<List<OrderPrintExpressApi.Resp>> orderPrintExpress(@RequestParam String supplierId,
                                                                             @RequestBody OrderPrintExpressApi.Req req);


    @GetMapping("pullOrder")
    SheinOrderDto pullOrder(@RequestParam String outOrderId, @RequestParam String supplierId);
    @GetMapping("updatePending")
    ResDTO<Boolean> updatePending(@RequestParam String orderNo);



    @FeignRequest(hystrixTimeoutSeconds = 300)
    @PostMapping("getCustomInfoTemplate")
    EcommerceReqResultDto<CustomInfoTemplateResp> getCustomInfoTemplate(@RequestParam String supplierId,
                                                                        @RequestParam String customInfoId);
    @PostMapping("getCompositeTaskResult")
    EcommerceReqResultDto<CompositeQueryTaskResp> getCompositeTaskResult(@RequestParam String supplierId,
                                                                         @RequestParam String taskId);
    @PostMapping("createCompositeTask")
    EcommerceReqResultDto<CompositeTaskResp> createCompositeTask(@RequestParam String supplierId,
                                                                 @RequestBody CompositeTaskReq req);
}
