package com.sdsdiy.ecommerceapi.api;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/microservice/ecommerce/sendRabbitMqApi")
public interface ManualImportOrderApi {

    @PostMapping("/manualSend/{queueName}")
    void manualSendRabbitMq(@PathVariable("queueName") String queueName,
                    @RequestBody String json);


    @PostMapping("manualConsumeMsg")
    void manualConsumeMsg(@RequestBody JSONObject jsonObject);

    @PostMapping("/sendShopMessage")
    void sendShopMessage(@RequestParam("sellerId") String sellerId,
                         @RequestParam("platformCode") String platformCode);
}
