package com.sdsdiy.ecommerceapi.api;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * Etsy相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce")
public interface PingEcommerceApi {

    /**
     * 测试
     *
     * @return
     */
    @GetMapping("/ping")
    String ping();
    /**
     * 测试
     *
     * @return
     */
    @PostMapping("/test")
    String test(@RequestBody JSONObject jsonObject);
    /**
     * 测试
     *
     * @return
     */
    @PostMapping("/test2")
    String test2(@RequestBody JSONObject jsonObject);

    @PostMapping("/getEnv")
    Map<String, Object> getEnv();
}