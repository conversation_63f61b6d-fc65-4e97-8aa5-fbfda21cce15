package com.sdsdiy.ecommerceapi.api.shopify;

import com.sds.platform.sdk.shopify.auth.ShopifyAuthCallbackWebhookParam;
import com.sds.platform.sdk.shopify.auth.ShopifyOauthWebhookReqParam;
import com.sdsdiy.ecommerceapi.dto.shopify.ShopifyAuthDto;
import com.sdsdiy.ecommerceapi.dto.shopify.ShopifyGenAuthDto;
import com.sdsdiy.ecommerceapi.dto.shopify.ShopifyMerchantDto;
import com.sdsdiy.ecommerceapi.param.shopify.ShopifyGenAuthParam;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequestMapping("/microservice/ecommerce/shopify/merchants")
public interface ShopifyMerchantApi {

    @GetMapping("/authRecord")
    ShopifyAuthDto getAuthRecord(@RequestParam String state);

    @PostMapping("/genAuthRedirectUrl")
    ShopifyGenAuthDto genAuthRedirectUrl(@RequestBody @Valid ShopifyGenAuthParam reqBody);

    @PutMapping("/auth/callback")
    ShopifyMerchantDto authCallback(@RequestBody @Valid ShopifyAuthCallbackWebhookParam reqBody);

    @GetMapping("/{shopName}/info")
    ShopifyMerchantDto getMerchantByShopName(@PathVariable String shopName);

    @GetMapping("/{shopName}/expiredToken")
    void expired(@PathVariable String shopName);

    @PostMapping("/auth/oauth2")
    ShopifyGenAuthDto genAuthUrl(@RequestBody @Valid ShopifyOauthWebhookReqParam param);
}
