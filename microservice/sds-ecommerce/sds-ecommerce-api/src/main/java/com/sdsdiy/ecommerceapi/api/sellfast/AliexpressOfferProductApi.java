package com.sdsdiy.ecommerceapi.api.sellfast;

import com.alibaba.fastjson.JSONObject;
import com.sds.platform.sdk.aliexpress.param.ascp.item.ItemTagPrintReq;
import com.sds.platform.sdk.aliexpress.resp.ascp.item.ItemTagPrintResp;
import com.sds.platform.sdk.aliexpress.resp.offer.product.OfferProductQueryResp;
import com.sdsdiy.ecommerceapi.dto.EcommerceReqResultDto;
import com.sdsdiy.ecommercedata.req.aliexpress.AeAegAddressReq;
import com.sdsdiy.ecommercedata.req.aliexpress.AeReq;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/microservice/AliexpressOfferProductApi")
public interface AliexpressOfferProductApi {


    @PostMapping("/offerProductQuery")
    EcommerceReqResultDto<OfferProductQueryResp> offerProductQuery(@RequestBody AeReq<String> req);

    @PostMapping("/itemTagPrint")
    EcommerceReqResultDto<ItemTagPrintResp> itemTagPrint(@RequestBody AeReq<ItemTagPrintReq> req);

    @PostMapping("/offerProductPost")
    EcommerceReqResultDto<Long> offerProductPost(@RequestBody AeReq<JSONObject> req);
}
