package com.sdsdiy.ecommerceapi.api.dhgate;

import com.sdsdiy.ecommerceapi.dhgate.req.order.OrderDeliveryReqForm;
import com.sdsdiy.ecommerceapi.dhgate.resp.order.OrderDeliveryResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/dhgate/online")
public interface DHGateOnlineApi {
    
    /**
     * 订单发货
     *
     * @param supplierId
     * @param reqForm
     * @return
     */
    @PostMapping("/{supplierId}/orderDelivery")
    OrderDeliveryResult orderDelivery(
        @PathVariable String supplierId,
        @RequestBody OrderDeliveryReqForm reqForm
    );
}