package com.sdsdiy.ecommerceapi.api.shopify;

import com.sdsdiy.ecommerceapi.param.shopify.ShopifyButtonNameParam;
import com.sdsdiy.ecommercedata.param.shopify.BatchFixDesignerHtmlMsg;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/microservice/ecommerce/shopify")
public interface ShopifyDesignerApi {

    @PutMapping("/{shopName}/changeButtonName")
    void changeButtonName(@PathVariable String shopName, @RequestBody ShopifyButtonNameParam param);

    @GetMapping("/{shopName}/fixDesignerPage")
    void fixDesignerPage(@PathVariable String shopName);

    @GetMapping("/refreshPublishedProductVendor")
    void refreshPublishedProductVendor(@RequestParam String shopifyShopName);

    @GetMapping("/{shopName}/syncPublishedProduct")
    boolean syncPublishedProduct(@PathVariable String shopName);

    @PostMapping("/designer/batchFixProductDesignerHtml")
    void batchFixProductDesignerHtml(@RequestBody BatchFixDesignerHtmlMsg batchFixDesignerHtmlMsg);
}
