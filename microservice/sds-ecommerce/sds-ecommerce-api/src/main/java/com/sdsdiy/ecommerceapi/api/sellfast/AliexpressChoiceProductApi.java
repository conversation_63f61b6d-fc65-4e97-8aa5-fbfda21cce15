package com.sdsdiy.ecommerceapi.api.sellfast;

import com.sds.platform.sdk.aliexpress.param.choice.product.AeopAEProductDraftReq;
import com.sds.platform.sdk.aliexpress.param.choice.product.PopChoiceProductReq;
import com.sds.platform.sdk.aliexpress.resp.choice.ChoiceDraftProductPostResp;
import com.sds.platform.sdk.aliexpress.resp.choice.ChoiceDraftQueryResp;
import com.sds.platform.sdk.aliexpress.resp.choice.ChoiceProductPostResp;
import com.sds.platform.sdk.aliexpress.resp.choice.ChoiceProductQueryResp;
import com.sdsdiy.ecommerceapi.dto.EcommerceReqResultDto;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/microservice/ecommerce/aliexpress/choice/product")
public interface AliexpressChoiceProductApi {

    @PostMapping("/createDraft")
    EcommerceReqResultDto<ChoiceDraftProductPostResp> createDraft(
            @RequestParam String aliexpressShop,
            @RequestParam String productToken,
            @RequestBody AeopAEProductDraftReq aeopAEProduct);

    @PostMapping("/draftQuery")
    EcommerceReqResultDto<ChoiceDraftQueryResp> draftQuery(
            @RequestParam String aliexpressShop,
            @RequestParam String productToken,
            @RequestBody Long draftId);


    @PostMapping("/postProduct")
    EcommerceReqResultDto<ChoiceProductPostResp> postProduct(
            @RequestParam String aliexpressShop,
            @RequestParam String productToken,
            @RequestBody PopChoiceProductReq popChoiceProductReq);

    @GetMapping("/choiceProductQuery")
    EcommerceReqResultDto<ChoiceProductQueryResp> choiceProductQuery(
            @RequestParam String orderAuthToken
            , @RequestParam String productId);
}
