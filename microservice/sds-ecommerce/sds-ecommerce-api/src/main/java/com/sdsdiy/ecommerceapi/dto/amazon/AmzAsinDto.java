package com.sdsdiy.ecommerceapi.dto.amazon;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 亚马逊产品信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmzAsinDto {

    @JSONField(name = "asin")
    private String asin;

    @JSONField(name = "infoJson")
    private String infoJson;

    @JSONField(name = "marketplaceId")
    private String marketplaceId;

    @JSONField(name = "marketplaceId")
    private Long createAt;

    @JSONField(name = "updateAt")
    private Long updateAt;

    @JSONField(name = "spapiVersion")
    private String spapiVersion;
}
