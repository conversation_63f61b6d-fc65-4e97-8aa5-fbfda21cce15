package com.sdsdiy.ecommerceapi.api.aliyun;

import com.sdsdiy.ecommercedata.param.aliyun.FaceVerifyParam;
import com.sdsdiy.ecommercedata.resp.aliyun.AliyunFaceVerifyResultResp;
import com.sdsdiy.ecommercedata.resp.aliyun.AliyunInitFaceVerifyResp;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/microservice/ecommerce/AliyunFaceVerifyApi")
public interface AliyunFaceVerifyApi {


    @PostMapping("/genFaceVerifyUrl")
    AliyunInitFaceVerifyResp genFaceVerifyUrl(@RequestBody FaceVerifyParam param);

    @GetMapping("/faceVerifyResult")
    AliyunFaceVerifyResultResp faceVerifyResult(@RequestParam("certifyId") String certifyId);

    @GetMapping("/aliFaceVerify/callback")
    void callBack(@RequestParam("callbackToken") String callBackToken,
                  @RequestParam("certifyId") String certifyId,
                  @RequestParam("passed") Integer passed);
}
