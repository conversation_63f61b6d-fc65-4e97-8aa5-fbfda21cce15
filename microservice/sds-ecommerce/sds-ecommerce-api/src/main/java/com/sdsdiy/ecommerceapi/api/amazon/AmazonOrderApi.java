package com.sdsdiy.ecommerceapi.api.amazon;

import com.sdsdiy.ecommerceapi.dto.amazon.AmzOrderDtoV2;
import com.sdsdiy.ecommerceapi.dto.amazon.AmzOrderFeeDto;
import com.sdsdiy.ecommerceapi.dto.amazon.AmzOrderTotalDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 亚马逊订单相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/amazon/order")
public interface AmazonOrderApi {
    
    /**
     * 获取亚马逊订单信息
     *
     * @param orderId
     * @return
     */
    @GetMapping("/{orderId}/total")
    AmzOrderTotalDto getTotal(@PathVariable String orderId);

    /**
     * 获取亚马逊订单信息
     *
     * @param orderId
     * @return
     */
    @GetMapping("/{orderId}/fed")
    AmzOrderFeeDto getFee(@PathVariable String orderId);

    @GetMapping("getOrder")
    AmzOrderDtoV2 getOrder(@RequestParam String orderId);
}