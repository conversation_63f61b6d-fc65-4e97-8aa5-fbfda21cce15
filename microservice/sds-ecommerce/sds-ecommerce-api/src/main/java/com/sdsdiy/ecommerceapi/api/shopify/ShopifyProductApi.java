package com.sdsdiy.ecommerceapi.api.shopify;

import com.sdsdiy.common.annotation.FeignRequest;
import com.sdsdiy.ecommerceapi.dto.EcommerceReqResultDto;
import com.sdsdiy.ecommerceapi.dto.shopify.ProductRespDto;
import com.sdsdiy.ecommerceapi.param.shopify.ShopifyProductParam;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Shopify相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/shopify")
public interface ShopifyProductApi {

    /**
     * graphQL创建产品
     */
    @PostMapping("/{shopName}/graphCreateProduct")
    @FeignRequest(hystrixTimeoutSeconds = 60)
    EcommerceReqResultDto<ProductRespDto> graphCreateProduct(
            @PathVariable String shopName,
            @RequestBody @Valid ShopifyProductParam productParam);

    @DeleteMapping("/{shopName}/graphQlProducts/{productId}")
    void graphDeleteProduct(
            @PathVariable("shopName") String shopName,
            @PathVariable("productId") Long productId
    );

}