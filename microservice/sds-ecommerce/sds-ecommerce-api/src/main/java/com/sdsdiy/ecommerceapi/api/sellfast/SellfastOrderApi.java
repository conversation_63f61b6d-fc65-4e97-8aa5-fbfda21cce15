package com.sdsdiy.ecommerceapi.api.sellfast;

import com.sdsdiy.ecommerceapi.dto.sellfast.SellfastOrderDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 速卖通订单接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/sellfast/order")
public interface SellfastOrderApi {

    /**
     * 获取速卖通订单信息
     *
     * @param orderId
     *
     * @return
     */
    @GetMapping("/{orderId}")
    SellfastOrderDto get(@PathVariable Long orderId);
}