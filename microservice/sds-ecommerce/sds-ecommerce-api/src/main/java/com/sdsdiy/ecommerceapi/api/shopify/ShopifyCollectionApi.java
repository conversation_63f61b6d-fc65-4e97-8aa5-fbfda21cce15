package com.sdsdiy.ecommerceapi.api.shopify;

import com.sdsdiy.ecommerceapi.dto.shopify.ShopifyCustomCollectionsDto;
import com.sdsdiy.ecommerceapi.param.shopify.ShopifyShopsParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Shopify相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/shopify")
public interface ShopifyCollectionApi {

    /**
     * 创建shopify页面
     *
     * @param shopName
     *
     * @return
     */
    @GetMapping("/{shopName}/customCollections")
    ShopifyCustomCollectionsDto getCustomCollections(@PathVariable String shopName);

    /**
     * 批量获取店铺分类
     *
     * @param param
     *
     * @return
     */
    @PostMapping("/shopsCustomCollections")
    List<ShopifyCustomCollectionsDto> getShopsCustomCollections(@RequestBody ShopifyShopsParam param);
}