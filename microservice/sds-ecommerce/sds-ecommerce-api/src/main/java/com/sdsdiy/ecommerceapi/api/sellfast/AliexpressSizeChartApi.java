package com.sdsdiy.ecommerceapi.api.sellfast;

import com.sdsdiy.ecommercedata.resp.aliexpress.sizechart.AeRequireSizeChartResp;
import com.sdsdiy.ecommercedata.resp.aliexpress.sizechart.AeSizeChartListResp;
import com.sdsdiy.ecommercedata.resp.aliexpress.sizechart.AeSupportNewSizeChartResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping("/microservice/ecommerce/sellfast/AliexpressSizeChartApi")
public interface AliexpressSizeChartApi {

    /**
     * 是否支持新版尺码模版列表
     * @param productAuthToken 产品宝token
     * @param categoryId 叶子类目id
     * @return AeSupportNewSizeChartResp
     */
    @GetMapping("/supportNewSizeChart")
    AeSupportNewSizeChartResp supportNewSizeChart(@RequestParam("productAuthToken") String productAuthToken,
                                                  @RequestParam("categoryId") String categoryId);

    /**
     * 判断叶子类目是否必须尺码表
     * @param productAuthToken 产品宝token
     * @param categoryId 叶子类目id
     * @return AeRequireSizeChartResp
     */
    @GetMapping("/categoryRequireSizeChart")
    AeRequireSizeChartResp categoryRequireSizeChart(@RequestParam("productAuthToken") String productAuthToken,
                                                    @RequestParam("categoryId") String categoryId);

    /**
     * 根据尺码大类和尺码小类获取对应尺码模版
     * @param productAuthToken 产品宝token
     * @param type 尺码大类
     * @param subType 尺码小类
     * @return AeSizeChartListResp
     */
    @GetMapping("/sizeChartlist")
    AeSizeChartListResp sizeChartList(@RequestParam("productAuthToken") String productAuthToken,
                                      @RequestParam("type") String type,
                                      @RequestParam("subType") String subType);
}
