package com.sdsdiy.ecommerceapi.dto.amazon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Shopify商户授权请求表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmzMerchantDto {

    /**
     * 授权时生成的唯一识别码
     */
    private String sellingPartnerId;

    private String version;

    /**
     * 回调地址
     */
    private String accessToken;

    private Long merchantId;

    private Long merchantStoreId;

    private Long authUserId;

    private String tokenType;

    private String mwsToken;

    private String authType;

    private Integer expiresIn;

    private Long tokenExpiresAt;

    private Long updateAt;

    private String refreshToken;
}
