package com.sdsdiy.ecommerceapi.api.dhgate;

import com.sdsdiy.ecommerceapi.dhgate.resp.DHGateCountry;
import com.sdsdiy.ecommerceapi.dhgate.resp.Meauser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Shopify相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/dhgate")
public interface DHGateBaseApi {
    
    /**
     * 单位列表
     *
     * @param supplierId
     * @return
     */
    @GetMapping("/{supplierId}/base/measureList")
    List<Meauser> getAllMeasure(@PathVariable String supplierId);
    
    /**
     * 国家列表
     *
     * @param supplierId
     * @return
     */
    @GetMapping("/{supplierId}/base/countryList")
    List<DHGateCountry> getAllCountry(@PathVariable String supplierId);
}