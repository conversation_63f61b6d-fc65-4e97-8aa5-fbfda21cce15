package com.sdsdiy.ecommerceapi.api.dhgate;

import com.sdsdiy.ecommerceapi.dhgate.req.item.ItemGroupQueryParam;
import com.sdsdiy.ecommerceapi.dhgate.req.item.ItemReqForm;
import com.sdsdiy.ecommerceapi.dhgate.resp.item.AfterSale;
import com.sdsdiy.ecommerceapi.dhgate.resp.item.ItemAddResult;
import com.sdsdiy.ecommerceapi.dhgate.resp.item.ItemGroup;
import com.sdsdiy.ecommerceapi.dhgate.resp.item.ItemTemplateList;
import com.sdsdiy.ecommerceapi.dto.EcommerceReqResultDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Shopify相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/dhgate")
public interface DHGateItemApi {
    
    /**
     * 创建产品
     *
     * @return
     */
    @PostMapping("/{supplierId}/item")
    EcommerceReqResultDto<ItemAddResult> create(
        @PathVariable String supplierId,
        @RequestBody ItemReqForm param
    );
    
    /**
     * 产品组列表
     *
     * @param supplierId
     * @param queryParam
     * @return
     */
    @PostMapping("/{supplierId}/item/itemGroup")
    List<ItemGroup> getAllItemGroup(
        @PathVariable String supplierId,
        @RequestBody ItemGroupQueryParam queryParam
    );
    
    @GetMapping("/{supplierId}/item/afterSaleList")
    List<AfterSale> afterSaleList(
        @PathVariable String supplierId
    );
    
    /**
     * 尺码模版列表
     *
     * @param supplierId
     * @param catePubId
     * @return
     */
    @GetMapping("/{supplierId}/item/itemTemplate")
    ItemTemplateList getTemplate(
        @PathVariable String supplierId,
        @RequestParam String catePubId
    );
}