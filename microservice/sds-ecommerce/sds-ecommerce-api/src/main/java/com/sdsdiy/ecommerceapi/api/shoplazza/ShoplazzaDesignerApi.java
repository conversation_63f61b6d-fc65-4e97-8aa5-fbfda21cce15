package com.sdsdiy.ecommerceapi.api.shoplazza;

import com.sdsdiy.ecommercedata.param.shoplazza.BatchEditProductDescParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Shopify相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/shoplazza")
public interface ShoplazzaDesignerApi {

    /**
     * 创建标签和页面
     *
     * @param shopName
     */
    @GetMapping("/{shopName}/openDesigner")
    void open(@PathVariable String shopName);

    /**
     * 修改按钮名称
     *
     * @param shopName
     */
    @PutMapping("/{shopName}/changeButtonName")
    void changeButtonName(@PathVariable String shopName);

    /**
     * 创建标签和页面
     *
     * @param shopName
     */
    @GetMapping("/{shopName}/closeDesigner")
    void close(@PathVariable String shopName);

    /**
     * 修正设计器
     *
     * @param shopName
     */
    @GetMapping("/{shopName}/fixDesigner")
    void fixDesigner(@PathVariable String shopName);


    /**
     * 同步汇出产品
     *
     * @param shopName
     *
     * @return
     */
    @GetMapping("/{shopName}/syncPublishedProduct")
    boolean syncPublishedProduct(@PathVariable String shopName);

    /**
     * 批量修复产品隐藏域内容
     *
     * @param param
     */
    @PostMapping("/designer/batchFixProductDesc")
    void batchFixProductDesc(@RequestBody BatchEditProductDescParam param);

    /**
     * 关闭设计器旧数据处理，全部打开
     *
     * @param shopNames
     *
     * @return
     */
    @PostMapping("/oldOfflineDesignToOpen")
    boolean oldOfflineDesignToOpen(@RequestBody List<String> shopNames);

    /**设计器修复-手动调用定时任务*/
    @GetMapping("manualCallTime")
    boolean manualCallTime();
}