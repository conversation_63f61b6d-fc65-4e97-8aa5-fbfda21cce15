package com.sdsdiy.ecommerceapi.api.dhgate;

import com.sdsdiy.ecommerceapi.dhgate.DHGateAuthCallbackParam;
import com.sdsdiy.ecommerceapi.dhgate.SupplierDto;
import com.sdsdiy.ecommerceapi.dto.dhgate.DHGateAuthRecordDto;
import com.sdsdiy.ecommerceapi.dto.dhgate.DHGateGenAuthDto;
import com.sdsdiy.ecommerceapi.param.dhgate.GenOauthRecordParam;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

/**
 * Shopify相关接口
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/dhgate/oauth2")
public interface DHGateAuthApi {
    
    /**
     * 生成授权链接
     *
     * @return
     */
    @PostMapping("/oauth2")
    DHGateGenAuthDto genAuthUrl(@RequestBody GenOauthRecordParam param);
    
    /**
     * 回调验证
     *
     * @param param
     * @return
     */
    @PostMapping("/sellerInfo")
    DHGateAuthRecordDto getAccessTokenAndSellerInfo(@RequestBody @Valid DHGateAuthCallbackParam param);
    
    @PostMapping("/{state}/authSuccess")
    SupplierDto authSuccess(@PathVariable String state);
}