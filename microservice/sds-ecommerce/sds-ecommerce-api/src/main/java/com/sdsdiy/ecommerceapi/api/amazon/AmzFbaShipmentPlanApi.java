package com.sdsdiy.ecommerceapi.api.amazon;

import com.sdsdiy.ecommercedata.param.amazon.AmzFbaInboundShipmentPlanReqDto;
import com.sdsdiy.ecommerceapi.dto.amazon.AmzFbaOrderRespDto;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 亚马逊fba发货计划
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/ecommerce/amz/fbaShipmentPlans")
public interface AmzFbaShipmentPlanApi {

    @PostMapping("")
    List<AmzFbaOrderRespDto> createPlanToAmz(@RequestBody AmzFbaInboundShipmentPlanReqDto planReqDto);
}