package com.sdsdiy.ecommerceapi.dto.amazon;

import lombok.Data;

import java.util.Map;

/**
 * 阿里订单数据
 *
 * <AUTHOR>
 */
@Data
public class AmazonOrderDtoV1 {

    private String amazonOrderId;

    private String accountId;

    private String buyerEmail;

    private String earliestShipDate;

    private Long earliestShipTime;

    private String fulfillmentChannel;

    private String isBusinessOrder;

    private String isGlobalExpressEnabled;

    private String isISPU;

    private String isPremiumOrder;

    private String isPrime;

    private String isReplacementOrder;

    private String isSoldByAB;

    /**
     * 形如：2021-01-06T23:49:35.542Z
     */
    private String lastUpdateDate;

    /**
     * 形如：2021-01-08T07:59:59Z
     */
    private String latestShipDate;

    private String marketplaceId;

    private String numberOfItemsShipped;

    private String numberOfItemsUnshipped;

    private String orderStatus;

    private Map<String, String> orderTotal;

    private String orderType;

    private String paymentMethod;

    private String purchaseDate;

    /**
     * 秒
     */
    private Long purchaseTime;

    private String salesChannel;

    private String sellerOrderId;

    private String shipmentServiceLevelCategory;

    private String shipServiceLevel;

    private Map<String, String> shippingAddress;
}
