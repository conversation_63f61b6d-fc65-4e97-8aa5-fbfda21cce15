package com.sdsdiy.ecommerceapi.api.aidrawing;

import com.sdsdiy.ecommercedata.dto.aidrawing.Img2promptRespDTO;
import org.springframework.web.bind.annotation.*;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.ecommerceapi.api.aidrawing
 * @Author: lujp
 * @CreateTime: 2023-05-24
 * @Description:
 * @Version: 1.0
 */
@RequestMapping("/microservice/ecommerce/aiDrawing")
public interface AiDrawingDiscordApi {

    @GetMapping("/getImg2promptToken")
    String getImg2promptToken();


    @PutMapping("/updateImg2promptToken")
    void updateImg2promptToken(@RequestParam("token") String token);

    @PostMapping("/img2prompt")
    Img2promptRespDTO img2prompt(@RequestParam("imgUrl") String imgUrl);

    @GetMapping("img2promptResult")
    Img2promptRespDTO img2promptResult(@RequestParam("img2promptId") String img2promptId);
}