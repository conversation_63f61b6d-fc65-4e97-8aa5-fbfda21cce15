package com.sdsdiy.orderdata.dto.qc.parcel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QcParcelPackSaveDTO implements Serializable {
    @ApiModelProperty(value = "订单id", hidden = true)
    private Long orderId;
    @ApiModelProperty(value = "原服务费")
    private BigDecimal originalServiceAmount;
    @ApiModelProperty(value = "当前服务费")
    private BigDecimal currentServiceAmount;
    @ApiModelProperty(value = "差价")
    private Integer differenceAmount;

    @ApiModelProperty(value = "包裹列表")
    private List<ParcelDTO> parcelList;

    @Data
    @NoArgsConstructor
    public static class ParcelDTO {
        @ApiModelProperty(value = "包裹id")
        private Long parcelId;
        @ApiModelProperty(value = "排序")
        private Integer sortWeight;
        @ApiModelProperty(value = "物流id")
        private Long logisticsId;
        @ApiModelProperty(value = "国家物流信息价格表")
        private Long countryExpressInfoNewId;
        @ApiModelProperty("尾程物流渠道id")
        private Long tailLogisticsChannelId;// todo yrs 拆包的话要让前端传
        @ApiModelProperty(value = "运费-商户付的")
        private BigDecimal carriageAmount;
        @ApiModelProperty(value = "运费-租户付的")
        private BigDecimal tenantCarriageAmount;
        @ApiModelProperty(value = "税费")
        private BigDecimal taxAmount;
        @ApiModelProperty(value = "物流服务费")
        private BigDecimal logisticsServiceAmount;
        @ApiModelProperty("FBA贴标费")
        private BigDecimal fbaLabelAmount;
        @ApiModelProperty(value = "清关费")
        private BigDecimal clearanceFee;
        @ApiModelProperty(value = "偏远费用")
        private BigDecimal carriageFarawayAmount;
        @ApiModelProperty(value = "偏远费用类型 NONE=无 ADD=加收 STARTING=起步价")
        private String carriageFarawayType;
        @ApiModelProperty(value = "明细")
        private List<ParcelItemDTO> itemList;
    }

    @Data
    @NoArgsConstructor
    public static class ParcelItemDTO {
        @ApiModelProperty(value = "子单id")
        private Long orderItemId;
        @ApiModelProperty(value = "生产单号")
        private String factoryOrderNo;
        @ApiModelProperty(value = "件数")
        private Integer qty;
    }

}
