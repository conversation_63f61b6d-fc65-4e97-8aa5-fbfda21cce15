package com.sdsdiy.orderdata.dto.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/19
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QcLostReqDTO implements Serializable {
    private static final long serialVersionUID = -1787433254095248107L;
    @ApiModelProperty(value = "工厂单id")
    private Long factoryOrderId;
    @ApiModelProperty(value = "件数")
    private Integer qty;
    @ApiModelProperty(value = "备注")
    private String remark;
}
