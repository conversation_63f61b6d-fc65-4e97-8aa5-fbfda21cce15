package com.sdsdiy.orderdata.dto.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BatchQcPackReqDTO implements Serializable {
    @ApiModelProperty(value = "客户订单id")
    private Long orderId;
    @ApiModelProperty(value = "本次发货明细")
    private List<CurrentShipItemDTO> currentShipItemList;

    @Data
    @NoArgsConstructor
    public static class CurrentShipItemDTO {
        @ApiModelProperty(value = "客户子单id")
        private Long orderItemId;
        @ApiModelProperty(value = "发货明细")
        private Integer qty;
    }
}
