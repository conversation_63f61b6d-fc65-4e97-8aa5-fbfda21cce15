package com.sdsdiy.orderdata.dto.msg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单收货地址记录(AddressReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-07 16:49:05
 */
@Data
public class OrderRefreshMsg implements Serializable {
    
 
        private Long id;
 
        private Long userId;
        private List<Long> ids;
        private String tag;
        //用于索引信息
        private String key;

        private Boolean saveSystemRecommendLogistics;
        private Boolean updateItemFactory;


}