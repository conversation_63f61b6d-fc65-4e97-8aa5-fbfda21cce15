package com.sdsdiy.orderdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.orderdata.vo
 * @Author: lujp
 * @CreateTime: 2023-08-02
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class OrderCanBeFinishVO {
    @ApiModelProperty("支付时间 时间戳")
    private Long payTime;
    @ApiModelProperty("订单号")
    private String no;
    @ApiModelProperty("商户号")
    private String merchantNo;
    @ApiModelProperty("订单类型")
    private String originType;
    @ApiModelProperty("超期时间 时间戳")
    private Long latestTime;
    @ApiModelProperty("子单信息")
    private List<OrderCanBeFinishItemVO> items;
}