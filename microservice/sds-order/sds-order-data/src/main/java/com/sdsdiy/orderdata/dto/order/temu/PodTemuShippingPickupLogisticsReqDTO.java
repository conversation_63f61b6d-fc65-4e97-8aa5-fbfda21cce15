package com.sdsdiy.orderdata.dto.order.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuShippingPickupLogisticsReqDTO implements Serializable {
    private static final long serialVersionUID = -6731795454603344321L;
    private Long issuingBayId;
    private Long merchantId;
    private String outBayCode;
    @ApiModelProperty(value = "店铺id")
    private Long merchantStoreId;
    @ApiModelProperty(value = "箱数")
    private Integer boxNum;
    @ApiModelProperty(value = "预估总重量kg")
    private BigDecimal totalWeight;
    @ApiModelProperty(value = "发货单ids")
    private List<Long> shippingOrderIds;

}
