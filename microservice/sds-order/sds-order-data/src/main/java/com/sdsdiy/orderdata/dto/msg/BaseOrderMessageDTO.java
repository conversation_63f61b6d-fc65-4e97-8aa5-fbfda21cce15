package com.sdsdiy.orderdata.dto.msg;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/6
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BaseOrderMessageDTO implements Serializable {
    private static final long serialVersionUID = 2452873125353358609L;
    private Long orderId;
    private Long userId;
    private Long sendTime;

    public BaseOrderMessageDTO(Long orderId) {
        this.orderId = orderId;
        this.sendTime = System.currentTimeMillis();
    }

    public BaseOrderMessageDTO(Long orderId, Long userId) {
        this.orderId = orderId;
        this.userId = userId;
        this.sendTime = System.currentTimeMillis();
    }
}
