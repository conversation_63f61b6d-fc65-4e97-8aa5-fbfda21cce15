package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BaseColorBlockDTO;
import com.sdsdiy.common.base.entity.dto.BaseImgUrlDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuFullyPoRespDTO implements Serializable {
    private static final long serialVersionUID = 7287086596639263910L;
    @ApiModelProperty(value = "包裹id，唯一")
    private Long orderParcelId;
    @ApiModelProperty(value = "订单id，列表不唯一")
    private Long orderId;
    @ApiModelProperty(value = "SDS订单号")
    private String orderNo;
    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;
    @ApiModelProperty(value = "支付时间")
    private Long payTime;
    @ApiModelProperty(value = "生产完成时间")
    private Long productionCompletedTime;
    @ApiModelProperty(value = "订单版本")
    private String transactionCode;

    private List<Item> itemList;

    private Merchant merchant;
    private MerchantStore merchantStore;

    private TemuPo temuPo;
    @ApiModelProperty(value = "收货仓库")
    private OutBay outBay;


    @Data
    public static class Item {
        private Long id;
        private String no;
        @ApiModelProperty(value = "成品ID")
        private String keyId;
        @ApiModelProperty(value = "补件：1-是")
        private Integer beResendForLose;
        @ApiModelProperty(value = "图片")
        private BaseImgUrlDTO imgUrl;
        private String productName;
        private String textureName;
        private String productSize;
        private Integer num;
        private Integer status;
        @ApiModelProperty("驳回取消")
        private Integer rejectCancel;
        @ApiModelProperty("漏件取消")
        private Integer loseCancel;
        @ApiModelProperty("商户取消数量")
        private Integer merchantCancelQty;

        private BaseColorBlockDTO productColorBlock;

        private ImportProduct importProduct;
    }

    @Data
    public static class ImportProduct {
        @ApiModelProperty(value = "导入sku")
        private String importSku;
    }


    @Data
    public static class Merchant {
        private Long id;
        @ApiModelProperty(value = "商户号")
        private String name;
    }

    @Data
    public static class MerchantStore {
        private Long id;
        @ApiModelProperty(value = "店铺名称")
        private String name;
    }

    @Data
    public static class TemuPo {
        @ApiModelProperty(value = "备货单")
        private String outPoNo;
        @ApiModelProperty(value = "备货单状态")
        private String outPoStatus;
        @ApiModelProperty(value = "倒计时，根据这个截止时间计算")
        private Long poExpirationTime;
        @ApiModelProperty(value = "状态:1-待处理,2-预约失败")
        private Integer status;
        @ApiModelProperty(value = "异常原因：status=2预约失败 时有值")
        private String reserveResult;
        @ApiModelProperty(value = "temu标签：fully_vmi,fully_custom-定制,fully_urgency-急采,fully_jit")
        private List<String> temuLabelList;
    }


    @Data
    public static class OutBay {
        @ApiModelProperty(value = "揽收仓编号")
        private String bayCode;
        @ApiModelProperty(value = "揽收仓名称")
        private String bayName;
    }

}
