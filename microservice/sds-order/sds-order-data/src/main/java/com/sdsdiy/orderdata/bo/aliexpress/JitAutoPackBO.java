package com.sdsdiy.orderdata.bo.aliexpress;

import com.sdsdiy.common.base.entity.dto.BaseLogisticsPackAttributeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class JitAutoPackBO implements Serializable {
    private static final long serialVersionUID = -6291044732152709874L;
    @ApiModelProperty(value = "所有参与组包的明细")
    private List<Po> allOrderList;
    @ApiModelProperty(value = "组包规则")
    private Rule rule;


    @ApiModelProperty(value = "组包结果列表")
    private List<Package> packageList;

    public List<Po> getAllOrderList() {
        return this.allOrderList == null ? Collections.emptyList() : this.allOrderList;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Package extends BaseLogisticsPackAttributeDTO {
        private Long packageId;
        @ApiModelProperty(value = "箱列表")
        private List<Box> boxList;
        @ApiModelProperty(value = "包裹内箱数")
        private Integer boxNum;
        @ApiModelProperty(value = "包裹内订单数")
        private Integer orderNum;
        @ApiModelProperty(value = "揽收仓")
        private String jitBayCode;
        private Long merchantId;
        @ApiModelProperty(value = "物流id")
        private Long logisticsId;
        private List<Po> poList;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Box extends BaseLogisticsPackAttributeDTO {
        private Long boxId;
        @ApiModelProperty(value = "采购单列表")
        private List<Po> orderList;
        @ApiModelProperty(value = "箱内订单数")
        private Integer orderNum;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Po extends BaseLogisticsPackAttributeDTO {
        private Long orderId;
        private Long orderParcelId;
        private Integer qty;
        private Long mainMerchantStoreId;
        private String jitBayCode;
        private Long merchantId;
        private String transactionCode;
        private Long logisticsId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rule {
        @ApiModelProperty(value = "每个包裹内采购单上限：<=0表示不限制")
        private Integer packageOrderLimit;
        @ApiModelProperty(value = "每个包裹内箱子上限：<=0表示不限制")
        private Integer packageBoxLimit;
        @ApiModelProperty(value = "每个箱内件数上限：<=0表示不限制")
        private Integer boxQtyLimit;
        @ApiModelProperty(value = "每个箱内采购单上限：<=0表示不限制")
        private Integer boxOrderLimit;
        @ApiModelProperty(value = "是否过滤掉超过单箱件数上限的订单")
        private Boolean filterOrderByBoxQtyLimit;

        public Boolean getFilterOrderByBoxQtyLimit() {
            return this.filterOrderByBoxQtyLimit != null && this.filterOrderByBoxQtyLimit;
        }
    }
}
