package com.sdsdiy.orderdata.dto.order.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuCreateShippingResultRespDTO implements Serializable {
    @ApiModelProperty(value = "发货单")
    private List<ShippingOrder> shippingOrderList;
    @ApiModelProperty(value = "是否失败：1-是")
    private Integer createFailed;
    @ApiModelProperty(value = "失败原因")
    private String failedMsg;

    @Data
    public static class ShippingOrder {
        @ApiModelProperty(value = "发货单id")
        private Long shippingOrderId;
        @ApiModelProperty(value = "创建结果:成功时是单号，否则失败原因")
        private String reserveResult;
        @ApiModelProperty(value = "是否成功：1-是")
        private Integer success;
    }
}
