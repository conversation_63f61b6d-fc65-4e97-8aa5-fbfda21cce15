package com.sdsdiy.orderdata.dto.msg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 订单收货地址记录(AddressReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-07 16:49:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryOrderOperateRecordMsg implements Serializable {
    
 
        private Integer num;
 
        private Long userId;
        private Long time;
        private String userName;
        //用于索引信息
        private String no;
}