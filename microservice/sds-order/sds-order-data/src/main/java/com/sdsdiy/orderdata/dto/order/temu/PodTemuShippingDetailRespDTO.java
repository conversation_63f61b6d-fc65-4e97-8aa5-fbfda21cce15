package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BaseColorBlockDTO;
import com.sdsdiy.common.base.entity.dto.BaseImgUrlDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@Accessors(chain = true)
public class PodTemuShippingDetailRespDTO {
    @ApiModelProperty("发货单id")
    private Long shippingOrderId;
    @ApiModelProperty(value = "发货单号")
    private String outShippingOrderNo;
    @ApiModelProperty(value = "采购单")
    private List<Po> poList;
    @ApiModelProperty("物流信息")
    private Carriage carriageInfo;
    @ApiModelProperty(value = "箱唛是否全打印过：1-是")
    private Integer allBoxMarkPrinted;

    @Data
    public static class Carriage {
        private Long id;
        @ApiModelProperty(value = "发货类型")
        private String deliveryType;
        @ApiModelProperty(value = "物流ID")
        private Long logisticsId;
        @ApiModelProperty("预约时间")
        private Long bookPickUpTime;
        @ApiModelProperty(value = "物流名称")
        private String logisticsName;
        @ApiModelProperty(value = "运单号")
        private String carriageNo;
        @ApiModelProperty(value = "运单标签")
        private String carriageLabel;
        @ApiModelProperty(value = "运单标签是否打印过：1-是")
        private Integer carriageLabelPrinted;

        @ApiModelProperty(value = "车牌号")
        private String carNumber;
        @ApiModelProperty(value = "司机手机号")
        private String driverPhone;
        @ApiModelProperty(value = "司机名称")
        private String driverName;
    }

    @Data
    public static class Po {
        @ApiModelProperty(value = "包裹id")
        private Long orderParcelId;
        @ApiModelProperty(value = "订单号")
        private String orderNo;
        @ApiModelProperty(value = "备货单号")
        private String outPoNo;
        @ApiModelProperty(value = "子单")
        private List<Item> itemList;
    }

    @Data
    public static class Item {
        private Long id;
        @ApiModelProperty(value = "生产订单号")
        private String no;
        @ApiModelProperty(value = "成品ID")
        private String keyId;
        @ApiModelProperty(value = "图片")
        private BaseImgUrlDTO imgUrl;
        @ApiModelProperty(value = "产品名称")
        private String productName;
        @ApiModelProperty(value = "材质")
        private String textureName;
        @ApiModelProperty(value = "尺寸")
        private String productSize;
        @ApiModelProperty(value = "颜色")
        private BaseColorBlockDTO productColorBlock;
        @ApiModelProperty(value = "数量")
        private Integer num;
        @ApiModelProperty(value = "分类")
        private List<String> categories;
        @ApiModelProperty(value = "包裹id")
        private Long packageId;
        @ApiModelProperty(value = "包裹号")
        private String packageSn;
        @ApiModelProperty(value = "箱唛是否打印过：1-是")
        private Integer boxMarkPrinted;
    }
}
