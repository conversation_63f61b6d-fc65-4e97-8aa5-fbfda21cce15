package com.sdsdiy.orderdata.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.orderdata.vo
 * @Author: lujp
 * @CreateTime: 2023-08-10
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class OrderCanFinishExcelVO {
    @ExcelProperty("订单支付时间")
    private String payTime;
    @ExcelProperty("订单号")
    private String no;
    @ExcelProperty("商户号")
    private String merchantNo;

    @ExcelProperty("订单类型")
    private String originType;

    @ExcelProperty("超期时间")
    private String latestTime;

    @ExcelProperty("子单号")
    private String itemId;

    @ExcelProperty("产品")
    private String productName;

    @ExcelProperty("数量")
    private String quantity;

    @ExcelProperty("仓位号")
    private String warehouseNo;

    @ExcelProperty("工厂")
    private String factoryName;

    @ExcelProperty("生产状态")
    private String productionStatus;
}