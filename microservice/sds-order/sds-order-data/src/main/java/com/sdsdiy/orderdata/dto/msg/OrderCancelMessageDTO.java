package com.sdsdiy.orderdata.dto.msg;

import com.sdsdiy.common.base.entity.dto.BaseIdQtyDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/6
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderCancelMessageDTO implements OrderAfterServiceMessageApi {
    private static final long serialVersionUID = 7524903346377783332L;
    private Long orderId;
    private Long userId;
    private Long afterServiceAuditId;
    private String platform;
    List<BaseIdQtyDTO> cancelOrderItems;

    /**
     * 消息是否来自CheckAndEndOrder方法
     */
    private Boolean fromCheckAndEndOrder = false;

    public Boolean getFromCheckAndEndOrder() {
        return this.fromCheckAndEndOrder != null && this.fromCheckAndEndOrder;
    }
}
