package com.sdsdiy.orderdata.dto.order.refund;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/3
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderRefundAdviseRespDTO implements Serializable {
    private Long orderId;
    private String orderPaymentType;
    @ApiModelProperty("赔付给商户")
    private AmountItem toMerchant;
    @ApiModelProperty("赔付给租户")
    private AmountItem toTenant;

    @ApiModelProperty("商户原金额")
    private AmountItem merchantOriginal;
    @ApiModelProperty("商户已退金额")
    private AmountItem merchantRefunded;
    @ApiModelProperty("商户当前金额")
    private AmountItem merchantCurrent;

    @Data
    public static class AmountItem {
        @ApiModelProperty("总费用")
        private BigDecimal totalAmount = BigDecimal.ZERO;
        @ApiModelProperty("产品费")
        private BigDecimal productAmount = BigDecimal.ZERO;
        @ApiModelProperty("服务费")
        private BigDecimal serviceAmount = BigDecimal.ZERO;
        @ApiModelProperty("物流费")
        private BigDecimal carriageAmount = BigDecimal.ZERO;
        @ApiModelProperty("素材服务费")
        private BigDecimal materialServiceAmount = BigDecimal.ZERO;

        @ApiModelProperty(value = "清关费")
        private BigDecimal clearanceFee;
        @ApiModelProperty(value = "关税金额")
        private BigDecimal taxAmount;
    }
}
