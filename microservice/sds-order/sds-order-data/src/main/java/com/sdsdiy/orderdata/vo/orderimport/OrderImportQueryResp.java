package com.sdsdiy.orderdata.vo.orderimport;

import com.sdsdiy.userdata.dto.MerchantStoreAuthTokenDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class OrderImportQueryResp {

    @ApiModelProperty("店铺信息")
    private MerchantStoreInfo merchantStoreInfo;

    @ApiModelProperty("订单")
    private OrderInfo orderInfo;

    @Data
    @NoArgsConstructor
    public static class MerchantStoreInfo {
        private Long id;
        @ApiModelProperty("商户号")
        private String merchantName;
        @ApiModelProperty("平台code")
        private String platformCode;
        @ApiModelProperty("店铺名称")
        private String merchantStoreName;
        @ApiModelProperty("店铺sellerId")
        private String sellerId;
        @ApiModelProperty("授权状态（非temu）")
        private String authStatus;
        @ApiModelProperty("上次订单拉取时间")
        private String lastOrderSyncTime;
        @ApiModelProperty("拉取结果")
        private String syncResult;
        @ApiModelProperty("预计下次拉取时间")
        private String estimateNextSyncTime;
        @ApiModelProperty("详细的授权信息")
        private List<MerchantStoreAuthTokenDto> authTokenList;
    }

    @Data
    @NoArgsConstructor
    public static class OrderInfo {
        @ApiModelProperty("订单导入结果")
        private String orderSyncResult;
        @ApiModelProperty("未导入原因")
        private String noSyncReason;
        @ApiModelProperty("第三方单号")
        private String outOrderNo;
        @ApiModelProperty("订单上次拉取时间")
        private String lastSyncTime;
        @ApiModelProperty("返回数据内容")
        private String orderJson;
    }
}

