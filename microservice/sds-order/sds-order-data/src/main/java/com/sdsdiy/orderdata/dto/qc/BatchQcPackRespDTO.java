package com.sdsdiy.orderdata.dto.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BatchQcPackRespDTO implements Serializable {
    @ApiModelProperty(value = "客户订单id")
    private Long orderId;
    @ApiModelProperty(value = "包裹id")
    private Long parcelId;

}
