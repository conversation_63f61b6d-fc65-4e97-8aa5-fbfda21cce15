package com.sdsdiy.orderdata.dto.amz;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import lombok.Data;

/**
 * 子单和亚马逊定制信息关联表(OrderItemAmzCustomInfoRel)RespDto类
 *
 * <AUTHOR>
 * @since 2022-06-06 18:14:08
 */
@Data
public class OrderItemAmzCustomInfoRelRespDto extends BaseDTO {
    private static final long serialVersionUID = -90320845580760885L;

    /**
     * amz_order_item_custom_info的id
     */
    private Long amzOrderItemCustomInfoId;
    private Integer isDesign;


}