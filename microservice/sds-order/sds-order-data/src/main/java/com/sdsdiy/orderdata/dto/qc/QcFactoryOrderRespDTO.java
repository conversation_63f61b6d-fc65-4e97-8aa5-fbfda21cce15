package com.sdsdiy.orderdata.dto.qc;

import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QcFactoryOrderRespDTO implements Serializable {
    @ApiModelProperty(value = "客户订单id")
    private Long orderId;
    @ApiModelProperty(value = "交付类型：1-FBA，2-自提，3-寄付，4-送货上门")
    private Integer deliveryType;
    @ApiModelProperty(value = "发货仓")
    private Long issuingBayId;
    @ApiModelProperty(value = "是否可以变更发货仓：1-可以")
    private Integer canChangeIssuingBay;
    @ApiModelProperty(value = "是否跨租户订单：1-是")
    private Integer crossTenantOrder;
    @ApiModelProperty(value = "工厂单列表")
    private List<FactoryOrderDTO> factoryOrderList;

    @ApiModelProperty(value = "工厂统计，批量质检才会返回")
    private List<FactoryDTO> factoryList;

    @ApiModelProperty(value = "打印订单信息按钮")
    private Integer printOrderInfoBtn;
    @ApiModelProperty(value = "打印装箱清单按钮")
    private Integer printBoxDetailBtn;
    @ApiModelProperty(value = "是否全部生产完成")
    private Integer allProductionCompleted;
    @ApiModelProperty(value = "是否全部打印过产品标签，1-是")
    private Integer printProductLabelAllPrinted;

    @Data
    @NoArgsConstructor
    public static class FactoryDTO {
        @ApiModelProperty(value = "工厂id")
        private Long id;
        @ApiModelProperty(value = "工厂名称")
        private String name;
        @ApiModelProperty(value = "本次数量")
        private Integer currentQty;
        @ApiModelProperty(value = "已质检数")
        private Integer qcQty;
        @ApiModelProperty(value = "已取消数")
        private Integer cancelQty;
        @ApiModelProperty(value = "总数")
        private Integer qty;
    }

    @Data
    @NoArgsConstructor
    public static class FactoryOrderDTO {
        @ApiModelProperty(value = "序号")
        private Integer sortNo;
        @ApiModelProperty("转移类型：none-非转移， reject-驳回转移， lose_resend-补件转移， after_service-售后转移")
        private String transferType;
        private Boolean beResendForLose;
        @ApiModelProperty(value = "生产单id")
        private Long factoryOrderId;
        @ApiModelProperty(value = "客户子单id")
        private Long orderItemId;
        @ApiModelProperty(value = "子单数量")
        private Integer orderItemQty;
        @ApiModelProperty(value = "客户子单状态")
        private Integer merchantOrderItemStatus;
        @ApiModelProperty(value = "生产单号")
        private String factoryOrderNo;
        @ApiModelProperty(value = "生产单状态")
        private Integer status;
        @ApiModelProperty(value = "打回重发类型")
        private Integer refuseType;
        @ApiModelProperty(value = "本次可质检数")
        private Integer currentQcQty;
        @ApiModelProperty(value = "数量")
        private Integer qty;
        @ApiModelProperty(value = "质检通过数")
        private Integer qcQty;
        @ApiModelProperty(value = "未通过数")
        private Integer qcFailedQty;
        @ApiModelProperty(value = "已发货/装箱数")
        private Integer shippedQty;
        @ApiModelProperty(value = "是否已全部发货")
        private Boolean allShipped;
        @ApiModelProperty(value = "包裹")
        private List<ParcelDTO> parcelList;

        @ApiModelProperty(value = "质检记录")
        private QcRecordDTO qcRecord;

        @ApiModelProperty(value = "是否本次扫码，1-是")
        private Integer isCurrentScan;
        @ApiModelProperty(value = "是否已质检，1-是")
        private Integer isQc;
        @ApiModelProperty(value = "是否免检，1-是")
        private Integer isExemption;
        @ApiModelProperty(value = "是否打印过产品标签，1-是")
        private Integer itemProductLabelPrinted;

        @ApiModelProperty(value = "成品")
        private EndProductDTO endProduct;
        @ApiModelProperty(value = "工厂")
        private BaseIdAndNameDTO factory;
        @ApiModelProperty(value = "材质")
        private BaseIdAndNameDTO texture;

        @ApiModelProperty(value = "原工厂单号")
        private String originalFactoryOrderNo;

        @ApiModelProperty(value = "自检通过按钮")
        private Integer qcPassBtn;
        @ApiModelProperty(value = "打回重发按钮")
        private Integer resendBtn;
        @ApiModelProperty(value = "能否打回重发-退款：1-可以")
        private Integer resendRefund;
        @ApiModelProperty(value = "补件按钮")
        private Integer lostBtn;
        @ApiModelProperty(value = "是否显示打印产品标签按钮：1-是")
        private Integer printProductLabelBtn;
    }

    @Data
    @NoArgsConstructor
    public static class EndProductDTO {
        @ApiModelProperty(value = "成品id")
        private Long endProductId;
        @ApiModelProperty(value = "成品ID")
        private String keyId;
        @ApiModelProperty(value = "序号")
        private String serial;
        @ApiModelProperty(value = "SKU")
        private String importSku;
        @ApiModelProperty(value = "货号")
        private String productSupplyCode;
        @ApiModelProperty(value = "变体id")
        private Long variantId;
        @ApiModelProperty(value = "产品名称")
        private String productName;
        @ApiModelProperty(value = "模板类型")
        private String prototypeType;
        @ApiModelProperty(value = "颜色")
        private String colorName;
        @ApiModelProperty(value = "尺寸")
        private String size;
        @ApiModelProperty(value = "包装说明")
        private String packExplain;
        @ApiModelProperty(value = "图片")
        private List<String> imgList;

    }

    @Data
    @NoArgsConstructor
    public static class ParcelDTO {
        @ApiModelProperty(value = "包裹id")
        private Long parcelId;
        @ApiModelProperty(value = "包裹")
        private String parcelName;
        @ApiModelProperty(value = "箱号/自提码/运单号")
        private String carriageNo;
        @ApiModelProperty(value = "状态：0-未发货，1-已发货")
        private Integer status;
        @ApiModelProperty(value = "件数")
        private Integer qty;
    }

    @Data
    @NoArgsConstructor
    public static class QcRecordDTO {
        @ApiModelProperty(value = "质检记录id")
        private Long id;
        /**
         * {@link com.sdsdiy.orderdata.enums.QcStatusEnum}
         */
        @ApiModelProperty(value = "质检状态")
        private Integer status;
        @ApiModelProperty(value = "数量")
        private Integer qty;
        @ApiModelProperty(value = "备注")
        private String remark;
        @ApiModelProperty(value = "质检人员")
        private String userName;

    }
}
