package com.sdsdiy.orderdata.dto.order.matchdesign;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 订单匹配成品记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "OrderMatchDesignRecordDTO", description = "订单匹配成品记录DTO")
public class OrderMatchDesignRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @DtoDefault
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "订单id")
    @DtoDefault
    private Long orderId;
    @ApiModelProperty(value = "子单id")
    @DtoDefault
    private Long orderItemId;
    @ApiModelProperty(value = "批次号")
    @DtoDefault
    private String batchNo;
    @ApiModelProperty(value = "成品id")
    @DtoDefault
    private Long designProductId;
    @ApiModelProperty(value = "成品母体keyId")
    @DtoDefault
    private String designProductKeyId;
    @ApiModelProperty(value = "匹配方式")
    @DtoDefault
    private Integer matchType;
    @ApiModelProperty(value = "状态")
    @DtoDefault
    private String status;
    @ApiModelProperty(value = "结果备注")
    @DtoDefault
    private String resultRemark;


}
