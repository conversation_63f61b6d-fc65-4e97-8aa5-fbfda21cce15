package com.sdsdiy.orderdata.dto.order.matchdesign;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderAssociatedDesignMessageDTO implements Serializable {
    /**
     * 有这个，可以不传其他字段
     */
    private Long orderMatchDesignRecordId;

//    private Long orderItemId;
//    private Long designProductId;
//    private Long userId;

    public OrderAssociatedDesignMessageDTO(Long orderMatchDesignRecordId) {
        this.orderMatchDesignRecordId = orderMatchDesignRecordId;
    }

//    public OrderAssociatedDesignMessageDTO(Long orderItemId, Long designProductId) {
//        this.orderItemId = orderItemId;
//        this.designProductId = designProductId;
//    }
}
