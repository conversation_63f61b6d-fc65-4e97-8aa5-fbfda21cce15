package com.sdsdiy.orderdata.dto.order.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuBoxingAddReqDTO implements Serializable {
    private static final long serialVersionUID = 8036897007235299228L;
    @ApiModelProperty("箱id:新增时，传空")
    private Long boxId;
    @ApiModelProperty("订单号/备货单号")
    @NotEmpty(message = "订单号/备货单号不能为空")
    private Set<String> nos;

}
