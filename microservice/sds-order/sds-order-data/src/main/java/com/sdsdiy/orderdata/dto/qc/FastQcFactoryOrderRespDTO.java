package com.sdsdiy.orderdata.dto.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/8
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class FastQcFactoryOrderRespDTO implements Serializable {
    @ApiModelProperty(value = "客户订单id")
    private Long orderId;
    @ApiModelProperty(value = "客户订单号")
    private String orderNo;
    @ApiModelProperty(value = "交付类型：1-FBA，2-自提，3-寄付，4-送货上门，5-JIT")
    private Integer deliveryType;
    @ApiModelProperty(value = "发货仓")
    private Long issuingBayId;

    @ApiModelProperty(value = "是否走正常的质检流程/订单信息：1-是")
    private Integer normalQc;

    @ApiModelProperty(value = "报错信息")
    private String errorMsg;
    @ApiModelProperty(value = "是否需要手动发货：1-是")
    private Integer manualShipment;
    @ApiModelProperty(value = "发货信息")
    private ShipmentInfo shipmentInfo;
    @ApiModelProperty(value = "是否直接打印面单：1-是")
    private Integer printCarriagePdf;

    @Data
    public static class ShipmentInfo {
        @ApiModelProperty(value = "包裹id")
        private Long parcelId;
        @ApiModelProperty(value = "物流名称")
        private String carriageName;
        @ApiModelProperty(value = "运单号")
        private String carriageNo;
        @ApiModelProperty(value = "物流id")
        private Long logisticsId;
        @ApiModelProperty(value = "物流codeId")
        private String logisticsCodeId;
        @ApiModelProperty("物流商id")
        private Long serviceProviderId;
        @ApiModelProperty("是否手动物流：1-是")
        private Integer carriageType;
        @ApiModelProperty(value = "pdfUrl")
        private String pdfUrl;
        @ApiModelProperty(value = "laberPdf")
        private String laberPdf;
    }
}
