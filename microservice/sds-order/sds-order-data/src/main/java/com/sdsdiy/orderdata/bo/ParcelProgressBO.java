package com.sdsdiy.orderdata.bo;

import lombok.Data;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.orderdata.bo.factory.order
 * @Author: lujp
 * @CreateTime: 2023-10-19
 * @Description:
 * @Version: 1.0
 */
@Data
public class ParcelProgressBO {
    private Integer sortWeight;
    private List<Long> productIds;
    private Integer totalQuantity;
    private String carriageName;
    private String carriageNo;
    private String iossStr;
    private String declareProductInfo;
    private String userInfoHtml;
    private String carriageAmountInfo;
    private String serviceFeeInfo;
    /**
     * 有没有申报信息
     */
    private Boolean haveCarriageDeclaration;
}