package com.sdsdiy.orderdata.dto.order.overdue;

import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class MerchantOrderOverdueDetailReqDTO extends BasePageSelect {
    @ApiModelProperty(value = "支付时间开始")
    private Long payStartTime;
    @ApiModelProperty(value = "支付时间截止")
    private Long payEndTime;
    @ApiModelProperty(value = "订单类型")
    private String numType;
    @ApiModelProperty(value = "生产状态：3-未发货、4-已发货")
    private Integer productionStatus;
    @ApiModelProperty(value = "商户名")
    private String merchantName;
    @ApiModelProperty(value = "超期原因ID")
    private Long overdueReasonId;
    @ApiModelProperty(value = "工厂id")
    private Long factoryId;
    @ApiModelProperty(value = "发货区域id")
    private Long issuingBayAreaId;
    @ApiModelProperty(value = "发货仓id")
    private Long issuingBayId;
}
