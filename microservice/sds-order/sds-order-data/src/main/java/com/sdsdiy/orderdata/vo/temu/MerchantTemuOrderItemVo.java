package com.sdsdiy.orderdata.vo.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class MerchantTemuOrderItemVo {

    @ApiModelProperty(value = "采购单号")
    private String parentOrderSn;

    @ApiModelProperty(value = "子单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态")
    private String orderStatusDesc;
}
