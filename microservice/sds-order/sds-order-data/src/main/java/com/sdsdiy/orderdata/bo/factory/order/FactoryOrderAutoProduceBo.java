package com.sdsdiy.orderdata.bo.factory.order;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.orderdata.dto.OrderRemarkRespDto;
import com.sdsdiy.orderdata.dto.factory.order.OrderHistoryDto;
import com.sdsdiy.orderdata.dto.factory.task.FactoryTaskOrderRelRespDto;
import com.sdsdiy.orderdata.dto.warning.OrderEarlyWarningDto;
import com.sdsdiy.productapi.dto.CategoryDto;
import com.sdsdiy.productapi.dto.ProductDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.orderapi.dto.factoryorder
 * @Description: 自动接单聚合信息
 * @date 2021/12/30 17:19
 */
@ApiModel("自动接单聚合信息")
@Data
public class FactoryOrderAutoProduceBo implements Serializable {

    @ApiModelProperty("主键---工厂单主键")
    @DtoDefault
    private Long id;
    @ApiModelProperty("工厂单编号")
    @DtoDefault
    private String no;
    @ApiModelProperty("订单状态")
    @DtoDefault
    private Integer status;
    @ApiModelProperty("订单类别")
    @DtoDefault
    private String originType;
    @ApiModelProperty("是否下载稿件")
    @DtoDefault
    private Integer isDownMaterrial;
    @ApiModelProperty("是否重新下载稿件")
    @DtoDefault
    private Integer manuscriptFeedbackStatus;
    @ApiModelProperty("售后时间")
    @DtoDefault
    private Long afterServiceTime;
    @ApiModelProperty("产品线id")
    @DtoDefault
    private Long productionLineId;

    @ApiModelProperty("下单时间")
    @DtoDefault
    private Date beginTime;
    @DtoDefault
    private Date outHarvestDate;
    @DtoDefault
    private Date outCycleDate;
    @DtoDefault
    private Long productId;
    @DtoDefault
    private Long issuingBayId;
    @DtoDefault
    private Long orderItemId;
    @DtoDefault
    private Integer num;
    @DtoDefault
    private Date outPaymentDate;

    @DtoDefault
    private Long outDate;

    @DtoDefault
    private Integer beResendForLose;

    @DtoDefault
    private Integer expressType;

    @ApiModelProperty("订单告警关系")
    @DtoBind(selfField = "orderItemId", relateField = "orderItemId", showField = "earlyWarningType", provider = "com.sdsdiy.orderimpl.relationprovider.warning.OrderEarlyWarningProvider")
    private List<OrderEarlyWarningDto> orderEarlyWarnings = Lists.newArrayList();

    @DtoDefault
    private Integer refuseType;

}
