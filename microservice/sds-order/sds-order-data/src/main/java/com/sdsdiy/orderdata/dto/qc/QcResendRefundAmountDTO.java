package com.sdsdiy.orderdata.dto.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/27
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QcResendRefundAmountDTO implements Serializable {
    private static final long serialVersionUID = 4443799893420938271L;
    @ApiModelProperty(value = "生产单id")
    private Long factoryOrderId;
    @ApiModelProperty(value = "商品费")
    private BigDecimal refundProductAmount;
    @ApiModelProperty(value = "物流费")
    private BigDecimal refundLogisticsAmount;
    @ApiModelProperty(value = "服务费")
    private BigDecimal refundServiceAmount;
    @ApiModelProperty(value = "素材服务费")
    private BigDecimal refundMaterialServiceAmount;

    @ApiModelProperty(value = "退款总金额")
    private BigDecimal refundTotalAmount;
}
