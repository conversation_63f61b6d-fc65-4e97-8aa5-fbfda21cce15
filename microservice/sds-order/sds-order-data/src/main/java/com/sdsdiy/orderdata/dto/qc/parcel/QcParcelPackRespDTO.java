package com.sdsdiy.orderdata.dto.qc.parcel;

import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QcParcelPackRespDTO implements Serializable {
    private static final long serialVersionUID = 4704269211648178389L;
    @ApiModelProperty(value = "订单id")
    private Long orderId;
    @ApiModelProperty(value = "原运费")
    private BigDecimal originalCarriageAmount;
    @ApiModelProperty(value = "当前运费")
    private BigDecimal currentCarriageAmount;
    @ApiModelProperty(value = "原服务费")
    private BigDecimal originalServiceAmount;
    @ApiModelProperty(value = "当前服务费")
    private BigDecimal currentServiceAmount;
    @ApiModelProperty(value = "差价")
    private BigDecimal differenceAmount;
    @ApiModelProperty(value = "租户差价")
    private BigDecimal tenantDifferenceAmount;
    @ApiModelProperty(value = "订单物流id")
    private Long orderLogisticsId;

    @ApiModelProperty(value = "包裹列表")
    private List<ParcelDTO> parcelList;
    @ApiModelProperty(value = "工厂单列表")
    private List<FactoryOrderDTO> factoryOrderList;

    @Data
    @NoArgsConstructor
    public static class ParcelDTO {
        @ApiModelProperty(value = "包裹id")
        private Long parcelId;
        @ApiModelProperty(value = "排序")
        private Integer sortWeight;
        @ApiModelProperty(value = "状态")
        private Integer status;
        @ApiModelProperty(value = "包裹")
        private String parcelName;
        @ApiModelProperty(value = "物流id")
        private Long logisticsId;
        @ApiModelProperty(value = "国家物流信息价格表")
        private Long countryExpressInfoNewId;
        @ApiModelProperty(value = "物流名称")
        private String carriageName;
        @ApiModelProperty(value = "运单号")
        private String carriageNo;
        @ApiModelProperty(value = "运费")
        private BigDecimal carriageAmount;
        @ApiModelProperty(value = "包裹总件数")
        private Integer totalQty;
        @ApiModelProperty(value = "明细")
        private List<ParcelItemDTO> itemList;
    }

    @Data
    @NoArgsConstructor
    public static class ParcelItemDTO {
        @ApiModelProperty(value = "明细id")
        private Long parcelItemId;
        @ApiModelProperty(value = "包裹id")
        private Long parcelId;
        @ApiModelProperty(value = "子单id")
        private Long orderItemId;
        @ApiModelProperty(value = "件数")
        private Integer qty;
    }

    @Data
    @NoArgsConstructor
    public static class FactoryOrderDTO {
        @ApiModelProperty(value = "子单id")
        private Long orderItemId;
        @ApiModelProperty(value = "生产单id")
        private Long factoryOrderId;
        @ApiModelProperty(value = "生产单号")
        private String factoryOrderNo;
        @ApiModelProperty(value = "是否补件")
        private Boolean beResendForLose;
        @ApiModelProperty(value = "工厂")
        private BaseIdAndNameDTO factory;
        @ApiModelProperty(value = "产品变体id")
        private Long variantId;
        @ApiModelProperty(value = "图片")
        private String imgUrl;
        @ApiModelProperty(value = "产品名称")
        private String productName;
        @ApiModelProperty(value = "颜色")
        private String colorName;
        @ApiModelProperty(value = "尺寸")
        private String size;

        @ApiModelProperty(value = "下单总数")
        private Integer orderItemQty;
        @ApiModelProperty(value = "质检通过数")
        private Integer qcQty;
        @ApiModelProperty(value = "驳回数")
        private Integer rejectQty;
        @ApiModelProperty(value = "漏件数")
        private Integer lessQty;
        @ApiModelProperty(value = "需退款数")
        private Integer cancelQty;
        @ApiModelProperty(value = "可分配数")
        private Integer distributableQty;
        @ApiModelProperty(value = "未装箱数")
        private Integer unPackQty;
    }
}
