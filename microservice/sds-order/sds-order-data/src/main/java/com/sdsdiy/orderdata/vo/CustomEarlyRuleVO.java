package com.sdsdiy.orderdata.vo;

import com.sdsdiy.orderdata.enums.PopChoiceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class CustomEarlyRuleVO {

    private IssuingInfoVO issuingInfo;
    private List<MerchantAndIdVO> merchants;
    private List<String> platformCodes;
    private String country;
    private String countryName;
    private String province;
    private String provinceName;
    private String city;
    private String cityName;
    private List<LogisticsVO> logistics;
    @ApiModelProperty("首单")
    private Integer firstOrder;
    @ApiModelProperty("大于多少件")
    private Integer greaterThan;
    @ApiModelProperty("单子单订单")
    private Integer singleItemOrder;
    @ApiModelProperty("单工厂多子单")
    private Integer singleFactoryMultiItem;
    @ApiModelProperty("多工厂多子单")
    private Integer multiFactoryMultiItem;

    /**
     * @see PopChoiceTypeEnum
     */
    @ApiModelProperty("半托管类型 0 - 全部 1 -半托管 2-全托管")
    private Integer popChoiceType;

    @ApiModelProperty("订单类型 / 订单标签")
    private String orderLabel;

    @Data
    @NoArgsConstructor
    public static class MerchantAndIdVO{
        private Long id;
        private String merchantName;
    }

    @Data
    @NoArgsConstructor
    public static class IssuingInfoVO{
        private Long issuingBayAreaId;
        private String issuingBayAreaName;
        private Long issuingBayId;
        private String issuingBayName;
    }
    @Data
    @NoArgsConstructor
    public static class LogisticsVO{
        private Long logisticsId;
        private String logisticsName;
    }


}