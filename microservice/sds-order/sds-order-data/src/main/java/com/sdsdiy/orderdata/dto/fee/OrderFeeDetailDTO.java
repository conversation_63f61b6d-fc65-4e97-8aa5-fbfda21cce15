package com.sdsdiy.orderdata.dto.fee;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 订单费用明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023/09/21
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "OrderFeeDetailDTO", description = "订单费用明细DTO")
public class OrderFeeDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @DtoDefault
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "订单id")
    @DtoDefault
    private Long orderId;
    @ApiModelProperty(value = "商户id")
    @DtoDefault
    private Long merchantId;
    @ApiModelProperty(value = "租户id")
    @DtoDefault
    private Long tenantId;
    @ApiModelProperty(value = "费用类型")
    @DtoDefault
    private String feeCode;

    @ApiModelProperty(value = "商户付租户")
    private BigDecimal merchantPayTenant;
    @ApiModelProperty(value = "商户付saas")
    private BigDecimal merchantPaySaas;
    @ApiModelProperty(value = "租户付saas")
    private BigDecimal tenantPaySaas;

    @ApiModelProperty(value = "saas退租户")
    private BigDecimal saasRefundTenant;
    @ApiModelProperty(value = "saas退商户")
    private BigDecimal saasRefundMerchant;
    @ApiModelProperty(value = "租户退商户")
    private BigDecimal tenantRefundMerchant;

}
