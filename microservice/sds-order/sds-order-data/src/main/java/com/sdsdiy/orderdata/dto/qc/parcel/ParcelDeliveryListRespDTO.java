package com.sdsdiy.orderdata.dto.qc.parcel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.orderdata.dto.qc.parcel
 * @Author: lujp
 * @CreateTime: 2023-09-20
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class ParcelDeliveryListRespDTO {

    @ApiModelProperty("序号")
    private Integer sort;
    @ApiModelProperty("生产单号")
    private String factoryOrderNo;
    @ApiModelProperty("sku")
    private String sku;
    @ApiModelProperty("成品id")
    private String designProductId;
    @ApiModelProperty("货号")
    private String factoryProductNo;
    @ApiModelProperty("序号")
    private String merchantProductNo;
    @ApiModelProperty("产品名")
    private String productName;
    @ApiModelProperty("材质")
    private String texture;
    @ApiModelProperty("颜色")
    private String color;
    @ApiModelProperty("尺码")
    private String size;
    @ApiModelProperty("总数量")
    private Integer total;
    @ApiModelProperty("本次发货")
    private Integer deliveryCount;
}