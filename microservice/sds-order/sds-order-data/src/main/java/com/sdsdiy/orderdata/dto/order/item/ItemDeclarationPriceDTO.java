package com.sdsdiy.orderdata.dto.order.item;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/27
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ItemDeclarationPriceDTO implements Serializable {
    private Long orderItemId;
    @ApiModelProperty(value = "美元")
    private BigDecimal declarePrice;
    @ApiModelProperty(value = "符号标签")
    private String currentSymbol;
    @ApiModelProperty(value = "产品变体id")
    private Long productVariantId;
}
