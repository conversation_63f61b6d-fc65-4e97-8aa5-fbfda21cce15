package com.sdsdiy.orderdata.dto.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class JitApplyCarriageMessageDTO implements Serializable {
    @ApiModelProperty(value = "jit包裹id")
    private Long aliexpressJitPackageId;
    private Long userId;

    private String jitBayCode;
    private Long issuingBayId;
    private Long merchantStoreId;
    private Long merchantId;
    private Collection<Long> orderParcelIds;

}
