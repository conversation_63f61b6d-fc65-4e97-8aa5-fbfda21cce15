package com.sdsdiy.orderdata.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 重新生成运单中 记录订单或包裹是否需要更新
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.orderdata.bo
 * @Author: lujp
 * @CreateTime: 2023-11-13
 * @Description:
 * @Version: 1.0
 */
@NoArgsConstructor
@Data
public class NeedUpdateFlagBO {
    private Boolean orderUpdate = false;
    private Boolean parcelUpdate = false;
}