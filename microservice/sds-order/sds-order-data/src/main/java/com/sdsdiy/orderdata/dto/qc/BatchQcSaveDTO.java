package com.sdsdiy.orderdata.dto.qc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BatchQcSaveDTO implements Serializable {
    private static final long serialVersionUID = -209738043973171005L;
    @ApiModelProperty(value = "客户订单id")
    private Long orderId;
    @ApiModelProperty(value = "本次发货明细")
    private List<CurrentShipItemDTO> currentShipItemList;
    @ApiModelProperty(value = "是否退款：1-是")
    private Integer isRefund;
    @ApiModelProperty(value = "是否检验包裹（发货）：1-是")
    private Integer isCheckParcel;

    @Data
    @NoArgsConstructor
    public static class CurrentShipItemDTO {
        @ApiModelProperty(value = "工厂单id")
        private Long factorOrderId;
        @ApiModelProperty(value = "子单id")
        private Long orderItemId;
        @ApiModelProperty(value = "驳回数量")
        private Integer rejectQty;
        @ApiModelProperty(value = "漏件数量")
        private Integer lessQty;
        @ApiModelProperty(value = "通过数量")
        private Integer passQty;

        @ApiModelProperty(value = "是否可以发货", hidden = true)
        private Integer shipable;

        public Integer getPassQty() {
            return this.passQty == null ? 0 : this.passQty;
        }

        public Integer getLessQty() {
            return this.lessQty == null ? 0 : this.lessQty;
        }

        public Integer getRejectQty() {
            return this.rejectQty == null ? 0 : this.rejectQty;
        }
    }
}
