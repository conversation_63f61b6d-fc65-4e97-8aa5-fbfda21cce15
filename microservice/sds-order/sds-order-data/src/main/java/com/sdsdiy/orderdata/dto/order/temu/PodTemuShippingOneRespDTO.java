package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuShippingOneRespDTO implements Serializable {
    private static final long serialVersionUID = -6731795454603344321L;
    @ApiModelProperty(value = "发货单")
    private PodTemuShippingUsableListDTO.ShippingOrder shippingOrder;
    private BaseIdAndNameDTO merchant;
    private BaseIdAndNameDTO outBay;
}
