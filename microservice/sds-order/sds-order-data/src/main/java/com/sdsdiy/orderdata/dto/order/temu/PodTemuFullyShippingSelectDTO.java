package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuFullyShippingSelectDTO extends BasePageSelect {
    @ApiModelProperty(value = "发货仓ID")
    private Long issuingBayId;
    @ApiModelProperty(value = "发货区域ID")
    private Long issuingBayAreaId;
    @ApiModelProperty(value = "第三方订单号")
    private String outOrderNo;
    @ApiModelProperty(value = "备货单号")
    private String outPoNo;
    @ApiModelProperty(value = "100订单号")
    private String merchantOrderNo;
    @ApiModelProperty(value = "商户号/商户id")
    private String merchantKeyword;
    //    @ApiModelProperty(value = "店铺")
//    private String storeKeyword;
    @ApiModelProperty(value = "仓库名称")
    private String outBayKeyword;

    @ApiModelProperty(value = "发货单号")
    private String outShippingOrderNo;

    @ApiModelProperty(value = "发货单创建时间开始")
    private String shippingOrderCreateTimeStart;
    @ApiModelProperty(value = "发货单创建时间结束")
    private String shippingOrderCreateTimeEnd;

    @ApiModelProperty(value = "发货批次号")
    private String outBatchNo;

    @ApiModelProperty(value = "发货状态")
    private Integer shippingStatus;

    @ApiModelProperty(value = "发货时间开始")
    private String shippingTimeStart;
    @ApiModelProperty(value = "发货时间结束")
    private String shippingTimeEnd;
    @ApiModelProperty(value = "发货类型：PICK_UP-揽收,SELF_SEND-普通自寄,TRUCK_SEND-卡车")
    private String deliveryType;
}
