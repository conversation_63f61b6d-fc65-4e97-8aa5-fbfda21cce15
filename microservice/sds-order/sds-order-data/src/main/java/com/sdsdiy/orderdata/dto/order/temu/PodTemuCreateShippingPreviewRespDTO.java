package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BaseAddressDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuCreateShippingPreviewRespDTO implements Serializable {
    @ApiModelProperty(value = "发货单")
    private List<ShippingOrder> shippingOrderList;

    @Data
    public static class ShippingOrder {
        @ApiModelProperty(value = "发货单id")
        private Long shippingOrderId;
        @ApiModelProperty(value = "店铺id")
        private Long merchantStoreId;
        @ApiModelProperty(value = "店铺分组id")
        private Long merchantStoreGroupId;
        @ApiModelProperty("是否紧急发货单，0-普通 1-急采")
        private Integer urgencyType;
        @ApiModelProperty(value = "小包数量")
        private Integer poQty;
        private PodTemuFullyBoxingRespDTO.Merchant merchant;
        private OutBay outBay;
        @ApiModelProperty("备货单列表")
        private List<Po> poList;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class OutBay extends BaseAddressDTO {
        @ApiModelProperty(value = "揽收仓编号")
        private String bayCode;
        @ApiModelProperty(value = "揽收仓名称")
        private String bayName;
    }

    @Data
    public static class Po {
        @ApiModelProperty(value = "包裹id：创建时传这个")
        private Long orderParcelId;
        @ApiModelProperty(value = "备货单号")
        private String outPoNo;
    }
}
