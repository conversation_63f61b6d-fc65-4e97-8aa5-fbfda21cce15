package com.sdsdiy.orderdata.dto.order.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Data
@NoArgsConstructor
@HeadFontStyle(fontHeightInPoints = 12)
public class OrderConsigneeInfoExcelDTO implements Serializable {
    @ExcelProperty(value = "订单号")
    private String orderNo;
    @ExcelProperty(value = "生产单号")
    private String factoryOrderNo;
    @ExcelProperty(value = "第三方订单号")
    private String outOrderNo;
    @ExcelProperty(value = "下单日期")
    private String payTimeStr;
    @ExcelProperty(value = "商品名称")
    private String productName;
    @ExcelProperty(value = "产品颜色")
    private String colorName;
    @ExcelProperty(value = "产品尺寸")
    private String size;
    @ExcelProperty(value = "件数")
    private Integer num;
    @ExcelProperty(value = "收件人名称")
    private String consignee;
    @ExcelProperty(value = "收件人电话")
    private String mobilePhone;
    @ExcelProperty(value = "目的地国家")
    private String country;
    @ExcelProperty(value = "州/省")
    private String province;
    @ExcelProperty(value = "城市")
    private String city;
    //    @ExcelProperty(value = "区域-地址1-地址2")
//    private String detail;
    @ExcelProperty(value = "详细地址1")
    private String addressDetail1;
    @ExcelProperty(value = "详细地址2")
    private String addressDetail2;
    @ExcelProperty(value = "详细地址3")
    private String addressDetail3;
    @ExcelProperty(value = "邮政编码")
    private String postcode;
    @ExcelProperty(value = "快递单号")
    private String carriageNo;
    @ExcelProperty(value = "店铺名称")
    private String storeName;
    @ExcelProperty(value = "商户号")
    private String merchantName;
}
