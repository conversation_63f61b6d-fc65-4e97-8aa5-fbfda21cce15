package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BaseAddressDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuCreateShippingReqDTO implements Serializable {
    @ApiModelProperty(value = "是否一键创建：1-是")
    private Integer autoCreate;
    @ApiModelProperty(value = "发货仓ID：一键创建时要传")
    private Long issuingBayId;

    @ApiModelProperty(value = "包裹ids：手动创建时要传")
    private List<Long> orderParcelIds;

}
