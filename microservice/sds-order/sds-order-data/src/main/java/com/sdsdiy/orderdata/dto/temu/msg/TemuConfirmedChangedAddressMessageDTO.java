package com.sdsdiy.orderdata.dto.temu.msg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/28
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TemuConfirmedChangedAddressMessageDTO implements Serializable {
    private String outId;
    private Long merchantId;
}
