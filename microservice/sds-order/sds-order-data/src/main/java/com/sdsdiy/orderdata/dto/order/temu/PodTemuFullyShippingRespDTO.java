package com.sdsdiy.orderdata.dto.order.temu;

import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuFullyShippingRespDTO implements Serializable {
    private static final long serialVersionUID = 7287086596639263910L;
    @ApiModelProperty(value = "发货单id")
    private Long shippingOrderId;

    @ApiModelProperty(value = "收货仓库")
    private OutBay outBay;
    @ApiModelProperty(value = "发货单号订单号")
    private String outShippingOrderNo;
    @ApiModelProperty(value = "物流信息")
    private Carriage carriageInfo;

    @ApiModelProperty(value = "装箱明细")
    private List<Box> boxList;
    @ApiModelProperty(value = "小包信息")
    private List<Po> poList;
    @ApiModelProperty(value = "商户")
    private PodTemuFullyBoxingRespDTO.Merchant merchant;

    @ApiModelProperty(value = "费用")
    private BigDecimal shippingFreight;
    /**
     * @see OfflinePayRecordConstant.StatusEnum
     */
    @ApiModelProperty("支付状态，未支付=UN_PAID、支付中=DURING、已支付=PAID")
    private String paymentStatus;

    @ApiModelProperty(value = "创建时间")
    private Long shippingOrderCreateTime;
    @ApiModelProperty(value = "装箱时间")
    private Long boxCompletedTime;
    @ApiModelProperty(value = "发货时间")
    private Long shippingTime;

    @Data
    public static class Box {
        @ApiModelProperty("箱id")
        private Long boxId;
        @ApiModelProperty("箱号")
        private String boxNo;
        @ApiModelProperty("总件数")
        private Integer totalQty;

        @ApiModelProperty(value = "采购单")
        private List<Po> poList;
    }

    @Data
    public static class Po {
        private Long shippingOrderBoxId;
        @ApiModelProperty(value = "是否配齐：1-是")
        private Integer isBoxed;
        @ApiModelProperty(value = "订单号")
        private String orderNo;
        @ApiModelProperty(value = "备货单号")
        private String outPoNo;
        @ApiModelProperty(value = "包裹号")
        private List<String> outPackageNoList;
        @ApiModelProperty(value = "件数")
        private Integer num;
    }


    @Data
    public static class OutBay {
        @ApiModelProperty(value = "揽收仓编号")
        private String bayCode;
        @ApiModelProperty(value = "揽收仓名称")
        private String bayName;
    }

    @Data
    public static class Carriage {
        @ApiModelProperty(value = "发货类型")
        private String deliveryType;
        @ApiModelProperty(value = "批次号")
        private String outBatchNo;
        @ApiModelProperty(value = "物流ID")
        private Long logisticsId;
        @ApiModelProperty("预约时间")
        private Long bookPickUpTime;
        @ApiModelProperty(value = "物流名称")
        private String logisticsName;
        @ApiModelProperty(value = "运单号")
        private String carriageNo;
        @ApiModelProperty(value = "车牌号")
        private String carNumber;
        @ApiModelProperty(value = "司机手机号")
        private String driverPhone;
        @ApiModelProperty(value = "司机名称")
        private String driverName;
    }

}
