package com.sdsdiy.orderdata.dto.msg;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/10
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class FactoryOrderProductionMessageDTO implements Serializable {
    private Long factoryId;
    /**
     * 属于同一个客户订单
     */
    private List<String> factoryOrderNos;
    /**
     * 客户订单id
     */
    private Long orderId;
    private Long operatorId;
    private Date sendingTime;
    private String orderProgress;
}
