package com.sdsdiy.orderdata.dto.temu.fullyManage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TemuPoInfoVO {

    @ApiModelProperty(value = "备货单号")
    private String outPoNo;
    @ApiModelProperty(value = "采购单状态")
    private String outPoStatus;
    @ApiModelProperty(value = "要求最晚发货时间")
    private Long poExpirationTime;

    @ApiModelProperty(value = "外部收货仓编码")
    private String outBayCode;
    @ApiModelProperty(value = "发货单号")
    private String outShippingOrderNo;
    @ApiModelProperty("发货单状态：1-未配齐,2-待发货，3-已发货")
    private Integer shippingOrderStatus;

    @ApiModelProperty(value = "类型：PICK_UP-揽收,SELF_SEND-普通自寄,TRUCK_SEND-卡车")
    private String deliveryType;
    @ApiModelProperty(value = "物流名称")
    private String logisticsName;
    @ApiModelProperty(value = "运单号")
    private String carriageNo;
    @ApiModelProperty(value = "车牌号")
    private String carNumber;
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;

    @ApiModelProperty(value = "补款账单id")
    private Long offlinePayRecordId;
}
