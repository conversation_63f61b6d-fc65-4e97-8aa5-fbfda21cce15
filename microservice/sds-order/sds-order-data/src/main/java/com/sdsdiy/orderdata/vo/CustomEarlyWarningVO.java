package com.sdsdiy.orderdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class CustomEarlyWarningVO {
    private Long id;
    private String name;
    private String colorCode;
    private List<CustomEarlyRuleVO> rules;
    @ApiModelProperty("0手动 1自动")
    private Integer isAuto;
    @ApiModelProperty("类型 1-系统 不可被删除、编辑   2-自定义")
    private Integer customType;
    private Date updateTime;
}