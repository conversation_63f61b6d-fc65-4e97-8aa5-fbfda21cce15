package com.sdsdiy.orderdata.dto.msg;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderShipmentMessageDTO implements Serializable {
    private Long orderId;
    private String orderProgress;
    private Integer isAdvance;
    /**
     * 包裹id可能没有值
     */
    private Long parcelId;
    private Boolean allShipped = Boolean.TRUE;
    private Long operateUserId;
    private Boolean logisticsChange = Boolean.FALSE;
}
