package com.sdsdiy.orderdata.dto.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TemuOrderDto {
    private Long id;

    @ApiModelProperty(value = "采购单号")
    private String parentOrderSn;

    @ApiModelProperty(value = "站点名")
    private String siteName;

    @ApiModelProperty(value = "站点ID")
    private Integer siteId;

    @ApiModelProperty(value = "国家/地区")
    private String regionName;

    @ApiModelProperty(value = "区域ID")
    private Integer regionId;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态")
    private String orderStatusDesc;

    @ApiModelProperty(value = "订单创建时间")
    private Long orderCreatedTime;

    @ApiModelProperty(value = "最迟发货时间")
    private Long latestShipTime;

    private List<TemuOrderItemDto> items;
}
