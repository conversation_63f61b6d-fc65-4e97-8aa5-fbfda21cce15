package com.sdsdiy.orderdata.dto.order.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuShippingUsableListDTO implements Serializable {
    @ApiModelProperty(value = "发货单")
    private List<ShippingOrder> shippingOrderList;

    @Data
    public static class ShippingOrder {
        @ApiModelProperty(value = "发货单id")
        private Long shippingOrderId;
        @ApiModelProperty(value = "发货单号")
        private String outShippingOrderNo;
        @ApiModelProperty(value = "状态：1-未配齐，2-待发货")
        private Integer status;
    }
}
