package com.sdsdiy.orderdata.dto.order.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PodTemuFullyBoxingRespDTO implements Serializable {
    private static final long serialVersionUID = 7287086596639263910L;
    @ApiModelProperty(value = "发货单id")
    private Long shippingOrderId;

    @ApiModelProperty(value = "收货仓库")
    private OutBay outBay;
    @ApiModelProperty(value = "发货单号订单号")
    private String outShippingOrderNo;
    @ApiModelProperty(value = "发货状态")
    private Integer shippingStatus;

    @ApiModelProperty(value = "是否急采：1-是")
    private Integer urgencyType;

    @ApiModelProperty(value = "最晚发货时间：倒计时根据这个截止时间计算")
    private Long minPoExpirationTime;
    @ApiModelProperty(value = "发货单创建时间")
    private Long shippingOrderCreateTime;

    //    @ApiModelProperty(value = "装箱明细")
//    private List<Box> boxList;
    @ApiModelProperty(value = "小包信息")
    private List<Po> poList;
    @ApiModelProperty(value = "商户")
    private Merchant merchant;


//    @Data
//    public static class Box {
//        @ApiModelProperty("箱id")
//        private Long boxId;
//        @ApiModelProperty("箱号")
//        private String boxNo;
//        @ApiModelProperty("箱子状态")
//        private String boxStatus;
//        @ApiModelProperty("已入箱订单数")
//        private Long boxedNum;
//        @ApiModelProperty("订单总数")
//        private Integer totalNum;
//        @ApiModelProperty(value = "箱创建时间")
//        private Long boxCreateTime;
//        @ApiModelProperty(value = "装箱完成时间")
//        private Long boxCompletedTime;
//    }

    @Data
    public static class Po {
        @ApiModelProperty(value = "是否配齐：1-是")
        private Integer isBoxed;
        @ApiModelProperty(value = "订单号")
        private String orderNo;
        @ApiModelProperty(value = "备货单号")
        private String outPoNo;
        @ApiModelProperty(value = "包裹号")
        private List<String> outPackageNoList;
        @ApiModelProperty(value = "件数")
        private Integer num;
    }


    @Data
    public static class OutBay {
        @ApiModelProperty(value = "揽收仓编号")
        private String bayCode;
        @ApiModelProperty(value = "揽收仓名称")
        private String bayName;
    }

    @Data
    public static class Merchant {
        private Long id;
        @ApiModelProperty(value = "商户id")
        private String code;
        @ApiModelProperty(value = "商户号")
        private String name;
    }
}
