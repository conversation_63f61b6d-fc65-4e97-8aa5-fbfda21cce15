package com.sdsdiy.issuingbayimpl.service.qc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.*;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.issuingbaydata.dto.qc.QcOperateRecordDTO;
import com.sdsdiy.issuingbaydata.dto.qc.QcRecordAddDTO;
import com.sdsdiy.issuingbayimpl.entity.po.qc.QcOperateRecord;
import com.sdsdiy.issuingbayimpl.feign.user.TenantSysUserFeign;
import com.sdsdiy.issuingbayimpl.mapper.qc.QcOperateRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 质检操作记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/09/19
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class QcOperateRecordService extends BaseServiceImpl<QcOperateRecordMapper, QcOperateRecord> {
    private final TenantSysUserFeign tenantSysUserFeign;

    @Transactional(rollbackFor = Exception.class)
    public void saveDtoBatch(List<QcRecordAddDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        Set<Long> factoryOrderIds = new HashSet<>();
        List<QcOperateRecord> recordList = dtoList.stream().map(dto -> {
            factoryOrderIds.add(dto.getFactoryOrderId());
            QcOperateRecord po = BeanUtil.copyProperties(dto, QcOperateRecord.class);
            po.setEvidenceImg(JSON.toJSONString(dto.getEvidenceImgList()));
            po.setPlatform(SdsPlatformEnum.POD.code);
            po.setIsLatest(BasePoConstant.YES);
            return po;
        }).collect(Collectors.toList());
        // 清除旧的标记
        this.lambdaUpdate().in(QcOperateRecord::getFactoryOrderId, factoryOrderIds)
                .eq(QcOperateRecord::getIsLatest, BasePoConstant.YES)
                .set(QcOperateRecord::getIsLatest, BasePoConstant.NO).update();
        this.saveBatch(recordList);
    }

    /**
     * 根据id获取
     *
     * @return dto
     */
    public QcOperateRecordDTO findDtoById(Long id, String fields) {
        QcOperateRecord one = this.lambdaQuery(fields, QcOperateRecordDTO.class)
                .eq(QcOperateRecord::getId, id).one();
        return RelationsBinder.convertAndBind(one, QcOperateRecordDTO.class, fields);
    }

    public List<QcOperateRecordDTO> findDtoByIds(BaseListQueryDTO<Long> reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getList())) {
            return Collections.emptyList();
        }
        List<QcOperateRecord> list = this.lambdaQuery(reqDTO.getFields(), QcOperateRecordDTO.class)
                .in(QcOperateRecord::getId, reqDTO.getList()).list();
        return RelationsBinder.convertAndBind(list, QcOperateRecordDTO.class, reqDTO.getFields());
    }

    public List<QcOperateRecord> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(QcOperateRecord::getId, ids).list();
    }

    public List<QcOperateRecord> findByFactoryOrderId(Long factoryOrderId) {
        return this.lambdaQuery().eq(QcOperateRecord::getFactoryOrderId, factoryOrderId)
                .orderByDesc(QcOperateRecord::getId)
                .list();
    }

    public List<QcOperateRecordDTO> findLatestDtoByFactoryOrderIds(BaseListQueryDTO<Long> reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getList())) {
            return Collections.emptyList();
        }
        List<QcOperateRecord> list = this.lambdaQuery(reqDTO.getFields(), QcOperateRecordDTO.class)
                .eq(QcOperateRecord::getIsLatest, BasePoConstant.YES)
                .in(QcOperateRecord::getFactoryOrderId, reqDTO.getList()).list();
        List<QcOperateRecordDTO> dtoList = RelationsBinder.convertAndBind(list, QcOperateRecordDTO.class, reqDTO.getFields());
        if (reqDTO.getFields() != null && reqDTO.getFields().contains("userName")) {
            this.formatUserName(QcOperateRecordDTO::getUserId, QcOperateRecordDTO::setUserName, dtoList);
        }
        return dtoList;
    }

    public <T> void formatUserName(
            java.util.function.Function<? super T, ? extends Long> getMapper
            , java.util.function.BiConsumer<? super T, String> setMapper
            , List<T> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        Set<Long> userIds = dtoList.stream().map(getMapper).collect(Collectors.toSet());
        List<BaseIdAndNameDTO> nameList = this.tenantSysUserFeign.findNameByIds(BaseListDto.of(userIds));
        Map<Long, String> nameMap = ListUtil.toMapByBaseIdAndName(nameList);
        dtoList.forEach(i -> {
            setMapper.accept(i, nameMap.get(getMapper.apply(i)));
        });
    }

    /**
     * 分页查询
     */
    public PageResultDto<QcOperateRecordDTO> getPage(BasePageSelect pageSelect) {
        Page<QcOperateRecord> page = this.lambdaQuery().orderByDesc(QcOperateRecord::getId)
                .page(new Page<>(pageSelect.getPage(), pageSelect.getSize()));
        return new PageResultDto<>(page.getTotal(),
                ListUtil.copyProperties(page.getRecords(), QcOperateRecordDTO.class));
    }
}
