package com.sdsdiy.issuingbayimpl.entity.bo;

import com.google.common.collect.Lists;
import com.sdsdiy.issuingbayapi.dto.areafactory.FactoryIdKeyBayIdValueMapParam;
import com.sdsdiy.issuingbayimpl.entity.po.BayFactoryDeliveryRule;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class BayIdByConditionBo {
    private BayFactoryDeliveryRule rule;
    private FactoryIdKeyBayIdValueMapParam param;
    private LogisticsRespDto logisticsResp;
    private List<Long> openAccountBayIds;

    public List<Long> getOpenAccountBayIds() {
        if (openAccountBayIds == null) {
            return Lists.newArrayList();
        }
        return this.openAccountBayIds;
    }
}
