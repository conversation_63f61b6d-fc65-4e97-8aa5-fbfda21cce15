package com.sdsdiy.issuingbayimpl.entity.po.aliexpress;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 速卖通JIT仓
 * </p>
 *
 * <AUTHOR>
 * @since 2024/04/22
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "aliexpress_jit_bay", schema = "sds_mc_issuing_bay")
@ApiModel(value = "AliexpressJitBay对象", description = "速卖通JIT仓")
public class AliexpressJitBay extends BaseMapperPO {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "揽收仓库代码")
    private String bayCode;

    @ApiModelProperty(value = "名称")
    private String bayName;

    @ApiModelProperty(value = "地址")
    private String detail;

    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    @ApiModelProperty(value = "联系人")
    private String consignee;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "可揽收的发货仓")
    private String availableIssuingBay;

    @ApiModelProperty(value = "省code")
    private String provinceCode;

    @ApiModelProperty(value = "市code")
    private String cityCode;
}
