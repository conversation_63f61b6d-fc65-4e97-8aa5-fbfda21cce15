package com.sdsdiy.issuingbayimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBayArea;
import com.sdsdiy.issuingbayimpl.mapper.IssuingBayAreaMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 发货区域(IssuingBayArea)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Log4j2
@Service
@DS("common")
public class IssuingBayAreaMapperManager extends BaseServiceImpl<IssuingBayAreaMapper, IssuingBayArea> {

    @Resource
    private IssuingBayAreaMapper issuingBayAreaMapper;


    public List<IssuingBayArea> getByPlatformType(String type) {
        LambdaQueryWrapper<IssuingBayArea> lambdaQueryWrapper = Wrappers.lambdaQuery(IssuingBayArea.class)
                .eq(IssuingBayArea::getType, type)
                .in(IssuingBayArea::getStatus, Lists.newArrayList(CommonStatus.ONLINE.getStatus(), CommonStatus.OFFLINE.getStatus()));
        return this.list(lambdaQueryWrapper);
    }
}