package com.sdsdiy.issuingbayimpl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.base.util.BeanTool;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBay;
import com.sdsdiy.issuingbayimpl.feign.TenantIssuingBayLogisticsAccountFeign;
import com.sdsdiy.issuingbayimpl.feign.TenantLogisticsFeign;
import com.sdsdiy.issuingbayimpl.manager.IssuingBayMapperManager;
import com.sdsdiy.logisticsapi.dto.base.TenantIssuingBayLogisticsAccountRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.issuingbayapi.constant.IssuingBayWarehouseTypeConstant.mainWarehouse;
import static com.sdsdiy.issuingbayapi.constant.IssuingBayWarehouseTypeConstant.ordinary;

/**
 * 发货仓(IssuingBay)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Log4j2
@Service
@DS("common")
public class IssuingBayService {
    @Resource
    private IssuingBayMapperManager issuingBayMapperManager;
    @Resource
    private IssuingBayAreaService issuingBayAreaService;
    @Resource
    private TenantLogisticsFeign tenantLogisticsFeign;
    @Resource
    private BayFactoryDeliveryRuleService bayFactoryDeliveryRuleService;
    @Resource
    private TenantIssuingBayLogisticsAccountFeign tenantIssuingBayLogisticsAccountFeign;

    public IssuingBay getIssuingBayById(Long id) {
        return issuingBayMapperManager.getOne(id);
    }

    public List<BaseIdAndNameDTO> findIdNameByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<IssuingBay> list = issuingBayMapperManager.lambdaQuery()
                .select(IssuingBay::getId, IssuingBay::getName)
                .in(IssuingBay::getId, ids).list();
        return ListUtil.copyProperties(list, BaseIdAndNameDTO.class);
    }

    public List<IssuingBayRespDto> findByIds(Set<Long> ids, String fields) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<IssuingBay> issuingBays = issuingBayMapperManager.listByIds(ids);
        return RelationsBinder.convertAndBind(issuingBays, IssuingBayRespDto.class, fields);
    }

    public List<IssuingBayRespDto> findByBayAreaIds(List<Long> areaIds, String fields) {
        List<IssuingBay> issuingBays = issuingBayMapperManager.findByIssuingBayAreaIds(areaIds);
        return RelationsBinder.convertAndBind(issuingBays, IssuingBayRespDto.class, fields);
    }

    public List<IssuingBay> findByBayAreaId(Long areaId, Long tenantId, String sort) {
        return issuingBayMapperManager.findByIssuingBayAreaId(areaId, tenantId, sort);
    }

    public IssuingBayRespDto findById(Long id, String fields) {
        IssuingBay issuingBay = issuingBayMapperManager.getById(id);
        Assert.validateNull(issuingBay, "发货仓不存在");
        return RelationsBinder.convertAndBind(issuingBay, IssuingBayRespDto.class, fields);
    }

    public void mainWarehouse(Long id) {
        IssuingBay issuingBay = issuingBayMapperManager.getOne(id);
        Assert.validateNull(issuingBay, "发货仓不存在");
        List<IssuingBay> bays = issuingBayMapperManager.findByIssuingBayAreaIds(Lists.newArrayList(issuingBay.getIssuingBayAreaId()));
        List<Long> bayIds = bays.stream().map(IssuingBay::getId).collect(Collectors.toList());
        List<TenantIssuingBayLogisticsAccountRespDto> accounts = tenantIssuingBayLogisticsAccountFeign.listByTenantId(issuingBay.getTenantId(), new BaseListReqDto(bayIds));
        if (!CollectionUtil.isEmpty(accounts)) {
            Set<Long> logisticsList = accounts.stream().filter(t -> YES.equals(t.getIsDeliver()))
                    .map(TenantIssuingBayLogisticsAccountRespDto::getLogisticsId)
                    .collect(Collectors.toSet());

            List<Long> existLogistics = accounts.stream()
                    .filter(t -> YES.equals(t.getIsDeliver()) && id.equals(t.getIssuingBayId()))
                    .map(TenantIssuingBayLogisticsAccountRespDto::getLogisticsId)
                    .collect(Collectors.toList());
            logisticsList.removeAll(existLogistics);
            if (!logisticsList.isEmpty()) {
                List<TenantLogisticsRespDto> list = tenantLogisticsFeign.findByIds(new BaseListReqDto(Lists.newArrayList(logisticsList.iterator())));
                String nameStr = list.stream().map(TenantLogisticsRespDto::getName).collect(Collectors.joining(","));
                throw new BusinessException("当前发货仓缺失物流：" + nameStr + ",请配置之后再操作");
            }
        }
        issuingBayMapperManager.updateMainWarehouse(id, issuingBay.getIssuingBayAreaId());
        tenantIssuingBayLogisticsAccountFeign.updateMainIssuingBay(issuingBay.getIssuingBayAreaId(), id);
        bayFactoryDeliveryRuleService.updateMainIssuingBay(issuingBay.getIssuingBayAreaId(), id);
    }

    public void checkName(Long tenantId, String name, Long id) {
        int count = issuingBayMapperManager.countByName(tenantId, name, id);
        Assert.validateTrue(count > 0, "名称不能重复");
    }

    public List<IssuingBayRespDto> findByTenantId(Long tenantId) {
        List<IssuingBay> issuingBayList = issuingBayMapperManager.findByTenantId(tenantId);
        return BeanTool.copyBeanList(issuingBayList, IssuingBayRespDto.class);
    }

    public void init(Long tenantId, Long id) {
        IssuingBay issuingBay = new IssuingBay();
        issuingBay.setIssuingBayAreaId(id);
        issuingBay.setName("平台仓");
        issuingBay.setWarehouseType(mainWarehouse);
        issuingBay.setPriority(1);
        issuingBay.setStatus(CommonStatus.ONLINE.getStatus());
        issuingBay.setCreatedTime(System.currentTimeMillis());
        issuingBay.setUpdatedTime(System.currentTimeMillis());
        issuingBay.setTenantId(tenantId);
        issuingBayMapperManager.save(issuingBay);
    }

    public List<IssuingBay> findMainWarehouse(Long bayAreaId) {
        return issuingBayMapperManager.lambdaQuery()
                .eq(IssuingBay::getIssuingBayAreaId, bayAreaId)
                .eq(IssuingBay::getWarehouseType, mainWarehouse)
                .eq(IssuingBay::getStatus, CommonStatus.ONLINE.getStatus())
                .list();
    }

    public Map<Long, Long> findAreaBayIdKeyBayIdMap(Collection<Long> bayAreaIds) {
        return issuingBayMapperManager.lambdaQuery()
                .in(IssuingBay::getIssuingBayAreaId, bayAreaIds)
                .eq(IssuingBay::getWarehouseType, mainWarehouse)
                .eq(IssuingBay::getStatus, CommonStatus.ONLINE.getStatus())
                .list().stream().collect(Collectors.toMap(IssuingBay::getIssuingBayAreaId, IssuingBay::getId, (a, b) -> b));
    }

    public List<IssuingBay> listByAreaId(Long bayAreaId, Integer status) {
        return issuingBayMapperManager.lambdaQuery()
                .eq(IssuingBay::getIssuingBayAreaId, bayAreaId)
                .eq(IssuingBay::getStatus, status)
                .list();
    }

    public List<IssuingBay> findContainMainWarehouse(Long id) {
        List<IssuingBay> result = Lists.newArrayList();
        IssuingBay one = issuingBayMapperManager.getOne(id);
        if (one == null) {
            return result;
        }
        if (ordinary.equals(one.getWarehouseType())) {
            List<IssuingBay> mainWarehouses = findMainWarehouse(one.getIssuingBayAreaId());
            result.addAll(mainWarehouses);
        }
        result.add(one);
        return result;
    }
}