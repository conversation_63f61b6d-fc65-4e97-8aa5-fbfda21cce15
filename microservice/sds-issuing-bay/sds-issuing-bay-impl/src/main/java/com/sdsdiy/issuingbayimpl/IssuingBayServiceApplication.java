package com.sdsdiy.issuingbayimpl;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
@EnableFeignClients
@MapperScan("com.sdsdiy.issuingbayimpl.mapper")
public class IssuingBayServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(IssuingBayServiceApplication.class, args);
    }
}
