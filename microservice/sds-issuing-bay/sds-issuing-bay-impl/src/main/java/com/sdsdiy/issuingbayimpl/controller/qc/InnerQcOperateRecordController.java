package com.sdsdiy.issuingbayimpl.controller.qc;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.core.base.util.DateUtil;
import com.sdsdiy.issuingbayapi.api.qc.QcOperateRecordApi;
import com.sdsdiy.issuingbaydata.dto.qc.QcOperateRecordDTO;
import com.sdsdiy.issuingbaydata.dto.qc.QcRecordAddDTO;
import com.sdsdiy.issuingbaydata.dto.qc.QcRecordRespDTO;
import com.sdsdiy.issuingbayimpl.entity.po.qc.QcOperateRecord;
import com.sdsdiy.issuingbayimpl.service.qc.QcOperateRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 质检操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023/09/19
 */
@Log4j2
@RestController()
@RequiredArgsConstructor
public class InnerQcOperateRecordController implements QcOperateRecordApi {
    private final QcOperateRecordService qcOperateRecordService;

    @Override
    public List<QcOperateRecordDTO> findLatestDtoByFactoryOrderIds(BaseListQueryDTO<Long> reqDTO) {
        return this.qcOperateRecordService.findLatestDtoByFactoryOrderIds(reqDTO);
    }

    @Override
    public void saveDtoBatch(List<QcRecordAddDTO> dtoList) {
        this.qcOperateRecordService.saveDtoBatch(dtoList);
    }

    @Override
    public List<QcRecordRespDTO> factoryOrderQcRecord(Long factoryOrderId) {
        List<QcOperateRecord> list = this.qcOperateRecordService.findByFactoryOrderId(factoryOrderId);
        List<QcRecordRespDTO> dtoList = list.stream().map(i -> {
            QcRecordRespDTO dto = BeanUtil.copyProperties(i, QcRecordRespDTO.class);
            dto.setCreateTimeStr(DateUtil.localDataTimeToString(i.getCreateTime()))
                    .setEvidenceImgList(JSON.parseArray(i.getEvidenceImg(), String.class));
            return dto;
        }).collect(Collectors.toList());
        this.qcOperateRecordService.formatUserName(QcRecordRespDTO::getUserId, QcRecordRespDTO::setUserName, dtoList);
        return dtoList;
    }

}

