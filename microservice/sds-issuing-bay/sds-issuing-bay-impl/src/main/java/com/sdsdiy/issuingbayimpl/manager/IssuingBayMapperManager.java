package com.sdsdiy.issuingbayimpl.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.helper.SortRule;
import com.sdsdiy.common.base.helper.SortUtil;
import com.sdsdiy.issuingbayapi.constant.IssuingBayWarehouseTypeConstant;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBay;
import com.sdsdiy.issuingbayimpl.mapper.IssuingBayMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;


/**
 * 发货仓(IssuingBay)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Log4j2
@Service
@DS("common")
public class IssuingBayMapperManager extends ServiceImpl<IssuingBayMapper, IssuingBay> {

    @Resource
    private IssuingBayMapper issuingBayMapper;


    public IssuingBay getOne(Long id) {
        LambdaQueryWrapper<IssuingBay> lambdaQueryWrapper = Wrappers.<IssuingBay>lambdaQuery()
                .eq(IssuingBay::getId, id)
                .ne(IssuingBay::getStatus, CommonStatus.DELETE.getStatus())
                .last("LIMIT 1");
        return issuingBayMapper.selectOne(lambdaQueryWrapper);
    }

    public List<IssuingBay> findByIssuingBayAreaIds(List<Long> issuingBayAreaIds) {
        if (CollUtil.isEmpty(issuingBayAreaIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<IssuingBay> lambdaQueryWrapper = Wrappers.<IssuingBay>lambdaQuery()
                .in(IssuingBay::getIssuingBayAreaId, issuingBayAreaIds)
                .ne(IssuingBay::getStatus, CommonStatus.DELETE.getStatus());
        return issuingBayMapper.selectList(lambdaQueryWrapper);
    }

    public List<IssuingBay> findByIssuingBayAreaId(Long issuingBayAreaId, Long tenantId, String sortStr) {
        LambdaQueryWrapper<IssuingBay> queryWrapper = Wrappers.<IssuingBay>lambdaQuery()
                .eq(IssuingBay::getIssuingBayAreaId, issuingBayAreaId)
                .eq(tenantId != null, IssuingBay::getTenantId, tenantId)
                .ne(IssuingBay::getStatus, CommonStatus.DELETE.getStatus());
        List<SortRule> sortRuleList = SortUtil.getSortRuleList(sortStr);
        for (SortRule sortRule : sortRuleList) {
            if (sortRule.getOrderByDesc().equals(true)) {
                if ("priority".equals(sortRule.getColumnName())) {
                    queryWrapper.orderByDesc(IssuingBay::getPriority);
                }
            } else {
                if ("priority".equals(sortRule.getColumnName())) {
                    queryWrapper.orderByAsc(IssuingBay::getPriority);
                }
            }
        }
        return issuingBayMapper.selectList(queryWrapper);
    }

    public List<IssuingBay> findByTenantId(Long tenantId) {
        return lambdaQuery()
                .eq(IssuingBay::getTenantId, tenantId)
                .list();
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateMainWarehouse(Long id, Long issuingBayAreaId) {
        lambdaUpdate().set(IssuingBay::getWarehouseType, IssuingBayWarehouseTypeConstant.mainWarehouse)
                .set(IssuingBay::getIsConsignment,YES)
                .eq(IssuingBay::getId, id).update();
        lambdaUpdate().set(IssuingBay::getWarehouseType, IssuingBayWarehouseTypeConstant.ordinary)
                .set(IssuingBay::getIsConsignment,NO)
                .eq(IssuingBay::getIssuingBayAreaId, issuingBayAreaId)
                .ne(IssuingBay::getId, id).update();
    }


    public int countByName(Long tenantId, String name, Long id) {
        return lambdaQuery().eq(IssuingBay::getName, name)
                .eq(IssuingBay::getTenantId, tenantId)
                .eq(id != null, IssuingBay::getId, id)
                .ne(IssuingBay::getStatus, CommonStatus.DELETE.getStatus())
                .count();
    }

    public List<IssuingBay> findByBayAreaIds(Collection<Long> areaIds, Long tenantId, Integer status) {
        if (CollUtil.isEmpty(areaIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(IssuingBay::getIssuingBayAreaId, areaIds).eq(IssuingBay::getTenantId, tenantId).eq(IssuingBay::getStatus, status).list();
    }
}