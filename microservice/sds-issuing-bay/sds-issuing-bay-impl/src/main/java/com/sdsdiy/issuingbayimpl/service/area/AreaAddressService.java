package com.sdsdiy.issuingbayimpl.service.area;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Charsets;
import com.sdsdiy.common.base.entity.dto.BaseAddressItemDTO;
import com.sdsdiy.common.base.enums.CountryEnum;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.issuingbaydata.dto.address.AreaAddressDTO;
import com.sdsdiy.issuingbayimpl.feign.user.ConfigAddressFeign;
import com.sdsdiy.userapi.dto.common.ConfigAddressDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
@Service
@RequiredArgsConstructor
public class AreaAddressService {
    private final ConfigAddressFeign configAddressFeign;
    @Autowired
    private AreaAddressService areaAddressService;

    private String getAddressJson(String country) {
        String json = "area/address_%s.json";
        return String.format(json, country.toUpperCase());
    }

    @Cacheable(value = "area_address_list_v6", key = "#country", cacheManager = "redisCacheManager", unless = "#result.isEmpty()")
    public List<BaseAddressItemDTO> areaAddress(String country) {
        if (CountryEnum.CN.equalsCode(country)) {
            List<ConfigAddressDTO> dtoList = configAddressFeign.findByParentId(0L);
            return ListUtil.copyProperties(dtoList, BaseAddressItemDTO.class
                    , (s, t) -> {
                        t.setAliasList(Collections.emptyList());
                        t.setCode(s.getAreaCode());
                        t.setCnName(s.getName());
                    });
        }
        ClassPathResource resource = new ClassPathResource(this.getAddressJson(country));
        String result;
        try {
            result = IoUtil.read(resource.getInputStream(), Charsets.UTF_8);
        } catch (IORuntimeException | IOException e) {
            return Collections.emptyList();
        }
        List<AreaAddressDTO> addressList = JSONUtil.toList(result, AreaAddressDTO.class);
        return ListUtil.copyProperties(addressList, BaseAddressItemDTO.class
                , (s, t) -> {
                    if (StrUtil.isNotBlank(s.getAlias())) {
                        t.setAliasList(Arrays.stream(s.getAlias().split(StrUtil.COMMA)).collect(Collectors.toList()));
                    }
                    if (CollUtil.isNotEmpty(t.getAliasList())) {
                        t.setCode(t.getAliasList().get(0));
                    } else {
                        t.setCode(s.getName());
                    }
                });
    }

    public BaseAddressItemDTO matchProvince(String country, String province) {
        if (StrUtil.isBlank(country) || StrUtil.isBlank(province)) {
            return null;
        }
        country = country.trim();
        province = province.trim();
        List<BaseAddressItemDTO> dtoList = areaAddressService.areaAddress(country);
        for (BaseAddressItemDTO dto : dtoList) {
            if (dto.getName().equalsIgnoreCase(province)) {
                return dto;
            }
            if (CollUtil.isNotEmpty(dto.getAliasList())) {
                for (String alias : dto.getAliasList()) {
                    if (alias.equalsIgnoreCase(province)) {
                        return dto;
                    }
                }
            }
        }
        return null;
    }


}
