package com.sdsdiy.issuingbayimpl.relationprovider;

import com.sdsdiy.core.dtoconvert.BaseRelationBindProvider;
import com.sdsdiy.issuingbayimpl.entity.po.BayFactoryDeliveryRule;
import org.springframework.stereotype.Component;
/**
 * 工厂发货规则(BayFactoryDeliveryRule)关系
 *
 * <AUTHOR>
 * @since 2022-08-02 11:13:18
 */
@Component
public class BayFactoryDeliveryRuleRelationProvider extends BaseRelationBindProvider<BayFactoryDeliveryRule> {

}