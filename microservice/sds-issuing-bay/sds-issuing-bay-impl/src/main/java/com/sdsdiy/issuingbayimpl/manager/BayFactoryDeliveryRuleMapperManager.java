package com.sdsdiy.issuingbayimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.sdsdiy.issuingbayimpl.entity.po.BayFactoryDeliveryRule;

import com.sdsdiy.issuingbayimpl.mapper.BayFactoryDeliveryRuleMapper;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 工厂发货规则(BayFactoryDeliveryRule)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-02 11:13:18
 */
@Slf4j
@Service
@DS("master")
public class BayFactoryDeliveryRuleMapperManager extends ServiceImpl<BayFactoryDeliveryRuleMapper, BayFactoryDeliveryRule>{

    @Resource
    private BayFactoryDeliveryRuleMapper bayFactoryDeliveryRuleMapper;
}