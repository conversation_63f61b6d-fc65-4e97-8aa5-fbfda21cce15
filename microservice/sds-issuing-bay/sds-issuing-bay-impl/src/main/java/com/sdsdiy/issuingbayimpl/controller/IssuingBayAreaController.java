package com.sdsdiy.issuingbayimpl.controller;

import cn.hutool.core.bean.BeanUtil;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.issuingbayapi.api.IssuingBayAreaApi;
import com.sdsdiy.issuingbayapi.dto.area.IssuingBayAreaSaveParam;
import com.sdsdiy.issuingbayapi.dto.area.IssuingBayAreaUpdateParam;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBay;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBayArea;
import com.sdsdiy.issuingbayimpl.manager.IssuingBayMapperManager;
import com.sdsdiy.issuingbayimpl.service.IssuingBayAreaService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货区域(IssuingBayArea)表控制层
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@RestController
public class IssuingBayAreaController implements IssuingBayAreaApi {
    /**
     * 服务对象
     */
    @Resource
    private IssuingBayAreaService issuingBayAreaService;
    @Resource
    private IssuingBayMapperManager issuingBayMapperManager;

    @Override
    public List<IssuingBayAreaRespDto> findByIds(String fields, List<Long> ids) {
        return issuingBayAreaService.findByIds(ids, fields);
    }

    @Override
    public IssuingBayAreaRespDto findById(Long id, String fields) {
        return issuingBayAreaService.findById(id, fields);
    }

    @Override
    public List<IssuingBayAreaRespDto> listAll(Long tenantId, Integer status, String fields) {
        List<IssuingBayArea> issuingBayAreas = issuingBayAreaService.listAll(tenantId, status);
        return RelationsBinder.convertAndBind(issuingBayAreas, IssuingBayAreaRespDto.class, fields);
    }


    @Override
    public List<IssuingBayAreaRespDto> findByIds(BaseListReqDto baseListReqDto) {
        return issuingBayAreaService.findByIds(baseListReqDto.getIdList(), baseListReqDto.getFields());
    }

    @Override
    public void save(@Valid IssuingBayAreaSaveParam param) {
        if (!TenantCommonConstant.SDSDIY_TENANT_ID.equals(param.getTenantId())) {
            param.setIsOpenToOtherTenant(0);
        }
        issuingBayAreaService.save(param);
    }

    @Override
    public void update(Long id, @Valid IssuingBayAreaUpdateParam param) {
        if (!TenantCommonConstant.SDSDIY_TENANT_ID.equals(param.getTenantId())) {
            param.setIsOpenToOtherTenant(0);
        }
        issuingBayAreaService.update(id, param);
    }

    @Override
    public List<IssuingBayAreaRespDto> listAllOpenByTenantId(Long tenantId) {
        List<IssuingBayArea> issuingBayAreas = this.issuingBayAreaService.listAll(tenantId, CommonStatus.ONLINE.getStatus());
        if (!issuingBayAreas.isEmpty()) {
            List<IssuingBayAreaRespDto> res = new ArrayList<>();
            List<IssuingBay> bays = this.issuingBayMapperManager.findByBayAreaIds(issuingBayAreas.stream().map(IssuingBayArea::getId).collect(Collectors.toList()),
                    tenantId, CommonStatus.ONLINE.getStatus());
            Map<Long, List<IssuingBay>> areaIdAndBayMap =
                    bays.stream().collect(Collectors.groupingBy(IssuingBay::getIssuingBayAreaId));
            for (IssuingBayArea bayArea : issuingBayAreas) {
                IssuingBayAreaRespDto respDto = BeanUtil.copyProperties(bayArea, IssuingBayAreaRespDto.class);
                if (areaIdAndBayMap.containsKey(bayArea.getId())) {
                    List<IssuingBay> issuingBays = areaIdAndBayMap.get(bayArea.getId());
                    List<IssuingBayRespDto> bayRespDtoList = new ArrayList<>();
                    for (IssuingBay issuingBay : issuingBays) {
                        IssuingBayRespDto bayRespDto = BeanUtil.copyProperties(issuingBay, IssuingBayRespDto.class);
                        bayRespDtoList.add(bayRespDto);
                    }
                    respDto.setIssuingBays(bayRespDtoList);
                }
                res.add(respDto);
            }
            return res;

        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<IssuingBayAreaRespDto> findListByTenantId(Long tenantId) {
        return issuingBayAreaService.findListByTenantId(tenantId);
    }
}