package com.sdsdiy.issuingbayimpl.controller;

import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.helper.BeanUtils;
import com.sdsdiy.issuingbayapi.api.BayFactoryDeliveryRuleApi;
import com.sdsdiy.issuingbayapi.dto.areafactory.FactoryIdKeyBayIdValueMapParam;
import com.sdsdiy.issuingbayapi.dto.areafactory.IssuingBayFactoryByBayIdResp;
import com.sdsdiy.issuingbayapi.dto.areafactory.IssuingBayFactoryPageParam;
import com.sdsdiy.issuingbaydata.dto.BayFactoryDeliveryRuleDto;
import com.sdsdiy.issuingbayimpl.entity.po.BayFactoryDeliveryRule;
import com.sdsdiy.issuingbayimpl.service.BayFactoryDeliveryRuleService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 工厂发货规则(BayFactoryDeliveryRule)表控制层
 *
 * <AUTHOR>
 * @since 2022-08-02 11:13:18
 */
@RestController
public class BayFactoryDeliveryRuleController implements BayFactoryDeliveryRuleApi {
    /**
     * 服务对象
     */
    @Resource
    private BayFactoryDeliveryRuleService bayFactoryDeliveryRuleService;

    @Override
    public PageListResultDTO<IssuingBayFactoryByBayIdResp> getIssuingBayFactoryByBayId(Long issuingBayId, IssuingBayFactoryPageParam pageParam) {
        return bayFactoryDeliveryRuleService.getIssuingBayFactoryPage(issuingBayId, pageParam);
    }

    @Override
    public Map<Long, Long> getFactoryIdKeyBayIdValueMap(@Valid FactoryIdKeyBayIdValueMapParam param) {
        return bayFactoryDeliveryRuleService.getFactoryIdKeyBayIdValueMap(param);
    }

    @Override
    public Long getOptimalIssuingBay(@Valid FactoryIdKeyBayIdValueMapParam param) {
        return bayFactoryDeliveryRuleService.getOptimalIssuingBay(param);
    }

    @Override
    public List<Long> getUsableIssuingBays(FactoryIdKeyBayIdValueMapParam param) {
        return bayFactoryDeliveryRuleService.getUsableIssuingBays(param);
    }

    @Override
    public List<Long> getFactoryIdsByBayId(Long issuingBayId) {
        return bayFactoryDeliveryRuleService.getFactoryIdsByBayId(issuingBayId);
    }

    @Override
    public Map<Long, Long> countFactoryNumGroupBayIds(BaseListReqDto reqDto) {
        return bayFactoryDeliveryRuleService.countFactoryNumGroupBayIds(reqDto.getIdList());
    }

    @Override
    public List<Long> getFactoryIdsByBayIds(List<Long> issuingBayIdList) {
        return bayFactoryDeliveryRuleService.getFactoryIdsByBayIds(issuingBayIdList);
    }

    @Override
    public List<BayFactoryDeliveryRuleDto> findAllByBayIds(BaseListReqDto issuingBayIdList) {
        List<BayFactoryDeliveryRule> result = bayFactoryDeliveryRuleService.findAllByBayIds(issuingBayIdList.getIdList());
        return BeanUtils.toList(result, BayFactoryDeliveryRuleDto.class);
    }
}
