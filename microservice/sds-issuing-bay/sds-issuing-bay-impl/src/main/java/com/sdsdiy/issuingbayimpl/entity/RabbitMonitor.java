package com.sdsdiy.issuingbayimpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class RabbitMonitor {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String queueName;

    private Integer alarmValue;

    private Integer intervalSecond;

    private String ownerName;

    private String ownerPhone;

    private String alarmContent;

    private Long lastMonitorTime;
}