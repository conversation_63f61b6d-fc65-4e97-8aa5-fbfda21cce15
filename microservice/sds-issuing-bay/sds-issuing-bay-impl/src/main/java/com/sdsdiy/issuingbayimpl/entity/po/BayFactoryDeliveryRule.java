package com.sdsdiy.issuingbayimpl.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;

import java.io.Serializable;

/**
 * 工厂发货规则(BayFactoryDeliveryRule)实体类
 *
 * <AUTHOR>
 * @since 2022-08-02 11:13:18
 */
@Data
public class BayFactoryDeliveryRule extends BaseMapperPO implements Serializable {
    private static final long serialVersionUID = 685725349741101182L;
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 工厂id
     */
    private Long factoryId;
                    
    /**
     * 发货仓id
     */
    private Long issuingBayId;
                    
    /**
     * 规则类型 SYSTEM=系统创建 USER=用户创建 
     */
    private String type;
                    
    /**
     * 商户条件存的是商户id
     */
    private String merchantCondition;

    /**
     * 订单条件SINGLE=单个 MULTI=多个 MULTI_FACTORY_MULTI_ORDER_ITEM 多工厂多子单
     */
    private String orderProductCondition;
                    
    /**
     * NONE COUNTRY PROVINCE CITY
     */
    private String addressType;
                    
    /**
     * 优先级
     */
    private Integer priority;

    private String countryCode;

    private String provinceCode;

    private String cityCode;

    private String countryName;
    private String provinceName;
    private String cityName;
    /**
     * 订单类型条件,分隔 普通订单=COMMON FBA订单=FBA
     */
    private String orderTypeCondition;
    /**
     * 物流类型条件,分普通物流=COMMON 寄付物流=CONSIGNMENT'
     */
    private String logisticsTypeCondition;
}