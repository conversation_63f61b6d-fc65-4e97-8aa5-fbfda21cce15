package com.sdsdiy.issuingbayimpl.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;


/**
 * 发货仓(IssuingBay)实体类
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:10
 */
@Data
public class IssuingBay extends BaseMapperPO {
    private static final long serialVersionUID = -90098106929966963L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * PLATFORM平台PRIVATE自发货
    */
    private String type;
    /**
    * 区域id
    */
    private Long issuingBayAreaId;
    /**
    * 名称
    */
    private String name;
    private String enName;
    /**
    * 国家code
    */
    private String country;
    /**
    * 国家名称
    */
    private String countryName;
    /**
    * 省份code
    */
    private String provinceCode;
    /**
    * 省份名称
    */
    private String province;
    /**
    * 城市code
    */
    private String cityCode;
    /**
    * 城市名称
    */
    private String city;
    /**
    * 详细地址
    */
    private String detail;
    /**
    * 优先级
    */
    private Integer priority;
    /**
    * 状态1开启 2关闭
    */
    private Integer status;
    /**
    * 创建者id
    */
    private Long createdUserId;
    /**
    * 创建时间
    */
    private Long createdTime;
    /**
    * 更新时间
    */
    private Long updatedTime;
    /**
    * 更新者id
    */
    private Long updatedUserId;
    /**
    * 普通仓ordinary主仓main_warehouse
    */
    private String warehouseType;
    /**
    * 租户id
    */
    private Long tenantId;
    /**
     * 是否支持寄付
     */
    private Integer isConsignment;
}