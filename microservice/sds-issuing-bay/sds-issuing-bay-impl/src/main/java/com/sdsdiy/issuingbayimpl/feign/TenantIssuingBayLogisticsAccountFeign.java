package com.sdsdiy.issuingbayimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.logisticsapi.api.TenantIssuingBayLogisticsAccountApi;
import com.sdsdiy.logisticsapi.api.TenantLogisticsApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @author: bin_lin
 * @date: 2020/7/23 20:35
 * @desc:
 */
@FeignClient(name = "service-logistics", contextId = "TenantIssuingBayLogisticsAccountFeign", url = MicroServiceEndpointConstant.SERVICE_LOGISTICS)
public interface TenantIssuingBayLogisticsAccountFeign extends TenantIssuingBayLogisticsAccountApi {

}
