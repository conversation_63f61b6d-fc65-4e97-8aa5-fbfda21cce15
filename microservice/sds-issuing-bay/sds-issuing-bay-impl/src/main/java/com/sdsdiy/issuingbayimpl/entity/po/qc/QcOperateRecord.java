package com.sdsdiy.issuingbayimpl.entity.po.qc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 质检操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023/09/19
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "qc_operate_record",schema = "sds_mc_issuing_bay")
@ApiModel(value = "QcOperateRecord对象", description = "质检操作记录")
public class QcOperateRecord extends BaseMapperPO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工厂订单id")
    private Long factoryOrderId;

    @ApiModelProperty(value = "发货仓id")
    private Long issuingBayId;

    @ApiModelProperty(value = "质检状态")
    private Byte status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "操作者id 0则为系统")
    private Long userId;

    @ApiModelProperty(value = "件数")
    private Integer qty;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "凭证图片")
    private String evidenceImg;

    @ApiModelProperty(value = "是否最新操作")
    private Integer isLatest;

}
