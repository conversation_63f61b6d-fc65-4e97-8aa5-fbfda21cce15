package com.sdsdiy.issuingbayimpl.controller;

import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.issuingbayapi.api.PriorityDispatchRuleApi;
import com.sdsdiy.issuingbaydata.dto.dispatch.*;
import com.sdsdiy.issuingbayimpl.entity.po.PriorityDispatchRule;
import com.sdsdiy.issuingbayimpl.service.dispatch.PriorityDispatchRuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 优先派单规则表(PriorityDispatchRule)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class PriorityDispatchRuleController implements PriorityDispatchRuleApi {

    private final PriorityDispatchRuleService priorityDispatchRuleService;

    @Override
    public PageResultDto<PriorityDispatchRulePageResp> page(Long tenantId, PriorityDispatchRulePageParam select) {
        return priorityDispatchRuleService.page(tenantId, select);
    }

    @Override
    public PriorityDispatchRuleDetailResp getOneDetail(Long id, Long tenantId) {
        PriorityDispatchRule one = priorityDispatchRuleService.one(id, tenantId);
        return RelationsBinder.convertAndBind(one, PriorityDispatchRuleDetailResp.class,"priorityDispatchRuleAreas");
    }

    @Override
    public void delete(Long id, Long tenantId) {
        priorityDispatchRuleService.delete(id, tenantId);
    }

    @Override
    public void insert(PriorityDispatchRuleAddParam param) {
        priorityDispatchRuleService.insert(param);
    }

    @Override
    public void update(Long id, PriorityDispatchRuleAddParam param) {
        priorityDispatchRuleService.update(id, param);
    }



}
