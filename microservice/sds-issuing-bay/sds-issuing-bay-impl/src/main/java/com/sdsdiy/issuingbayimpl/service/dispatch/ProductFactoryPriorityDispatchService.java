package com.sdsdiy.issuingbayimpl.service.dispatch;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.issuingbaydata.dto.dispatch.*;
import com.sdsdiy.issuingbayimpl.entity.po.PriorityDispatchRule;
import com.sdsdiy.issuingbayimpl.entity.po.ProductFactoryPriorityDispatch;
import com.sdsdiy.issuingbayimpl.manager.PriorityDispatchRuleMapperManager;
import com.sdsdiy.issuingbayimpl.manager.ProductFactoryPriorityDispatchMapperManager;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品工厂优先派单配置(ProductFactoryPriorityDispatch)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductFactoryPriorityDispatchService {

    private final PriorityDispatchRuleAreaService priorityDispatchRuleAreaService;
    private final PriorityDispatchRuleMapperManager priorityDispatchRuleMapperManager;
    private final ProductFactoryPriorityDispatchMapperManager productFactoryPriorityDispatchMapperManager;

    public PageResultDto<ProductFactoryPriorityDispatchPageResp> page(ProductFactoryPriorityDispatchPageParam param) {
        Page<PriorityDispatchRule> page = priorityDispatchRuleMapperManager.lambdaQuery()
                .eq(PriorityDispatchRule::getTenantId, param.getTenantId())
                .eq(StringUtil.isNotBlank(param.getType()), PriorityDispatchRule::getType, param.getType())
                .like(StringUtil.isNotBlank(param.getKeyword()), PriorityDispatchRule::getName, param.getKeyword())
                .orderByDesc(PriorityDispatchRule::getUpdateTime, PriorityDispatchRule::getId)
                .page(new Page<>(param.getPage(), param.getSize()));
        List<PriorityDispatchRule> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<ProductFactoryPriorityDispatchPageResp> collect = records.stream().map(record -> {
                ProductFactoryPriorityDispatchPageResp resp = new ProductFactoryPriorityDispatchPageResp();
                resp.setPriorityDispatchRuleId(record.getId());
                resp.setPriorityDispatchRuleName(record.getName());
                resp.setType(record.getType());
                resp.setSupplyChainType(param.getSupplyChainType());
                return resp;
            }).collect(Collectors.toList());
            return new PageResultDto<>(page, collect);
        }
        return new PageResultDto<>();
    }

    @Transactional(rollbackFor = Exception.class)
    public void resetBatch(Long productParentId, List<ProductFactoryPriorityDispatchAddParam> param) {
        //先删除
        productFactoryPriorityDispatchMapperManager.lambdaUpdate()
                .eq(ProductFactoryPriorityDispatch::getProductParentId, productParentId)
                .remove();
        //再批量新增
        if (CollectionUtil.isEmpty(param)) {
            return;
        }
        List<ProductFactoryPriorityDispatch> productFactoryPriorityDispatchList = Lists.newArrayList();
        for (ProductFactoryPriorityDispatchAddParam item : param) {
            if (CollectionUtil.isEmpty(item.getPriorityDispatchRuleIds())) {
                continue;
            }
            for (Long priorityDispatchRuleId : item.getPriorityDispatchRuleIds()) {
                ProductFactoryPriorityDispatch productFactoryPriorityDispatch = new ProductFactoryPriorityDispatch();
                productFactoryPriorityDispatch.setProductParentId(productParentId);
                productFactoryPriorityDispatch.setFactoryId(item.getFactoryId());
                productFactoryPriorityDispatch.setSupplyChainType(item.getSupplyChainType());
                productFactoryPriorityDispatch.setPriorityDispatchRuleId(priorityDispatchRuleId);
                productFactoryPriorityDispatchList.add(productFactoryPriorityDispatch);
            }
        }
        productFactoryPriorityDispatchMapperManager.saveBatch(productFactoryPriorityDispatchList);
    }

    public List<ProductFactoryPriorityDispatchListResp> list(Long productParentId, String supplyChainType) {
        List<ProductFactoryPriorityDispatch> factoryPriorityDispatches = productFactoryPriorityDispatchMapperManager
                .lambdaQuery()
                .eq(ProductFactoryPriorityDispatch::getProductParentId, productParentId)
                .eq(ProductFactoryPriorityDispatch::getSupplyChainType, supplyChainType)
                .list();
        if (CollectionUtil.isEmpty(factoryPriorityDispatches)) {
            return Lists.newArrayList();
        }
        List<Long> ruleIds = factoryPriorityDispatches.stream().map(ProductFactoryPriorityDispatch::getPriorityDispatchRuleId).collect(Collectors.toList());
        List<PriorityDispatchRule> priorityDispatchRules = priorityDispatchRuleMapperManager.lambdaQuery()
                .in(PriorityDispatchRule::getId, ruleIds)
                .list();
        if (CollectionUtil.isEmpty(priorityDispatchRules)) {
            return Lists.newArrayList();
        }
        List<ProductFactoryPriorityDispatchListResp> result = Lists.newArrayList();
        Map<Long, PriorityDispatchRule> ruleMap = priorityDispatchRules.stream().collect(Collectors.toMap(PriorityDispatchRule::getId, rule -> rule));
        Map<Long, List<ProductFactoryPriorityDispatch>> factoryKeyGroup = factoryPriorityDispatches.stream().collect(Collectors.groupingBy(ProductFactoryPriorityDispatch::getFactoryId));
        factoryKeyGroup.forEach((factoryId, productFactoryPriorityDispatches) -> {
            ProductFactoryPriorityDispatchListResp priorityDispatchResp = new ProductFactoryPriorityDispatchListResp();
            priorityDispatchResp.setFactoryId(factoryId);
            ArrayList<ProductFactoryPriorityDispatchListResp.PriorityDispatchRuleDto> list = Lists.newArrayList();
            for (ProductFactoryPriorityDispatch productFactoryPriorityDispatch : productFactoryPriorityDispatches) {
                if (!ruleMap.containsKey(productFactoryPriorityDispatch.getPriorityDispatchRuleId())) {
                    continue;
                }
                ProductFactoryPriorityDispatchListResp.PriorityDispatchRuleDto priorityDispatchRuleDto = new ProductFactoryPriorityDispatchListResp.PriorityDispatchRuleDto();
                priorityDispatchRuleDto.setId(productFactoryPriorityDispatch.getId());
                priorityDispatchRuleDto.setPriorityDispatchRuleId(productFactoryPriorityDispatch.getPriorityDispatchRuleId());
                priorityDispatchRuleDto.setSupplyChainType(productFactoryPriorityDispatch.getSupplyChainType());
                priorityDispatchRuleDto.setType(ruleMap.get(productFactoryPriorityDispatch.getPriorityDispatchRuleId()).getType());
                priorityDispatchRuleDto.setPriorityDispatchRuleName(ruleMap.get(productFactoryPriorityDispatch.getPriorityDispatchRuleId()).getName());
                list.add(priorityDispatchRuleDto);
            }
            priorityDispatchResp.setPriorityDispatchRules(list);
            result.add(priorityDispatchResp);
        });
        return result;
    }

    public List<Long> getPriorityMatchFactoryIds(PriorityMatchFactoryParam param) {
        if (CollectionUtil.isEmpty(param.getFactoryIds()) || CollectionUtil.isEmpty(param.getProductParentIds())
                || StringUtil.isBlank(param.getSupplyChainType()) || StringUtil.isBlank(param.getCountryCode()) || StringUtil.isBlank(param.getProvince())) {
            return Lists.newArrayList();
        }
        //获取到工厂的规则
        List<ProductFactoryPriorityDispatch> dispatches = productFactoryPriorityDispatchMapperManager.lambdaQuery()
                .in(ProductFactoryPriorityDispatch::getFactoryId, param.getFactoryIds())
                .in(ProductFactoryPriorityDispatch::getProductParentId, param.getProductParentIds())
                .eq(ProductFactoryPriorityDispatch::getSupplyChainType, param.getSupplyChainType())
                .list();
        Set<Long> ruleIds = dispatches.stream().map(ProductFactoryPriorityDispatch::getPriorityDispatchRuleId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(ruleIds)) {
            return Lists.newArrayList();
        }
        //获取到规则的优先级
        List<PriorityDispatchRule> priorityDispatchRules = priorityDispatchRuleMapperManager.lambdaQuery()
                .in(PriorityDispatchRule::getId, ruleIds)
                .eq(PriorityDispatchRule::getCountryCode, param.getCountryCode())
                .list();
        if (CollectionUtil.isEmpty(priorityDispatchRules)) {
            return Lists.newArrayList();
        }
        Map<Long, Set<String>> ruleIdKeyAreaMap = priorityDispatchRuleAreaService.getRuleIdKeyAreaMap(ruleIds);
        if (CollectionUtil.isEmpty(ruleIdKeyAreaMap)) {
            return Lists.newArrayList();
        }
        //获取到匹配的规则
        List<Long> matchPriorityDispatchRuleIds = Lists.newArrayList();
        for (PriorityDispatchRule priorityDispatchRule : priorityDispatchRules) {
            //规则的州省不保护的
            Set<String> provinces = ruleIdKeyAreaMap.get(priorityDispatchRule.getId());
            if (CollectionUtil.isEmpty(provinces) || StringUtil.isBlank(param.getProvince())) {
                continue;
            }
            provinces = provinces.stream().map(String::toLowerCase).collect(Collectors.toSet());
            if (!provinces.contains(param.getProvince().toLowerCase())) {
                continue;
            }
            matchPriorityDispatchRuleIds.add(priorityDispatchRule.getId());
        }
        Map<Long, Set<Long>> factoryIdKeyGroup = dispatches.stream()
                .filter(i -> matchPriorityDispatchRuleIds.contains(i.getPriorityDispatchRuleId()))
                .collect(Collectors.groupingBy(ProductFactoryPriorityDispatch::getFactoryId, Collectors.mapping(ProductFactoryPriorityDispatch::getProductParentId, Collectors.toSet())));
        //获取到匹配的工厂
        ArrayList<Long> factoryIds = Lists.newArrayList();
        factoryIdKeyGroup.forEach((factoryId, productParentIdsSet) -> {
            if (productParentIdsSet.size() != param.getProductParentIds().size()) {
                return;
            }
            factoryIds.add(factoryId);
        });
        return factoryIds;
    }

    public void copyPriorityDispatch(Long productParentId, Long newProductParentId) {
        //获取到工厂的规则
        List<ProductFactoryPriorityDispatch> dispatches = productFactoryPriorityDispatchMapperManager.lambdaQuery()
                .eq(ProductFactoryPriorityDispatch::getProductParentId, productParentId)
                .list();
        if (CollectionUtil.isEmpty(dispatches)) {
            return;
        }
        for (ProductFactoryPriorityDispatch dispatch : dispatches) {
            dispatch.setId(null);
            dispatch.setProductParentId(newProductParentId);
        }
        productFactoryPriorityDispatchMapperManager.saveBatch(dispatches);
    }
}
