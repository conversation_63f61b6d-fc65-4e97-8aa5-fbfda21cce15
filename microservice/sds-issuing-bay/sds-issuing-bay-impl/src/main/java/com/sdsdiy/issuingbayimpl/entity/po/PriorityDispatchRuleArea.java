package com.sdsdiy.issuingbayimpl.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;

import java.io.Serializable;

/**
 * 优先派单区域规则表(PriorityDispatchRuleArea)实体类
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:02
 */
@Data
public class PriorityDispatchRuleArea extends BaseMapperPO implements Serializable {
    private static final long serialVersionUID = -78989771149934354L;
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 优先规则id
     */
    private Long priorityDispatchRuleId;

    /**
     * 国家简码
     */
    private String countryCode;

    /**
     * 州省
     */
    private String province;

    /**
     * 省简码别名,使用逗号分隔
     */
    private String provinceCodeMulti;
}
