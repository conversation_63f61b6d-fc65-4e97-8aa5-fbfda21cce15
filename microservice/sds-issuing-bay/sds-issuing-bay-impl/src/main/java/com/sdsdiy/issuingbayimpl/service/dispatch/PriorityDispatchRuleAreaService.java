package com.sdsdiy.issuingbayimpl.service.dispatch;

import cn.hutool.core.collection.CollectionUtil;
import com.beust.jcommander.internal.Maps;
import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Lists;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.issuingbayimpl.entity.po.PriorityDispatchRuleArea;
import com.sdsdiy.issuingbayimpl.manager.PriorityDispatchRuleAreaMapperManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 优先派单区域规则表(PriorityDispatchRuleArea)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PriorityDispatchRuleAreaService {

    private final PriorityDispatchRuleAreaMapperManager priorityDispatchRuleAreaMapperManager;

    public Map<Long, Set<String>> getRuleIdKeyAreaMap(Collection<Long> priorityDispatchRuleIds) {
        List<PriorityDispatchRuleArea> ruleAreas = priorityDispatchRuleAreaMapperManager.lambdaQuery()
                .in(PriorityDispatchRuleArea::getPriorityDispatchRuleId, priorityDispatchRuleIds)
                .list();
        if (CollectionUtil.isEmpty(ruleAreas)) {
            return Maps.newHashMap();
        }
        Map<Long, Set<String>> ruleIdKeyAreaMap = Maps.newHashMap();
        ruleAreas.forEach(ruleArea -> {
            Set<String> areas = ruleIdKeyAreaMap.get(ruleArea.getPriorityDispatchRuleId());
            if (CollectionUtil.isEmpty(areas)) {
                areas = this.getMatchAreaList(ruleArea);
                ruleIdKeyAreaMap.put(ruleArea.getPriorityDispatchRuleId(), areas);
                return;
            }
            areas.addAll(this.getMatchAreaList(ruleArea));
        });
        return ruleIdKeyAreaMap;
    }

    private Set<String> getMatchAreaList(PriorityDispatchRuleArea ruleArea) {
        Set<String> list = Sets.newHashSet();
        if (ruleArea == null) {
            return list;
        }
        if (StringUtils.isNotBlank(ruleArea.getProvince())) {
            list.add(ruleArea.getProvince());
        }
        if (StringUtils.isNotBlank(ruleArea.getProvinceCodeMulti())) {
            String[] split = ruleArea.getProvinceCodeMulti().split(",");
            list.addAll(Lists.newArrayList(split));
        }
        return list;
    }

}
