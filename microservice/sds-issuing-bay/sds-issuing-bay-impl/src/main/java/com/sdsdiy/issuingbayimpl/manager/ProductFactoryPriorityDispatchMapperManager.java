package com.sdsdiy.issuingbayimpl.manager;

import com.sdsdiy.issuingbayimpl.entity.po.ProductFactoryPriorityDispatch;

import com.sdsdiy.issuingbayimpl.mapper.ProductFactoryPriorityDispatchMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 产品工厂优先派单配置(ProductFactoryPriorityDispatch)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductFactoryPriorityDispatchMapperManager extends ServiceImpl<ProductFactoryPriorityDispatchMapper, ProductFactoryPriorityDispatch> {

    @Resource
    private final ProductFactoryPriorityDispatchMapper productFactoryPriorityDispatchMapper;
}
