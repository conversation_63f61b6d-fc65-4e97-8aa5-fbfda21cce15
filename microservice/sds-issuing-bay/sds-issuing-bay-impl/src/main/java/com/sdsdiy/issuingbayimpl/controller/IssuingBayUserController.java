package com.sdsdiy.issuingbayimpl.controller;

import com.sdsdiy.issuingbayimpl.entity.po.IssuingBayUser;
import com.sdsdiy.issuingbayimpl.service.IssuingBayUserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 仓位创建信息(IssuingBayUser)表控制层
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@RestController
@RequestMapping("issuingBayUser")
public class IssuingBayUserController {
    /**
     * 服务对象
     */
    @Resource
    private IssuingBayUserService issuingBayUserService;

}