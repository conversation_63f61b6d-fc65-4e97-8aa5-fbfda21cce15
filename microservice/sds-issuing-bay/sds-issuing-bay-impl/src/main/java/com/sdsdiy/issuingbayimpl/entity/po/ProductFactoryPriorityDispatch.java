package com.sdsdiy.issuingbayimpl.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;

import java.io.Serializable;

/**
 * 产品工厂优先派单配置(ProductFactoryPriorityDispatch)实体类
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:03
 */
@Data
public class ProductFactoryPriorityDispatch extends BaseMapperPO implements Serializable {
    private static final long serialVersionUID = 517163382023148935L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 优先规则id
     */
    private Long priorityDispatchRuleId;

    /**
     * 工厂id
     */
    private Long factoryId;

    /**
     * 产品母体id
     */
    private Long productParentId;

    /**
     * 供应类型  ONE_PIECE:一件 SMALL_ORDER:小单
     */
    private String supplyChainType;
    
}
