package com.sdsdiy.issuingbayimpl.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;


/**
 * 发货区域(IssuingBayArea)实体类
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Data
public class IssuingBayArea  extends BaseMapperPO  implements Serializable {
    private static final long serialVersionUID = -87108624553245150L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * PLATFORM 平台 PRIVATE自发货
    */
    private String type;
    /**
    * 名称
    */
    private String name;
    /**
    * 状态1开启2关闭99删除
    */
    private Integer status;
    /**
    * 优先级
    */
    private Integer priority;
    /**
    * 创建时间
    */
    private Long createdTime;
    /**
    * 创建者用户id
    */
    private Long createdUserId;
    /**
    * 更新时间
    */
    private Long updatedTime;
    /**
    * 更新者用户id
    */
    private Long updatedUserId;
    /**
    * 租户id
    */
    private Long tenantId;

    private Integer isOpenIcon;
    private String  address;
    private String  iconUrl;

    private Integer isOpenToOtherTenant;
}