package com.sdsdiy.issuingbayimpl.service;

import com.sdsdiy.issuingbayimpl.entity.po.IssuingBayUser;

import com.sdsdiy.issuingbayimpl.manager.IssuingBayUserMapperManager;

import lombok.extern.log4j.Log4j2;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * 仓位创建信息(IssuingBayUser)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Log4j2
@Service
public class IssuingBayUserService {
    @Resource
    private IssuingBayUserMapperManager issuingBayUserMapperManager;
}