package com.sdsdiy.issuingbayimpl.entity.po;

import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;
import java.io.Serializable;


/**
 * 仓位创建信息(IssuingBayUser)实体类
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Data
public class IssuingBayUser extends BaseMapperPO implements Serializable {
    private static final long serialVersionUID = 513889494324807148L;
    
    private Long id;
    /**
    * system_role的id
    */
    private Long roleId;
    /**
    * 1平台2自发货
    */
    private Integer productionType;
    /**
    * 发货地区id
    */
    private Long bayAreaId;
    /**
    * issuing_bay对应的id
    */
    private Long bayId;
    /**
    * 创建人id
    */
    private Long createdUserId;
    /**
    * 创建时间
    */
    private Long createdTime;
}