package com.sdsdiy.issuingbayimpl.feign.user;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.userapi.api.tenant.TenantSysUserApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/12/13
 */
@FeignClient(name = "service-user", contextId = "TenantSysUserFeign", url = MicroServiceEndpointConstant.SERVICE_USER)
public interface TenantSysUserFeign extends TenantSysUserApi {
}

