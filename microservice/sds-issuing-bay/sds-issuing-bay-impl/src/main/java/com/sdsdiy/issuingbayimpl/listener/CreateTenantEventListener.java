package com.sdsdiy.issuingbayimpl.listener;

import cn.hutool.json.JSONUtil;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.MqListenerRegisterCondition;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.user.UserConsumerConst;
import com.sdsdiy.core.mq.queue.user.UserTagConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.sdsdiy.issuingbayimpl.service.IssuingBayAreaService;
import com.sdsdiy.userapi.constant.tenant.mq.TenantMqConstant;
import com.sdsdiy.userdata.dto.tenant.TenantCreateMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;

import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;


/**
 * 功能描述: 创建租户监听
 *
 * @Author: lin_bin
 * @Date: 2020/9/2 22:19
 */
@Service
@Slf4j
@Conditional(MqListenerRegisterCondition.class)
@RocketMQMessageListener(topic = RocketMqTopicConst.EVENT_USER,
        consumerGroup = UserConsumerConst.GID_CONSUMER_ISSUING_BAY_CREATE_TENANT,
        tag = UserTagConst.TAG_TENANT_CREATE)
public class CreateTenantEventListener implements RocketMQListener {
    @Resource
    private IssuingBayAreaService issuingBayAreaService;
    @Override
    public void consumeMsg(MessageView messageView) {
        TenantCreateMessage dto = RocketMQUtil.getBodyBean(messageView, TenantCreateMessage.class);
        onMessage(dto);

    }


    @LogTraceId
    public void onMessage(TenantCreateMessage message) {
        log.info("receive create tenant message tenantId：{} begin", message.getTenantId());
        issuingBayAreaService.init(message.getTenantId());
        log.info("receive create tenant message tenantId：{} end ", message.getTenantId());
    }

    //@KafkaListener(topics = TenantMqConstant.MQ_TENANT_TOPIC, groupId = "GID_CONSUMER_ISSUING_BAY_CREATE_TENANT")
    public void onMessage(ConsumerRecord<String, String> record, Acknowledgment ack, @Header(KafkaHeaders.RECEIVED_TOPIC) String topic) {
        if (TenantMqConstant.TAG_TENANT_CREATE.equals(record.key())) {
            Optional<String> message = Optional.ofNullable(record.value());
            if (message.isPresent()) {
                onMessage(JSONUtil.toBean(message.get(), TenantCreateMessage.class));
                ack.acknowledge();
            }
        } else {
            ack.acknowledge();
        }
    }
}
