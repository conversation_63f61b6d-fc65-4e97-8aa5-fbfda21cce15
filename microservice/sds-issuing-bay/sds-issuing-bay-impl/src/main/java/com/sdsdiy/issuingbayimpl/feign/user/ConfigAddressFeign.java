package com.sdsdiy.issuingbayimpl.feign.user;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.userapi.api.config.ConfigAddressApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/7/29
 */
@FeignClient(name = "service-user", contextId = "ConfigAddressFeign", url = MicroServiceEndpointConstant.SERVICE_USER)
public interface ConfigAddressFeign extends ConfigAddressApi {
}
