package com.sdsdiy.issuingbayimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBayUser;

import com.sdsdiy.issuingbayimpl.mapper.IssuingBayUserMapper;

import lombok.extern.log4j.Log4j2;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 仓位创建信息(IssuingBayUser)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Log4j2
@Service
@DS("common")
public class IssuingBayUserMapperManager extends ServiceImpl<IssuingBayUserMapper, IssuingBayUser> {

    @Resource
    private IssuingBayUserMapper issuingBayUserMapper;
}