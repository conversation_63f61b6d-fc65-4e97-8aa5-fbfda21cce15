package com.sdsdiy.issuingbayimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.issuingbayimpl.entity.RabbitMonitor;
import com.sdsdiy.issuingbayimpl.mapper.IssuingBayUserMapper;
import com.sdsdiy.issuingbayimpl.mapper.RabbitMonitorMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@DS("common")
@RequiredArgsConstructor
public class RabbitMonitorManager extends ServiceImpl<RabbitMonitorMapper, RabbitMonitor> {

    private final IssuingBayUserMapper issuingBayUserMapper;
}