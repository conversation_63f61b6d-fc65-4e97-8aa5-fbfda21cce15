package com.sdsdiy.issuingbayimpl.manager;

import com.sdsdiy.issuingbayimpl.entity.po.IssuingAreaFactory;

import com.sdsdiy.issuingbayimpl.mapper.IssuingAreaFactoryMapper;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 区域下的工厂(IssuingAreaFactory)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-02 11:13:18
 */
@Slf4j
@Service
public class IssuingAreaFactoryMapperManager extends ServiceImpl<IssuingAreaFactoryMapper, IssuingAreaFactory>{

    @Resource
    private IssuingAreaFactoryMapper issuingAreaFactoryMapper;
}