package com.sdsdiy.issuingbayimpl.config;


import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述: Swagger2API文档的配置
 *
 * @Author: linBin
 * @Date: 2020/5/27 13:32
 */
@Configuration
@EnableSwagger2
@EnableKnife4j
public class SwaggerConfig {
    @Value("${swagger.enable:true}")
    private boolean swaggerEnable;

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerEnable)
                .apiInfo(apiInfo())
                .globalOperationParameters(setHeaderToken())
                .select()
                .apis(RequestHandlerSelectors.withClassAnnotation(RestController.class))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("物流服务接口文档")
                .version("1.0")
                .build();
    }


    private List<Parameter> setHeaderToken() {
        List<Parameter> pars = new ArrayList<>();
        return pars;
    }

    public ParameterBuilder parameterBuilder(String keyName, String description) {
        return parameterBuilder(keyName, description, false);
    }

    public ParameterBuilder parameterBuilder(String keyName, String description, boolean required) {
        return new ParameterBuilder()
                .name(keyName)
                .description(description)
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(required);
    }


}
