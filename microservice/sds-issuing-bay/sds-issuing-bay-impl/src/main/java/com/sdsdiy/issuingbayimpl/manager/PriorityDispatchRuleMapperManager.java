package com.sdsdiy.issuingbayimpl.manager;

import com.sdsdiy.issuingbayimpl.entity.po.PriorityDispatchRule;

import com.sdsdiy.issuingbayimpl.mapper.PriorityDispatchRuleMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 优先派单规则表(PriorityDispatchRule)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PriorityDispatchRuleMapperManager extends ServiceImpl<PriorityDispatchRuleMapper, PriorityDispatchRule> {

    @Resource
    private final PriorityDispatchRuleMapper priorityDispatchRuleMapper;
}
