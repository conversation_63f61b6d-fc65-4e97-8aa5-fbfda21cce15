package com.sdsdiy.issuingbayimpl.service.aliexpress;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.enums.CountryEnum;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.coreconfig.config.EnvGlobalConfig;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.coreconfig.util.DingDingUtil;
import com.sdsdiy.issuingbaydata.dto.aliexpress.AliexpressJitBayDTO;
import com.sdsdiy.issuingbaydata.dto.aliexpress.AliexpressJitBaySaveDTO;
import com.sdsdiy.issuingbayimpl.entity.po.aliexpress.AliexpressJitBay;
import com.sdsdiy.issuingbayimpl.mapper.aliexpress.AliexpressJitBayMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 速卖通JIT仓 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024/04/22
 */
@Service
@Log4j2
public class AliexpressJitBayService extends BaseServiceImpl<AliexpressJitBayMapper, AliexpressJitBay> {
    /**
     * 新增或更新
     */
    public void saveDto(AliexpressJitBaySaveDTO dto) {
        AliexpressJitBay one = this.lambdaQuery().select(AliexpressJitBay::getId)
                .eq(AliexpressJitBay::getBayCode, dto.getBayCode()).one();
        if (one == null) {
            one = new AliexpressJitBay();
        }
        BeanUtil.copyProperties(dto, one);
        // 解析省市区
        simpleAddressParsing(one);
        if (one.getId() == null) {
            one.setCountry(CountryEnum.CN.getCode());
            this.save(one);
            if (StrUtil.isBlank(one.getProvinceCode())) {
                DingDingUtil.sendDDMessage(DingDingUtil.EXCEPTION_ROBOT_URL
                        , DingDingUtil.formatContent(one.getBayCode() + " " + one.getDetail(), "出现新的揽收仓")
                        , EnvGlobalConfig.isTestProfiles() ? Collections.emptyList() : Collections.singletonList("13625078224"), false);
            }
        } else {
            this.updateById(one);
        }
    }

    public static void simpleAddressParsing(AliexpressJitBay one) {
        // 很简单的处理
        String detail = one.getDetail();
        if (StrUtil.isBlank(detail)) {
            return;
        }
        if (detail.contains("东莞")) {
            one.setProvince("广东省")
                    .setProvinceCode("440000")
                    .setCity("东莞市")
                    .setCityCode("441900");
        } else if (detail.contains("杭州")) {
            one.setProvince("浙江省")
                    .setProvinceCode("330000")
                    .setCity("杭州市")
                    .setCityCode("330100");
        }
    }

    public List<AliexpressJitBayDTO> findJitBayCodeByIssuingBay(Long issuingBayId) {
        List<AliexpressJitBay> list = this.lambdaQuery()
                .like(AliexpressJitBay::getAvailableIssuingBay, StrUtil.COMMA + issuingBayId + StrUtil.COMMA)
                .list();
        return ListUtil.copyProperties(list, AliexpressJitBayDTO.class);
    }

    /**
     * 根据id获取
     *
     * @return dto
     */
    public AliexpressJitBayDTO findDtoByCode(String bayCode, String fields) {
        AliexpressJitBay one = this.lambdaQuery(fields, AliexpressJitBayDTO.class)
                .eq(AliexpressJitBay::getBayCode, bayCode).one();
        return RelationsBinder.convertAndBind(one, AliexpressJitBayDTO.class, fields);
    }

    public List<AliexpressJitBayDTO> findDtoByCodes(BaseListQueryDTO<String> reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getList())) {
            return Collections.emptyList();
        }
        List<AliexpressJitBay> list = this.lambdaQuery(reqDTO.getFields(), AliexpressJitBayDTO.class)
                .in(AliexpressJitBay::getBayCode, reqDTO.getList()).list();
        return RelationsBinder.convertAndBind(list, AliexpressJitBayDTO.class, reqDTO.getFields());
    }

    public List<AliexpressJitBay> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(AliexpressJitBay::getId, ids).list();
    }

    public List<AliexpressJitBayDTO> findByName(String bayName, String fields) {
        if (StrUtil.isBlank(bayName)) {
            return Collections.emptyList();
        }
        List<AliexpressJitBay> list = this.lambdaQuery().like(AliexpressJitBay::getBayName, bayName.trim()).list();
        return RelationsBinder.convertAndBind(list, AliexpressJitBayDTO.class, fields);
    }

    /**
     * 分页查询
     */
    public PageResultDto<AliexpressJitBayDTO> getPage(BasePageSelect pageSelect) {
        Page<AliexpressJitBay> page = this.lambdaQuery().orderByDesc(AliexpressJitBay::getId)
                .page(new Page<>(pageSelect.getPage(), pageSelect.getSize()));
        return new PageResultDto<>(page.getTotal(),
                ListUtil.copyProperties(page.getRecords(), AliexpressJitBayDTO.class));
    }

    public List<AliexpressJitBayDTO> findByCodes(Collection<String> bayCodes) {
        if (CollUtil.isEmpty(bayCodes)) {
            return Collections.emptyList();
        }
        List<AliexpressJitBay> list = this.lambdaQuery().in(AliexpressJitBay::getBayCode, bayCodes).list();
        return ListUtil.copyProperties(list, AliexpressJitBayDTO.class);
    }

    public List<AliexpressJitBayDTO> all() {
        // 最多10000条 这边数据没那么多
        List<AliexpressJitBay> list = this.lambdaQuery().last("limit 10000")
                .list();
        return ListUtil.copyProperties(list, AliexpressJitBayDTO.class);
    }
}
