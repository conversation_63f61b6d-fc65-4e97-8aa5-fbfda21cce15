package com.sdsdiy.issuingbayimpl.entity.po;

import com.sdsdiy.common.base.entity.dto.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 优先派单规则表(PriorityDispatchRule)实体类
 *
 * <AUTHOR>
 * @since 2024-06-12 20:04:02
 */
@Data
public class PriorityDispatchRule extends BasePO implements Serializable {
    private static final long serialVersionUID = -13360318931519591L;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 规则名称
     */
    private String name;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 国家简码
     */
    private String countryCode;

    /**
     * AREA=区域地址优先
     */
    private String type;
}
