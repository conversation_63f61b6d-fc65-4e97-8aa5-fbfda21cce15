package com.sdsdiy.issuingbayimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.base.util.BeanTool;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.issuingbayapi.dto.area.IssuingBayAreaSaveParam;
import com.sdsdiy.issuingbayapi.dto.area.IssuingBayAreaUpdateParam;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto;
import com.sdsdiy.issuingbayimpl.entity.po.IssuingBayArea;
import com.sdsdiy.issuingbayimpl.feign.TenantLogisticsFeign;
import com.sdsdiy.issuingbayimpl.manager.IssuingBayAreaMapperManager;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFBASaveParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 发货区域(IssuingBayArea)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-26 12:02:12
 */
@Log4j2
@Service
@DS("common")
public class IssuingBayAreaService {
    @Resource
    private IssuingBayAreaMapperManager issuingBayAreaMapperManager;
    @Resource
    private IssuingBayService issuingBayService;
    @Resource
    private TenantLogisticsFeign tenantLogisticsFeign;

    public List<BaseIdAndNameDTO> findIdNameByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<IssuingBayArea> list = issuingBayAreaMapperManager.lambdaQuery()
                .select(IssuingBayArea::getId, IssuingBayArea::getName)
                .in(IssuingBayArea::getId, ids).list();
        return ListUtil.copyProperties(list, BaseIdAndNameDTO.class);
    }

    public List<IssuingBayAreaRespDto> findByIds(List<Long> ids, String fields) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<IssuingBayArea> issuingBays = issuingBayAreaMapperManager.lambdaQuery(fields, IssuingBayAreaRespDto.class)
                .in(IssuingBayArea::getId, ids).list();
        return RelationsBinder.convertAndBind(issuingBays, IssuingBayAreaRespDto.class, fields);
    }

    public IssuingBayAreaRespDto findById(Long id, String fields) {
        IssuingBayArea area = issuingBayAreaMapperManager.getById(id);
        return RelationsBinder.convertAndBind(area, IssuingBayAreaRespDto.class, fields);
    }

    public List<IssuingBayArea> listAll(Long tenantId, Integer status) {
        ArrayList<Integer> statusList = Lists.newArrayList(CommonStatus.ONLINE.getStatus(), CommonStatus.OFFLINE.getStatus());
        if (status != null) {
            statusList = Lists.newArrayList(status);
        }
        return issuingBayAreaMapperManager.lambdaQuery()
                .eq(IssuingBayArea::getTenantId, tenantId)
                .in(IssuingBayArea::getStatus, statusList)
                .list();
    }

    public void save(IssuingBayAreaSaveParam param) {
        IssuingBayArea issuingBayArea = BeanTool.copyBean(param, IssuingBayArea.class);
        issuingBayArea.setCreatedTime(System.currentTimeMillis());
        issuingBayArea.setUpdatedTime(System.currentTimeMillis());
        issuingBayArea.setUpdatedUserId(param.getCreatedUserId());
        issuingBayArea.setStatus(CommonStatus.OFFLINE.getStatus());
        issuingBayAreaMapperManager.save(issuingBayArea);
        TenantLogisticsFBASaveParam fbaSaveParam = new TenantLogisticsFBASaveParam();
        fbaSaveParam.setName("自提");
        fbaSaveParam.setIssuingBayAreaId(issuingBayArea.getId());
        if (TenantCommonConstant.SDSDIY_TENANT_ID.equals(param.getTenantId())) {
            tenantLogisticsFeign.saveFbaZtOne(param.getTenantId(), fbaSaveParam);
        }
        tenantLogisticsFeign.saveFbmZtOne(param.getTenantId(), fbaSaveParam);
    }

    public void update(Long id, IssuingBayAreaUpdateParam param) {
        IssuingBayArea issuingBayArea = BeanTool.copyBean(param, IssuingBayArea.class);
        issuingBayArea.setUpdatedTime(System.currentTimeMillis());
        issuingBayArea.setId(id);
        issuingBayAreaMapperManager.updateById(issuingBayArea);
    }

    @Transactional(rollbackFor = Exception.class)
    public void init(Long tenantId) {
        IssuingBayArea issuingBayArea = new IssuingBayArea();
        issuingBayArea.setName("平台仓（区域）");
        issuingBayArea.setStatus(1);
        issuingBayArea.setPriority(1);
        issuingBayArea.setTenantId(tenantId);
        issuingBayArea.setCreatedTime(System.currentTimeMillis());
        issuingBayArea.setUpdatedTime(System.currentTimeMillis());
        issuingBayAreaMapperManager.save(issuingBayArea);
        issuingBayService.init(tenantId, issuingBayArea.getId());
        TenantLogisticsFBASaveParam param = new TenantLogisticsFBASaveParam();
        param.setName("自提");
        param.setIssuingBayAreaId(issuingBayArea.getId());
        tenantLogisticsFeign.saveFbaZtOne(tenantId, param);
        tenantLogisticsFeign.saveFbmZtOne(tenantId, param);
    }

    public List<IssuingBayAreaRespDto> findListByTenantId(Long tenantId) {
        List<IssuingBayArea> list = issuingBayAreaMapperManager.lambdaQuery()
                .eq(IssuingBayArea::getTenantId, tenantId)
                .eq(IssuingBayArea::getStatus, CommonStatus.ONLINE.getStatus())
                .list();
        return BeanUtil.copyToList(list, IssuingBayAreaRespDto.class);
    }
}