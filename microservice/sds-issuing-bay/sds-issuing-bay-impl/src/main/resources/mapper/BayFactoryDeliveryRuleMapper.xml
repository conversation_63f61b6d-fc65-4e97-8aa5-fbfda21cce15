<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.issuingbayimpl.mapper.BayFactoryDeliveryRuleMapper">

    <resultMap type="com.sdsdiy.issuingbayimpl.entity.po.BayFactoryDeliveryRule" id="BayFactoryDeliveryRuleMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="factoryId" column="factory_id" jdbcType="INTEGER"/>
        <result property="issuingBayId" column="issuing_bay_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="merchantCondition" column="merchant_condition" jdbcType="VARCHAR"/>
        <result property="orderProductCondition" column="order_product_condition" jdbcType="VARCHAR"/>
        <result property="addressCondition" column="address_condition" jdbcType="VARCHAR"/>
        <result property="addressType" column="address_type" jdbcType="VARCHAR"/>
        <result property="priority" column="priority" jdbcType="INTEGER"/>
    </resultMap>


</mapper>