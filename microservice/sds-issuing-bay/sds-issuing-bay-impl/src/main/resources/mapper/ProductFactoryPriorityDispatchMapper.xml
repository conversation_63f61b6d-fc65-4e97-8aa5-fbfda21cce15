<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.issuingbayimpl.mapper.ProductFactoryPriorityDispatchMapper">

    <resultMap type="com.sdsdiy.issuingbayimpl.entity.po.ProductFactoryPriorityDispatch"
               id="ProductFactoryPriorityDispatchMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="priorityDispatchRuleId" column="priority_dispatch_rule_id" jdbcType="INTEGER"/>
        <result property="factoryId" column="factory_id" jdbcType="INTEGER"/>
        <result property="productParentId" column="product_parent_id" jdbcType="INTEGER"/>
        <result property="supplyChainType" column="supply_chain_type" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>
