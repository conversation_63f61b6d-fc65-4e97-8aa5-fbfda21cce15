package com.sdsdiy.logisticsdata.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

/**
 * @author: bin_lin
 * @date: 2024/11/12 18:27
 * @desc:
 */
@Data
public class LogisticsExpensesEstimatedParam {
    @ApiModelProperty("物流集合")
    private Set<Long> logisticIds;
    @ApiModelProperty("店铺上的sellerId")
    private String sellerId;
    @ApiModelProperty("订单id")
    private Long orderId;
    @ApiModelProperty("店铺id")
    private Long merchantStoreId;
    @ApiModelProperty("发货仓id")
    private Long issuingBayId;
    @ApiModelProperty("第三方单号")
    private String parentOrderNo;
    @ApiModelProperty("产品长度")
    private BigDecimal length;
    @ApiModelProperty("产品宽度")
    private BigDecimal width;
    @ApiModelProperty("产品高度")
    private BigDecimal height;
    @ApiModelProperty("重量")
    private BigDecimal weight;
}
