package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 物流跟踪表(CarriageTrackInfoRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-08-24 22:02:55
 */
@Data
public class CarriageTrackInfoRespDto implements Serializable {

    private Long id;
    @ApiModelProperty(value = "订单id")
    private Long orderId;
    @ApiModelProperty(value = "对应的物流渠道")
    private Long logisticsId;
    @ApiModelProperty(value = "运单id")
    private Long carriageNoRecodeId;
    @ApiModelProperty(value = "事件编号")
    private String eventNo;
    @ApiModelProperty(value = "运单编号")
    private String carriageNo;
    @ApiModelProperty(value = "跟踪信息")
    private String trackInfo;
    @ApiModelProperty(value = "是否发货 1.是 0否")
    private Integer isDelivered;
    @ApiModelProperty(value = "运单是否正常1.是0.否")
    private Integer isHealth;

}