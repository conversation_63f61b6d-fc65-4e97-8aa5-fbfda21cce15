package com.sdsdiy.logisticsdata.dto.temu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemuFullyWarehouseCountryAddressRespDTO {
    

    private Long id;


    private Long areaCode;


    private String name;


    private String firstLetter;

    private String fullName;
    
    private Long parentAreaCode;
    
    private Integer areaType;
    
    private List<TemuFullyWarehouseCountryAddressRespDTO> children;
}
