package com.sdsdiy.logisticsdata.dto.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: bin_lin
 * @date: 2024/11/20 19:06
 * @desc:
 */
@Data
public class TemuUsableShippingServiceRangeResp {
    @ApiModelProperty("状态信息 预估状态 PROCESSING=进行中,COMPLETED=完成,NO_COMPLETED=无法预估，物流可选择,FAILED=预估结果失败，物流无法使用")
    private String status;
    @ApiModelProperty("报错信息")
    private String errorMsg;
    @ApiModelProperty("可用物流服务列表")
    private List<TemuUsableShippingServiceDTO> usableShippingServiceList;

    public TemuUsableShippingServiceRangeResp() {
        usableShippingServiceList = new ArrayList<>();
    }
}
