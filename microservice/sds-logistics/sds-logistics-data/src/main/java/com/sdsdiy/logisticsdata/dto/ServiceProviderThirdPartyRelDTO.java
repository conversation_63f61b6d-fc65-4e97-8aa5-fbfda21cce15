package com.sdsdiy.logisticsdata.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.logisticsdata.enums.ServiceProviderThirdPartyEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ServiceProviderThirdPartyRelDTO {

    private Long serviceProviderId;

    /**
     * @see ServiceProviderThirdPartyEnum
     */
    private String mapType;

    private String thirdPartyLogisticsId;

    private String thirdPartyLogisticsName;
}
