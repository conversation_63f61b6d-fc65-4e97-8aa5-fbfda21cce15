package com.sdsdiy.logisticsdata.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class AliexpressAddressVO {
    @JsonProperty("id")
    private Long id;
    @JsonProperty("mobile")
    @NotBlank
    private String mobile;
    @JsonProperty("province")
    private String province;
    @JsonProperty("name")
    private String name;
    @JsonProperty("county")
    private String county;
    @JsonProperty("email")
    @NotBlank
    private String email;
    @JsonProperty("city")
    private String city;
    @JsonProperty("country")
    private String country;
    @JsonProperty("address_id")
    private Long addressId;
    @JsonProperty("postcode")
    private String postcode;
    @JsonProperty("member_type")
    private String memberType;
    @JsonProperty("street")
    @NotBlank
    private String street;
    @JsonProperty("fax")
    private String fax;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("street_address")
    private String streetAddress;
}