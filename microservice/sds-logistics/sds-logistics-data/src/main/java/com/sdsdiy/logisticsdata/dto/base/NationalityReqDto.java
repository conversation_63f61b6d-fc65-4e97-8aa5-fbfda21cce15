package com.sdsdiy.logisticsdata.dto.base;

import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * (NationalityReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-15 10:32:31
 */
@Data
public class NationalityReqDto implements Serializable {
    
 
        private Integer id;
     
        @NotNull(message = "国家（地区）英文名不能为空")
        @ApiModelProperty(value = "国家（地区）英文名")
        private String englishName;
     
        @NotNull(message = "国家（地区）名不能为空")
        @ApiModelProperty(value = "国家（地区）名")
        private String name;
     
        @NotNull(message = "英文缩写不能为空")
        @ApiModelProperty(value = "英文缩写")
        private String abbreviation;
     
        @NotNull(message = "电话区号不能为空")
        @ApiModelProperty(value = "电话区号")
        private Integer code;
     
        @NotNull(message = "排序不能为空")
        @ApiModelProperty(value = "排序")
        private Integer sort;
     
        @NotNull(message = "1不显示2显示具体的省市区不能为空")
        @ApiModelProperty(value = "1不显示2显示具体的省市区")
        private Integer showCountryDetail;
    
}