package com.sdsdiy.logisticsdata.enums;

/**
 * <AUTHOR>
 * @date 2021/8/16
 */
public enum IssuingBayType {
    //
    PLATFORM("PLATFORM",1, "平台"),
    PRIVATE("PRIVATE", 2,"自发货"),
    ;
    public final String type;
    public final Integer code;
    public final String desc;

    IssuingBayType(String type,Integer code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }
}
