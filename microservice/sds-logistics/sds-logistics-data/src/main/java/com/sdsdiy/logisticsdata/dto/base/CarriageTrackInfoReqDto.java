package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * 物流跟踪表(CarriageTrackInfoReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-08-24 22:02:55
 */
@Data
public class CarriageTrackInfoReqDto implements Serializable {


    private Long id;

    @NotNull(message = "订单id不能为空")
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @NotNull(message = "对应的物流渠道不能为空")
    @ApiModelProperty(value = "对应的物流渠道")
    private Long logisticsId;

    @NotNull(message = "运单id不能为空")
    @ApiModelProperty(value = "运单id")
    private Long carriageNoRecodeId;

    @NotNull(message = "事件编号不能为空")
    @ApiModelProperty(value = "事件编号")
    private String eventNo;

    @NotNull(message = "运单编号不能为空")
    @ApiModelProperty(value = "运单编号")
    private String carriageNo;

    @NotNull(message = "跟踪信息不能为空")
    @ApiModelProperty(value = "跟踪信息")
    private String trackInfo;

    @NotNull(message = "是否发货 1.是 0否不能为空")
    @ApiModelProperty(value = "是否发货 1.是 0否")
    private Integer isDelivered;

    @NotNull(message = "运单是否正常1.是0.否不能为空")
    @ApiModelProperty(value = "运单是否正常1.是0.否")
    private Integer isHealth;

    @NotNull(message = "是否删除  0 否 1是不能为空")
    @ApiModelProperty(value = "是否删除  0 否 1是")
    private Integer isDelete;

    @NotNull(message = "创建者id不能为空")
    @ApiModelProperty(value = "创建者id")
    private Long createUid;

    @NotNull(message = "更新者id不能为空")
    @ApiModelProperty(value = "更新者id")
    private Long updateUid;

    @NotNull(message = "创建时间不能为空")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @NotNull(message = "更新时间不能为空")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}