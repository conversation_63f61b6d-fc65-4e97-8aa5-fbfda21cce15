package com.sdsdiy.logisticsdata.param.temu;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class TemuUsableShippingServiceGetParam {
    private Long orderId;
    private Long merchantStoreId;
    private String sellerId;
    private Long issuingBayId;
    private String parentOrderNo;
    private BigDecimal length;
    private BigDecimal width;
    private BigDecimal height;
    private BigDecimal weight;
}