package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;


/**
 * 订单收货地址记录(AddressReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-06 16:36:23
 */
@Data
public class ShipAmountDto implements Serializable {
        private Double shipAmount;
        private Double serviceAmount;
        private Double originShipAmount;
        @ApiModelProperty("清关费用")
        private BigDecimal clearanceFee = BigDecimal.ZERO;
        @ApiModelProperty("关税费率")
        private BigDecimal tariffRate = BigDecimal.ZERO;
        private BigDecimal tenantCarriageCommissionRate;
        @ApiModelProperty("偏远费用")
        private BigDecimal carriageFarawayAmount = BigDecimal.ZERO;
        @ApiModelProperty("偏远类型")
        private String carriageFarawayType = "NONE";
        @ApiModelProperty("产品id和关税费率Map")
        private Map<Long, BigDecimal> productIdKeyAndTariffRateMap;
}