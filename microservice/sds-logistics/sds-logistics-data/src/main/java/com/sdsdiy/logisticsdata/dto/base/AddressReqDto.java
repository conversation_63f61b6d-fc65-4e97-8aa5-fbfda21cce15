package com.sdsdiy.logisticsdata.dto.base;

import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * 订单收货地址记录(AddressReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-06 16:36:23
 */
@Data
public class AddressReqDto implements Serializable {
    
 
        private Long id;
     
        private Long userId;
     
        @NotNull(message = "商户id不能为空")
        @ApiModelProperty(value = "商户id")
        private Long merchantId;
     
        @NotNull(message = "收件人不能为空")
        @ApiModelProperty(value = "收件人")
        private String consignee;
     
        @NotNull(message = "国家不能为空")
        @ApiModelProperty(value = "国家")
        private String country;
     
        @NotNull(message = "省不能为空")
        @ApiModelProperty(value = "省")
        private String province;
     
        @NotNull(message = "省ID不能为空")
        @ApiModelProperty(value = "省ID")
        private String provinceCode;
     
        @NotNull(message = "市不能为空")
        @ApiModelProperty(value = "市")
        private String city;
     
        @NotNull(message = "市ID不能为空")
        @ApiModelProperty(value = "市ID")
        private String cityCode;
     
        @NotNull(message = "固定电话不能为空")
        @ApiModelProperty(value = "固定电话")
        private String cellphone;
     
        @NotNull(message = "移动电话不能为空")
        @ApiModelProperty(value = "移动电话")
        private String mobilePhone;
     
        @NotNull(message = "详细地址不能为空")
        @ApiModelProperty(value = "详细地址")
        private String detail;
     
        @NotNull(message = "邮政编码不能为空")
        @ApiModelProperty(value = "邮政编码")
        private String postcode;
     
        private Long createTime;
     
        private String md5;
    
}