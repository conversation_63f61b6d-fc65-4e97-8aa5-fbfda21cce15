package com.sdsdiy.logisticsdata.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <p>
 * temu的在线下单包裹记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-23
 */
@Data
@NoArgsConstructor
public class TemuShipmentRecordInfoDto {
    private Long id;

    @ApiModelProperty(value = "temu外部订单号")
    private String outOrderNo;

    @ApiModelProperty("我们的物流id")
    private Long logisticsId;

    @ApiModelProperty("temu 仓库id")
    private String warehouseId;

    @ApiModelProperty(value = "temu 渠道id")
    private Long channelId;

    @ApiModelProperty(value = "temu 物流公司id")
    private Long shipCompanyId;

    @ApiModelProperty(value = "temu返回包裹号")
    private String packageSn;

    @ApiModelProperty(value = "0申请中 1成功 2失败")
    private Integer shippingLabelStatus;

    @ApiModelProperty(value = "面单地址")
    private String shippingLabelUrl;

    @ApiModelProperty(value = "运单号")
    private String trackingNumber;

    private List<TemuShipmentRecordItemRelDto> shipmentRecordItemRelDtoList;
}
