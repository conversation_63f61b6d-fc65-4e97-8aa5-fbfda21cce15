package com.sdsdiy.logisticsdata.dto;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import com.sdsdiy.logisticsdata.dto.base.CountryExpressInfoAreaRespDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/13
 */
@Data
public class LogisticsSyncDTO {

    private ServiceProviderDTO serviceProvider;
    private List<ServiceProviderParamDTO> serviceProviderParamList;
    private List<TenantServiceProviderDTO> tenantServiceProviderList;
    private List<TenantServiceProviderAccountDTO> tenantServiceProviderAccountList;
    private List<TenantAccountParamDTO> tenantAccountParamList;
    private List<TenantAddressDTO> tenantAddressList;
    private List<LogisticsChannelDTO> logisticsChannelList;
    private List<TenantLogisticsDTO> tenantLogisticsList;
    private List<CountryExpressInfoDTO> countryExpressInfoList;
    private List<CountryExpressInfoAreaRespDto> countryExpressInfoAreaList;

    @ToString
    @Getter
    @Setter
    public static class TenantServiceProviderAccountDTO {
        private Long id;
        private String name;
        private Long serviceProviderId;
        private Long tenantServiceProviderId;
        private Long tenantId;
        private Long tenantAddressId;
        private String type;
        private String deliveryMethod;
        private String status;
        private Date createTime;
        private Date updateTime;
        private Integer isDelete;
        private Long createUid;
        private Long updateUid;
        private Integer isAuth;
    }

    @ToString
    @Getter
    @Setter
    public static class CountryExpressInfoDTO {
        private Long id;
        private String name;
        private Integer limitPriority;
        private Integer firstPriority;
        private Integer prescriptionStart;
        private Integer prescriptionEnd;
        private Integer prescriptionType;
        private Double handlePrice;
        private String excludeArea;
        private String remark;
        private Integer status;
        private Integer type;
        private Long logisticsId;
        private String price;
        private Integer areaType;
        private Double firstPriorityCost;
        private Integer minusFirstPriority;
        private Integer ruleWeight;
        private Double ruleWeightRound;
        private String currencySymbol;
        private String chargeType;
        private BigDecimal perUnitCost;
        private Integer isOpenFarawayArea;
        private String farawayAreaResource;
        private String farawayAreaType;
        private BigDecimal farawayAreaValue;
        private String farawayAreaWeightType;
        private Integer farawayAreaWeight;
        private Integer minWeightLimit;
    }

    @ToString
    @Getter
    @Setter
    public static class TenantLogisticsDTO {
        private Long id;
        private Long tenantId;
        private Long logisticsChannelId;
        private Long serviceProviderId;
        private Long issuingBayAreaId;
        private String name;
        private String serviceProviderName;
        private String channelType;
        private BigDecimal discount;
        private String remark;
        private Integer status;
        private BigDecimal packingCharges;
        private String codeId;
        private Integer expressStatus;
        private Integer bulkOnOff;
        private BigDecimal bulkRate;
        private Integer allProductStatus;
        private String applyNode;
        private String trackingMode;
        private String courierCode;
        private BigDecimal serviceAmount;
        private BigDecimal fixedServiceAmount;
        private Integer customUploadLable;
        private BigDecimal fbaLabelAmount;
        private Integer refundPercent;
        private Integer refundResetNum;
        private BigDecimal bulkBase;
    }

    @ToString
    @Getter
    @Setter
    public static class LogisticsChannelDTO {
        private Long id;
        private Long serviceProviderId;
        private String name;
        private String code;
        private Integer type;
        private String standardName;
        private String methodName;
        private String aliexpressCode;
        private String warehouseCarrierService;
        private Integer isPublic;
        private String isOther;
        private String iossType;
        private Integer isCollectVatAble;
        private Integer isButtJoint;
        private Integer isTrackInfo;
        private String tracking51CourierCode;
        private String tracking17CourierCode;
    }

    @ToString
    @Getter
    @Setter
    public static class TenantAddressDTO {
        private Long id;
        private Long tenantId;
        private String company;
        private String name;
        private String telephone;
        private String mobile;
        private String email;
        private String country;
        private String province;
        private String city;
        private String address;
        private String extraAddress;
        private String postCode;
        private String provinceCode;
        private String cityCode;
    }

    @ToString
    @Getter
    @Setter
    public static class TenantAccountParamDTO {
        private Long id;
        private Long tenantId;
        private Long tenantServiceProviderAccountId;
        private Long serviceProviderParamId;
        private String paramKey;
        private String paramValue;
    }

    @ToString
    @Getter
    @Setter
    public static class ServiceProviderParamDTO {
        private Long id;
        private Long serviceProviderId;
        private String name;
        private String paramKey;
        private String remark;
        private String textboxType;
        private String optionalValue;
        private String isRequire;
        private Integer priority;
        private String defaultValue;
    }


    @ToString
    @Getter
    @Setter
    public static class ServiceProviderDTO {
        private Long id;
        private String name;
        private Integer status;
        private String type;
        private Integer isPublic;
        private String remark;
        private String link;
        private String addressLanguage;
        private String addressType;
        private Integer isOfficialFarawayArea;
    }

    @ToString
    @Getter
    @Setter
    public static class TenantServiceProviderDTO {
        private Long id;
        private Long serviceProviderId;
        private Long tenantId;
    }

}
