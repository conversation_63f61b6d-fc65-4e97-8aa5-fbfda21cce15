package com.sdsdiy.logisticsdata.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 * 手动上传面单记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ManualUploadLabelRecordParam", description = "手动上传面单记录Param")
public class ManualUploadLabelRecordParam {


    @ApiModelProperty(value = "order_id")
    private Long orderId;

    @ApiModelProperty(value = "包裹id")
    private Long parcelId;

    @ApiModelProperty(value = "运单号")
    private String carriageNo;
}
