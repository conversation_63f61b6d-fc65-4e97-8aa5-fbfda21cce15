package com.sdsdiy.logisticsdata.param.temu;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemuFullyWarehouseCountryAddressTownParam {
    @ApiModelProperty(value = "请求地址",hidden = true)
    private String url;
    @ApiModelProperty(value = "cookie",hidden = true)
    private String cookie;
    @ApiModelProperty(value = "商户号",hidden = true)
    private String mallId;

    @ApiModelProperty(value = "省编码")
    private Long provinceCode;
    @ApiModelProperty(value = "市编码")
    private Long cityCode;
    @ApiModelProperty(value = "区编码")
    private Long districtCode;
}
