package com.sdsdiy.logisticsdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/7 18:00)
 * 物流尾程信息
 */
@Data
public class LogisticsTailOnlineEstimatedByOrderReq {
    //    @ApiModelProperty("物流渠道id")
//    private List<Long> tailLogisticsChannelIds;
    @ApiModelProperty("渠道codeIds")
    private List<String> tailChannelCodeIds;
    @ApiModelProperty("物流id")
    private Long logisticsId;
    @ApiModelProperty("是否首次下单")
    private Integer isFirstTime;
}
