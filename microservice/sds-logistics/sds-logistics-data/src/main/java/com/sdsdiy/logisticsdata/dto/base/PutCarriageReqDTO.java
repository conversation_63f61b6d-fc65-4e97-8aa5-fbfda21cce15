package com.sdsdiy.logisticsdata.dto.base;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.logisticsdata.dto.base
 * @Author: lujp
 * @CreateTime: 2023-09-27
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class PutCarriageReqDTO {
    /**
     * 这里是运单号
     */
    private String orderNo;
    private Long logisticsId;
    private String name;
    private String labelPdf;
    /**
     * 租户要付的物流费
     */
    private BigDecimal logisticsFee;
    private BigDecimal logisticsServiceFee;
    private BigDecimal orderQuota;
}