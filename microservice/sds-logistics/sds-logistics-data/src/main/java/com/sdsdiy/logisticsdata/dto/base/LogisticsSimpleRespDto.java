package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 物流信息表(LogisticsRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-06-28 10:10:38
 */
@Data
public class LogisticsSimpleRespDto implements Serializable {

    private Long id;
    @ApiModelProperty(value = "渠道名")
    private String name;
    @ApiModelProperty(value = "产品代码")
    private String codeId;
}