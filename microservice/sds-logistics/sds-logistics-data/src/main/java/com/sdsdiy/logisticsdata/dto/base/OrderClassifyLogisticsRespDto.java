package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 物流信息表(LogisticsRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-06-28 10:10:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderClassifyLogisticsRespDto implements Serializable {

    private Long id;
    @ApiModelProperty(value = "物流渠道名称")
    private String name;
    @ApiModelProperty(value = "组集合")
    private List<Long> groupIds;
}