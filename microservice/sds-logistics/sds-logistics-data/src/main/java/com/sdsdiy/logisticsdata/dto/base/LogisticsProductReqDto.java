package com.sdsdiy.logisticsdata.dto.base;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
 * 物流产品关联表(LogisticsProductReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-06-28 10:10:38
 */
@Data
public class LogisticsProductReqDto implements Serializable {

    private Long id;

    private Long logisticId;

    private Long productId;

    private Long productParentId;

    @NotNull(message = "1关联2取消关联不能为空")
    @ApiModelProperty(value = "1关联2取消关联")
    private Integer status;


}