package com.sdsdiy.logisticsdata.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class LogisticsExpensesEstimatedBaseParam {
    private Long productTenantId;
    //商户店铺Id
    private Long merchantStoreId;
    //店铺sellerId
    private String sellerId;
    //第三方单号
    private String parentOrderNo;
    //可发货订单号
    private List<String> canSendOrderSnList;
    //订单中产品最长边 厘米
    private BigDecimal length;
    //订单中产品最长宽 厘米
    private BigDecimal width;
    //订单中产品最长高 厘米
    private BigDecimal height;
    //订单总量 克
    private BigDecimal weight;
    //countryCode(收件人国家码)
    private String countryCode;
    //是否首次
    private Integer isFirstTime;
}