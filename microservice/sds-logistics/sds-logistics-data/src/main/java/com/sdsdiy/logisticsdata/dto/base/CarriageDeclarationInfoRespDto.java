package com.sdsdiy.logisticsdata.dto.base;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * 申报信息表(CarriageDeclarationInfoRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-08-31 17:22:07
 */
@Data
public class CarriageDeclarationInfoRespDto implements Serializable {
    
        private Long id;
        private Long merchantId;
        private Long orderId;
        @ApiModelProperty(value = "对应的物流渠道")
        private Long logisticsId;
        @ApiModelProperty(value = "版本号")
        private Integer carriageVersion;
        @ApiModelProperty(value = "产品总重量")
        private BigDecimal weight;
        @ApiModelProperty(value = "产品价格")
        private BigDecimal price;
        @ApiModelProperty(value = "申报价格")
        private BigDecimal declareprice;
        @ApiModelProperty(value = "收件地址信息")
        private String adressInfo;
        @ApiModelProperty(value = "申报产品信息")
        private String productsInfo;
        @ApiModelProperty(value = "是否删除  0 否 1是")
        private Integer isDelete;
        @ApiModelProperty(value = "创建者id")
        private Long createUid;
        @ApiModelProperty(value = "更新者id")
        private Long updateUid;
        @ApiModelProperty(value = "创建时间")
        private Date createTime;
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

}