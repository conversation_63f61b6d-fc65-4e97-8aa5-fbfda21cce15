package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 运单申请记录表(CarriageNoRecodeRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-09-04 19:22:36
 */
@Data
public class CarriageNoRecodeRespDto implements Serializable {
    
        private Long id;
        private Long merchantId;
        private Long orderId;
        @ApiModelProperty(value = "对应的物流渠道")
        private Long logisticsId;
        @ApiModelProperty(value = "申报信息id")
        private Long declarationInfoId;
        @ApiModelProperty(value = "请求状态 1.待生成 2.生成成功 3.同步运单 4.生成失败")
        private Integer status;
        @ApiModelProperty(value = "事件编号")
        private String eventNo;
        @ApiModelProperty(value = "运单编号")
        private String carriageNo;
        @ApiModelProperty(value = "订单编号")
        private String orderNo;
        @ApiModelProperty(value = "申请号（自己生成的")
        private String extendNo;
        @ApiModelProperty(value = "1.运营人员手动申请 2.自主调用")
        private Integer operationMode;
        @ApiModelProperty(value = "收件人国家简码")
        private String countryCode;
        @ApiModelProperty(value = "版本号")
        private Integer carriageVersion;
        @ApiModelProperty(value = "面单状态1.未生成 2.生成成功 3.生成失败")
        private Integer sheetLabelStatus;
        @ApiModelProperty(value = "面单地址")
        private String labelUrl;
        @ApiModelProperty(value = "物流状态 1.创建 2.发货 3.揽收 4.中转 5.签收 6.无需跟踪")
        private Integer trackStatus;
        @ApiModelProperty(value = "最近调用时间")
        private Date apiUpdateTime;
        @ApiModelProperty(value = "最近更新时间")
        private Date latestTrackTime;
        @ApiModelProperty(value = "订单完成时间")
        private Date orderFinishTime;
        @ApiModelProperty(value = "揽收时间")
        private Date receivingTime;
        @ApiModelProperty(value = "签收时间")
        private Date signTime;
        @ApiModelProperty(value = "最后一条时间")
        private String latestTrackInfo;
        @ApiModelProperty(value = "错误信息记录")
        private String errorInfo;
        @ApiModelProperty(value = "额外信息字段")
        private String extraInfo;
        @ApiModelProperty(value = "是否废弃  0 否 1是")
        private Integer isAbandon;
        @ApiModelProperty(value = "是否删除  0 否 1是")
        private Integer isDelete;
        @ApiModelProperty(value = "创建者id")
        private Long createUid;
        @ApiModelProperty(value = "更新者id")
        private Long updateUid;
        @ApiModelProperty(value = "创建时间")
        private Date createTime;
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;
        /**
         * 物流仓账号配置Id 0为默认仓号申请
         */
        private Long logisticsBayAccountConfigId;
        @ApiModelProperty(value = "物流里程碑 NONE NO_RECORD=暂无记录  PICKUP=待揽收   DOMESTIC_TRANSPORT=国内运输中  INTERNATIONAL_TRANSPORT=国外运输中  TRANSPORT=运输中  ARRIVAL_AT_PICKUP=到达待取  DELIVERED=已妥投  DELIVERY_EXCEPTION=物流更新异常")
        private String latestTrackMilestone;
}