package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * (CountryExpressInfoNewRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-07-06 14:42:59
 */
@Data
public class CountryExpressInfoNewRespDto implements Serializable {

    private Long id;
    private String name;
    @ApiModelProperty(value = "限重")
    private Integer limitPriority;
    @ApiModelProperty(value = "首重")
    private Integer firstPriority;
    @ApiModelProperty(value = "开始时间")
    private Integer prescriptionStart;
    @ApiModelProperty(value = "发货结束时间")
    private Integer prescriptionEnd;
    @ApiModelProperty(value = "发货时间类型：1自然日，2工作日")
    private Integer prescriptionType;
    @ApiModelProperty(value = "挂号费")
    private Double handlePrice;
    @ApiModelProperty(value = "排除区域")
    private String excludeArea;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "1开启2禁用")
    private Integer status;
    @ApiModelProperty(value = "1默认2不默认")
    private Integer type;
    @ApiModelProperty(value = "渠道id")
    private Long logisticsId;
    private Long createTime;
    @ApiModelProperty(value = "阶梯价")
    private String price;
    @ApiModelProperty(value = "区域范围：0部分 1全部")
    private Integer areaType;
    @ApiModelProperty(value = "首重收费金额")
    private Double firstPriorityCost;
    @ApiModelProperty(value = "阶梯收费-是否减去首重 1 是 0 否")
    private Integer minusFirstPriority;
    @ApiModelProperty(value = "重量计算规则 0 区间最大重量 1 实际重量 2 取整")
    private Integer ruleWeight;
    @ApiModelProperty(value = "重量计算规则-取整值")
    private Double ruleWeightRound;
    @ApiModelProperty(value = "金额")
    private String currencySymbol;
    /**
     * 运费
     */
    private Double freight;
    /**
     * 是否抛重
     */
    private Integer isThrowWeight;
    /**
     * 总重量
     */
    private Double totalWeight;
    /**
     * 运费类型
     */
    private String chargeType;
    /**
     * 费用
     */
    private BigDecimal perUnitCost;
    /**
     * 是否开关偏远地区
     */
    private Integer isOpenFarawayArea;
    /**
     * self-自身,dhl-dhl的偏远地址
     */
    private String farawayAreaResource;
    /**
     * 处理类型:
     */
    private String farawayAreaType;
    /**
     * 偏远费用
     */
    private BigDecimal farawayAreaValue;
    /**
     * noLimit=无限制ge=大于等于 le=小于等于
     */
    private String farawayAreaWeightType;
    /**
     * 重量
     */
    private Integer farawayAreaWeight;
    /**
     * 是否是偏远地区
     */
    private Integer isFarawayArea;
    /**
     * 最小重量
     */
    private Integer minWeightLimit;
    /**
     * 是否边长限制
     */
    private Integer isSideLimit;
    /**
     * 最大宽度
     */
    private BigDecimal widthMax;
    /**
     * 最大长度
     */
    private BigDecimal lengthMax;
    /**
     * 最大高度
     */
    private BigDecimal heightMax;
    @ApiModelProperty("是否有服务费上限")
    private Integer isServiceChargeUpperLimit;
    @ApiModelProperty("服务费上限费用")
    private BigDecimal serviceChargeUpperCost;
    @ApiModelProperty("是否有服务费下限")
    private Integer isServiceChargeLowerLimit;
    @ApiModelProperty("服务费下限费用")
    private BigDecimal serviceChargeLowerCost;
    @ApiModelProperty("是否有服务费上限")
    private Integer isServiceChargeDerated;
    @ApiModelProperty("服务费减免条件")
    private BigDecimal serviceChargeDeratedConditionalCost;
    @ApiModelProperty("是否启用当前国家妥投时效数据")
    private Integer isUseTrackSign;

    @ApiModelProperty("清关费用")
    private BigDecimal clearanceFee;
    @ApiModelProperty("关税税率")
    private BigDecimal tariffRate;
}