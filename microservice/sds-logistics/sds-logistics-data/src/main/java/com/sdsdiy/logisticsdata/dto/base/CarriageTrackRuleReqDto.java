package com.sdsdiy.logisticsdata.dto.base;

import java.util.Date;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * (CarriageTrackRuleReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-09-03 15:48:50
 */
@Data
public class CarriageTrackRuleReqDto implements Serializable {
    
 
        @NotNull(message = "id不能为空")
        @ApiModelProperty(value = "id")
        private Long id;
     
        @NotNull(message = "服务商id不能为空")
        @ApiModelProperty(value = "服务商id")
        private Long serviceProdviderId;
     
        @NotNull(message = "揽收关键字 , 分割不能为空")
        @ApiModelProperty(value = "揽收关键字 , 分割")
        private String receiveingKeyword;
     
        @NotNull(message = "签收关键字 ，分割不能为空")
        @ApiModelProperty(value = "签收关键字 ，分割")
        private String signKeyword;
     
        @NotNull(message = "是否删除  0 否 1是不能为空")
        @ApiModelProperty(value = "是否删除  0 否 1是")
        private Integer isDelete;
     
        @NotNull(message = "创建者id不能为空")
        @ApiModelProperty(value = "创建者id")
        private Long createUid;
     
        @NotNull(message = "更新者id不能为空")
        @ApiModelProperty(value = "更新者id")
        private Long updateUid;
     
        @NotNull(message = "创建时间不能为空")
        @ApiModelProperty(value = "创建时间")
        private Date createTime;
     
        @NotNull(message = "更新时间不能为空")
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;
    
}