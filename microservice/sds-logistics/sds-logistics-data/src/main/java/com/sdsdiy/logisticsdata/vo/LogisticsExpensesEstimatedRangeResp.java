package com.sdsdiy.logisticsdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;

/**
 * @author: bin_lin
 * @date: 2024/11/12 18:27
 * @desc:
 */
@Data
public class LogisticsExpensesEstimatedRangeResp {
    @ApiModelProperty("是否可用物流")
    private Integer isAvailable;
    @ApiModelProperty("不可用原因")
    private String errorMsg;
    @ApiModelProperty("评估价最高的内容")
    private String estimatedTextMax;
    @ApiModelProperty("评估价最低的内容")
    private String estimatedTextMin;
    @ApiModelProperty("最小评估费")
    private BigDecimal estimatedMin;
    @ApiModelProperty("预估状态 PROCESSING=进行中,COMPLETED=完成,NO_COMPLETED=无法预估，物流可选择,FAILED=预估结果失败，物流无法使用")
    private String estimatedStatus;
    public LogisticsExpensesEstimatedRangeResp() {
        this.isAvailable = YES;
    }

}
