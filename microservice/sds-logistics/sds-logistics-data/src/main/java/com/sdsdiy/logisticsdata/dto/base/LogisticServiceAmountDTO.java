package com.sdsdiy.logisticsdata.dto.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.io.Serializable;

/**
 * (LogisticProduct)实体类
 *
 * <AUTHOR>
 * @since 2020-07-07 21:09:43
 */
@Data
public class LogisticServiceAmountDTO implements Serializable {
    @DtoDefault
    private Long id;
    @DtoDefault
    private Long logisticsId;
    @DtoDefault
    private String amountCode;
    @DtoDefault
    private Double amount;
}