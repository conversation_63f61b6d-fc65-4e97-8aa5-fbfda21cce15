package com.sdsdiy.logisticsdata.dto.base;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
 * (NationalityRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-07-15 10:32:31
 */
@Data
public class NationalityRespDto  extends BaseDTO {
    @DtoDefault
    @ApiModelProperty(value = "国家（地区）英文名")
    private String englishName;
    @DtoDefault
    @ApiModelProperty(value = "国家（地区）名")
    private String name;
    @DtoDefault
    @ApiModelProperty(value = "英文缩写")
    private String abbreviation;
    @ApiModelProperty(value = "电话区号")
    private Integer code;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "1不显示2显示具体的省市区")
    private Integer showCountryDetail;

}