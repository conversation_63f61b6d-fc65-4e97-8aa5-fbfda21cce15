package com.sdsdiy.logisticsdata.dto.base;

import com.sdsdiy.common.base.entity.dto.BasePO;
import lombok.Data;

import java.io.Serializable;


/**
 * 物流仓账号配置(LogisticsBayAccountConfig)实体类
 *
 * <AUTHOR>
 * @since 2021-07-09 16:41:26
 */
@Data
public class LogisticsBayAccountConfigDTO  implements Serializable {
    private static final long serialVersionUID = -67972929706519593L;
    /**
    * 名称做标识用
    */
    private String name;
    /**
    * 物流id
    */
    private Long logisticsId;
    /**
    * 仓id
    */
    private Long issuingBayId;
    /**
    * 物流配置
    */
    private String configInfo;
    /**
    * 是否启用账号配置
    */
    private Integer isEnableConfig;
    /**
    * 发货人信息
    */
    private String shipperInfo;
    /**
    * 是否启用发货人
    */
    private Integer isEnableShipper;

}