package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * (CountryExpressInfoNewReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-07-06 14:43:00
 */
@Data
public class CountryExpressInfoNewReqDto implements Serializable {


    private Long id;

    private String name;

    @NotNull(message = "限重不能为空")
    @ApiModelProperty(value = "限重")
    private Integer limitPriority;

    @NotNull(message = "首重不能为空")
    @ApiModelProperty(value = "首重")
    private Integer firstPriority;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间")
    private Integer prescriptionStart;

    @NotNull(message = "发货结束时间不能为空")
    @ApiModelProperty(value = "发货结束时间")
    private Integer prescriptionEnd;

    @NotNull(message = "发货时间类型：1自然日，2工作日不能为空")
    @ApiModelProperty(value = "发货时间类型：1自然日，2工作日")
    private Integer prescriptionType;

    @NotNull(message = "挂号费不能为空")
    @ApiModelProperty(value = "挂号费")
    private Double handlePrice;

    @NotNull(message = "排除区域不能为空")
    @ApiModelProperty(value = "排除区域")
    private String excludeArea;

    @NotNull(message = "备注不能为空")
    @ApiModelProperty(value = "备注")
    private String remark;

    @NotNull(message = "1开启2禁用不能为空")
    @ApiModelProperty(value = "1开启2禁用")
    private Integer status;

    @NotNull(message = "1默认2不默认不能为空")
    @ApiModelProperty(value = "1默认2不默认")
    private Integer type;

    @NotNull(message = "渠道id不能为空")
    @ApiModelProperty(value = "渠道id")
    private Long logisticsId;

    private Long createTime;

    @NotNull(message = "阶梯价不能为空")
    @ApiModelProperty(value = "阶梯价")
    private String price;

    @NotNull(message = "区域范围：0部分 1全部不能为空")
    @ApiModelProperty(value = "区域范围：0部分 1全部")
    private Integer areaType;

    @NotNull(message = "首重收费金额不能为空")
    @ApiModelProperty(value = "首重收费金额")
    private Double firstPriorityCost;

    @NotNull(message = "阶梯收费-是否减去首重 1 是 0 否不能为空")
    @ApiModelProperty(value = "阶梯收费-是否减去首重 1 是 0 否")
    private Integer minusFirstPriority;

    @NotNull(message = "重量计算规则 0 区间最大重量 1 实际重量 2 取整不能为空")
    @ApiModelProperty(value = "重量计算规则 0 区间最大重量 1 实际重量 2 取整")
    private Integer ruleWeight;

    @NotNull(message = "重量计算规则-取整值不能为空")
    @ApiModelProperty(value = "重量计算规则-取整值")
    private Double ruleWeightRound;

    @NotNull(message = "金额不能为空")
    @ApiModelProperty(value = "金额")
    private String currencySymbol;

}