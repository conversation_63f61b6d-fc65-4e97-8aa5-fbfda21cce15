package com.sdsdiy.logisticsdata.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MerchantAliExpressConfig {
    @JsonProperty("send_fail_handle")
    private String sendFailHandle;
    @JsonProperty("sender_address")
    private AliexpressAddressVO senderAddress;
    @JsonProperty("refund_address")
    private AliexpressAddressVO refundAddress;

    @ApiModelProperty("jit退件 1销毁 2退回")
    private Integer jitReturn;
    @ApiModelProperty("jit退件地址")
    private AeChoiceAddressVO jitRefundAddress;
}