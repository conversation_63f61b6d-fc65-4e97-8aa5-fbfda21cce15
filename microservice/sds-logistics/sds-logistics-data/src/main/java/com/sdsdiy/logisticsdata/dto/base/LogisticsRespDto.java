package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 物流信息表(LogisticsRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-06-28 10:10:38
 */
@Data
public class LogisticsRespDto implements Serializable {

    private Long id;
    private Double pickServiceAmount;
    @ApiModelProperty(value = "渠道名")
    private String name;
    @ApiModelProperty(value = "商户id")
    private Long serviceProviderId;
    @ApiModelProperty(value = "折扣")
    private Double discount;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "1启用2不启用3删除")
    private Integer status;
    @ApiModelProperty(value = "包装人工费")
    private Double packingCharges;
    @ApiModelProperty(value = "产品代码")
    private String codeId;
    @ApiModelProperty(value = "算法类型")
    private String algorithmType;
    @ApiModelProperty(value = "阶梯加时最小的间隔")
    private Integer minAddWeight;
    @ApiModelProperty(value = "1特快2不是")
    private Integer expressStatus;
    @ApiModelProperty(value = "是否是自家物流")
    private Integer beCommon;
    @ApiModelProperty(value = "1 fba物流 2 普通物流")
    private Integer type;
    @ApiModelProperty(value = "是否是关联物流")
    private Integer isButtJoint;
    @ApiModelProperty(value = "是否有物流信息")
    private Integer isTrackInfo;
    @ApiModelProperty(value = "对应的速卖通code")
    private String aliexpressCode;
    @ApiModelProperty(value = "对应的速卖通warehouse_carrier_service")
    private String warehouseCarrierService;
    @ApiModelProperty(value = "退款要收的手续费")
    private Integer refundPercent;
    @ApiModelProperty(value = "退款后是否重新生成运单号")
    private Integer refundResetNum;
    @ApiModelProperty(value = "标准名称，用于物流同步")
    private String standardName;
    @ApiModelProperty(value = "体积计算开关true(开)false(关)")
    private Integer bulkOnOff;
    @ApiModelProperty(value = "体积系数")
    private Double bulkRate;
    @ApiModelProperty(value = "仓id")
    private Long issuingBayId;
    @ApiModelProperty(value = "区域id")
    private Long issuingBayAreaId;
    @ApiModelProperty(value = "亚马逊methodName")
    private String methodName;
    @ApiModelProperty(value = "全部产品都可用 1是")
    private Integer allProductStatus;
    @ApiModelProperty(value = "服务费")
    private Double serviceAmount;
    @ApiModelProperty(value = "未减免时的服务费")
    private BigDecimal noDiscountServiceAmount;
    @ApiModelProperty(value = "固定服务费")
    private Double fixedServiceAmount;
    @ApiModelProperty(value = "申请节点")
    private String applyNode;
    private Double fbaLabelAmount;
    private String isOther;
    @ApiModelProperty(value = "ioss类型")
    private String iossType;
    @ApiModelProperty(value = "是否代收费用")
    private Integer isCollectVatAble;
    @ApiModelProperty(value = "ddu类型notFill=不填optional=选填must=必填")
    private String dduType;

    /**
     * original=渠道商本身接口  tracking51=51跟踪 tracking17=17跟踪
     */
    private String trackingMode;
    /**
     * 1追踪承运商code
     */
    private String courierCode;
    /**
     * 是否需要商户上传面单
     */
    private Integer customUploadLable;
    private Long tenantId;
    private BigDecimal bulkBase;
    /**
     * 渠道类型aliOnline=线上平台offline线下
     */
    private String channelType;

    /**
     * 渠道方式FBA FBM
     */
    private String deliveryMethod;
    /**
     * 是否跨租户区域
     */
    private Integer isSpanTenantArea;
    /**
     * 发货地区
     */
    private String shipmentPlaceType;

    @ApiModelProperty(value = "getServiceProviderInfo")
    private ServiceProviderRespDto serviceProviderRespDto;
    /**
     * 郭煌物流商信息
     */
    private String dhGateShippingType;
    /**
     * 物流渠道
     */
    private Long logisticsChannelId;

    /**
     * 当订单取消/售后/重新生成运单/换单等操作时，运单是否需要在第三方取消 0不需要 1需要
     */
    private Integer needCancelOrderInThirdParty;
}