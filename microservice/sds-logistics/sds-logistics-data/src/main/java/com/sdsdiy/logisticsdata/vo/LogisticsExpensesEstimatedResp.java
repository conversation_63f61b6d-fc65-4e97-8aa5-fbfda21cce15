package com.sdsdiy.logisticsdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;

/**
 * @author: bin_lin
 * @date: 2024/11/12 18:27
 * @desc:
 */
@Data
public class LogisticsExpensesEstimatedResp {
    @ApiModelProperty("是否可用物流")
    private Integer isAvailable;
    @ApiModelProperty("评估价内容")
    private String estimatedText;
//    @ApiModelProperty("状态 PROCESSING=评估中 COMPLETED=评估完成 NO_COMPLETED=无法完成预估 FAILED=评估失败")
//    private String status;
    @ApiModelProperty("不可用原因")
    private String errorMsg;
    @ApiModelProperty("预估状态 PROCESSING=评估中 COMPLETED=评估完成 NO_COMPLETED=无法完成预估 FAILED=评估失败")
    private String estimatedStatus;
    @ApiModelProperty("最小评估费")
    private BigDecimal estimated;

    public LogisticsExpensesEstimatedResp() {
        this.isAvailable = YES;
    }

}
