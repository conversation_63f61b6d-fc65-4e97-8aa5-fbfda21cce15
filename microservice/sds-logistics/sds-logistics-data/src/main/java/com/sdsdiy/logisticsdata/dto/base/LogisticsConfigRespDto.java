package com.sdsdiy.logisticsdata.dto.base;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
 * 物流配置(针对发货人与物流密钥等)(LogisticsConfigRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-06-24 18:57:02
 */
@Data
public class LogisticsConfigRespDto implements Serializable {

    private Long id;
    private Long logisticId;
    @ApiModelProperty(value = "物流配置")
    private String configInfo;
    @ApiModelProperty(value = "启用配置")
    private Integer enableConfig;
    @ApiModelProperty(value = "发货人信息")
    private String shipperInfo;
    @ApiModelProperty(value = "启用发货人")
    private Integer enableShipper;
    @ApiModelProperty(value = "是否删除  0 否 1是")
    private Integer isDelete;
    @ApiModelProperty(value = "创建者id")
    private Long createUid;
    @ApiModelProperty(value = "更新者id")
    private Long updateUid;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}