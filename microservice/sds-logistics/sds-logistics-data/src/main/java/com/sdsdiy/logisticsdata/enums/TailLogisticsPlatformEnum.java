package com.sdsdiy.logisticsdata.enums;

import com.sdsdiy.logisticsdata.dto.TailLogisticsPlatFormDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 尾程物流商枚举
 * <AUTHOR>
 * @date 2025/5/8
 */
@AllArgsConstructor
@Getter
public enum TailLogisticsPlatformEnum {
    
    SERVICE_PROVIDER_TEMU(96L,"TEMU")
    ;
    
    private final Long id;
    private final String name;
    
    public static List<TailLogisticsPlatFormDto> getTailLogisticsPlatformIds(){
        List<TailLogisticsPlatFormDto> tailLogisticsPlatFormDtos = new ArrayList<>();
        TailLogisticsPlatformEnum[] values = TailLogisticsPlatformEnum.values();
        for (TailLogisticsPlatformEnum value : values) {
            TailLogisticsPlatFormDto tailLogisticsPlatFormDto = new TailLogisticsPlatFormDto();
            tailLogisticsPlatFormDto.setId(value.getId());
            tailLogisticsPlatFormDto.setName(value.getName());
            tailLogisticsPlatFormDtos.add(tailLogisticsPlatFormDto);
        }
        return tailLogisticsPlatFormDtos;
    } 
}
