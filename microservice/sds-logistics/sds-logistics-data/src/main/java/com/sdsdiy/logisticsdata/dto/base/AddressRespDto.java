package com.sdsdiy.logisticsdata.dto.base;

import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * 订单收货地址记录(AddressRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-07-06 16:36:23
 */
@Data
public class AddressRespDto implements Serializable {
    
        private Long id;
        private Long userId;
        @ApiModelProperty(value = "商户id")
        private Long merchantId;
        @ApiModelProperty(value = "收件人")
        private String consignee;
        @ApiModelProperty(value = "国家")
        private String country;
        @ApiModelProperty(value = "省")
        private String province;
        @ApiModelProperty(value = "省ID")
        private String provinceCode;
        @ApiModelProperty(value = "市")
        private String city;
        @ApiModelProperty(value = "市ID")
        private String cityCode;
        @ApiModelProperty(value = "固定电话")
        private String cellphone;
        @ApiModelProperty(value = "移动电话")
        private String mobilePhone;
        @ApiModelProperty(value = "详细地址")
        private String detail;
        @ApiModelProperty(value = "邮政编码")
        private String postcode;
        private Long createTime;
        private String md5;

}