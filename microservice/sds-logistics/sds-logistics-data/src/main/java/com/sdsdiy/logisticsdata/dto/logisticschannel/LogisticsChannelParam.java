package com.sdsdiy.logisticsdata.dto.logisticschannel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/13 16:13)
 */

@Data
public class LogisticsChannelParam {

    /**
     * 服务商id
     */
    @ApiModelProperty("服务商id")
    private Long serviceProviderId;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String name;

    /**
     * 渠道码
     */
    @ApiModelProperty("渠道码")
    private String code;

    /**
     * 1 fba物流 2 普通物流
     */
    @ApiModelProperty("物流类型：1 FBA物流，2 普通物流")
    private Integer type;

    /**
     * 标准名称，用于亚马逊物流同步
     */
    @ApiModelProperty("标准名称，用于亚马逊物流同步")
    private String standardName;

    /**
     * 亚马逊methodName
     */
    @ApiModelProperty("亚马逊methodName")
    private String methodName;

    /**
     * 对应的速卖通code
     */
    @ApiModelProperty("对应的速卖通code")
    private String aliexpressCode;

    /**
     * 对应的速卖通warehouse_carrier_service
     */
    @ApiModelProperty("对应的速卖通warehouse_carrier_service")
    private String warehouseCarrierService;

    /**
     * 是否公用0否1是
     */
    @ApiModelProperty("是否公用：0 否，1 是")
    private Integer isPublic;

    /**
     * 是否其他
     */
    @ApiModelProperty("是否其他")
    private String isOther;

    /**
     * ioss类型notFill=不填optional=选填must=必填
     */
    @ApiModelProperty("IOSS类型：notFill=不填，optional=选填，must=必填")
    private String iossType;

    /**
     * ddu类型notFill=不填optional=选填must=必填
     */
    @ApiModelProperty("DDU类型：notFill=不填，optional=选填，must=必填")
    private String dduType;

    /**
     * 是否代收税费能力
     */
    @ApiModelProperty("是否代收税费能力")
    private Integer isCollectVatAble;

    /**
     * 是否是对接物流
     */
    @ApiModelProperty("是否是对接物流")
    private Integer isButtJoint;

    /**
     * 是否有物流信息
     */
    @ApiModelProperty("是否有物流信息")
    private Integer isTrackInfo;

    /**
     * 51tracking
     */
    @ApiModelProperty("51tracking的快递代码")
    private String tracking51CourierCode;

    /**
     * 17tracking
     */
    @ApiModelProperty("17tracking的快递代码")
    private String tracking17CourierCode;

    /**
     * 郭煌物流商信息
     */
    @ApiModelProperty("郭煌物流商信息")
    private String dhGateShippingType;

    /**
     * Walmart物流商代码
     */
    @ApiModelProperty("Walmart物流商代码")
    private String walmartCarrierCode;

}
