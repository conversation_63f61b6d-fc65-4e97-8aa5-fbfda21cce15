package com.sdsdiy.logisticsdata.dto.base;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * 申报信息表(CarriageDeclarationInfoReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-08-31 17:22:07
 */
@Data
public class CarriageDeclarationInfoReqDto implements Serializable {
    
 
        private Long id;
     
        private Long merchantId;
     
        private Long orderId;
     
        @NotNull(message = "对应的物流渠道不能为空")
        @ApiModelProperty(value = "对应的物流渠道")
        private Long logisticsId;
     
        @NotNull(message = "版本号不能为空")
        @ApiModelProperty(value = "版本号")
        private Integer carriageVersion;
     
        @NotNull(message = "产品总重量不能为空")
        @ApiModelProperty(value = "产品总重量")
        private BigDecimal weight;
     
        @NotNull(message = "产品价格不能为空")
        @ApiModelProperty(value = "产品价格")
        private BigDecimal price;
     
        @NotNull(message = "申报价格不能为空")
        @ApiModelProperty(value = "申报价格")
        private BigDecimal declareprice;
     
        @NotNull(message = "收件地址信息不能为空")
        @ApiModelProperty(value = "收件地址信息")
        private String adressInfo;
     
        @NotNull(message = "申报产品信息不能为空")
        @ApiModelProperty(value = "申报产品信息")
        private String productsInfo;
     
        @NotNull(message = "是否删除  0 否 1是不能为空")
        @ApiModelProperty(value = "是否删除  0 否 1是")
        private Integer isDelete;
     
        @NotNull(message = "创建者id不能为空")
        @ApiModelProperty(value = "创建者id")
        private Long createUid;
     
        @NotNull(message = "更新者id不能为空")
        @ApiModelProperty(value = "更新者id")
        private Long updateUid;
     
        @NotNull(message = "创建时间不能为空")
        @ApiModelProperty(value = "创建时间")
        private Date createTime;
     
        @NotNull(message = "更新时间不能为空")
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;
    
}