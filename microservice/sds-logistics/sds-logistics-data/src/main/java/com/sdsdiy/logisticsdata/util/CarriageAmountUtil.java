package com.sdsdiy.logisticsdata.util;

import cn.hutool.core.util.NumberUtil;
import com.sdsdiy.common.base.helper.CompareUtils;
import com.sdsdiy.common.base.helper.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
public class CarriageAmountUtil {

    /**
     * 根据商户物流计算租户物流
     *
     * @param merchantCarriageAmount       商户物流费
     * @param tenantCarriageCommissionRate 租户拥挤比例
     * @return 租户付给SAAS的物流费
     */
    public static BigDecimal getTenantCarriageAmount(BigDecimal merchantCarriageAmount, BigDecimal tenantCarriageCommissionRate) {
        if (merchantCarriageAmount == null || CompareUtils.eqZero(merchantCarriageAmount)) {
            return BigDecimal.ZERO;
        }
        if (!NumberUtils.greaterZero(tenantCarriageCommissionRate)) {
            return merchantCarriageAmount;
        }
        return merchantCarriageAmount.divide(BigDecimal.ONE.add(NumberUtil.div(tenantCarriageCommissionRate, BigDecimal.valueOf(100))), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal getAddTenantCarriageCommissionFreight(BigDecimal freight, BigDecimal tenantCarriageCommissionRate) {
        if (freight == null) {
            return null;
        }
        return BigDecimal.valueOf(getAddTenantCarriageCommissionFreight(freight.doubleValue(), tenantCarriageCommissionRate));
    }

    /**
     * @param freight                      原物流费
     * @param tenantCarriageCommissionRate 租户佣金比例
     * @return 加租户佣金后的物流费
     */
    public static Double getAddTenantCarriageCommissionFreight(Double freight, BigDecimal tenantCarriageCommissionRate) {
        if (!NumberUtils.greaterZero(tenantCarriageCommissionRate) || !NumberUtils.greaterZero(freight)) {
            return freight;
        }
        //存的是%的单位的 还的除以100
        BigDecimal newFreight = NumberUtil.mul(freight, NumberUtil.add(BigDecimal.ONE, NumberUtil.div(tenantCarriageCommissionRate, BigDecimal.valueOf(100))));
        return newFreight.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
}
