package com.sdsdiy.logisticsdata.dto.temu;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class TemuUsableShippingServiceDTO {
    @ApiModelProperty("预估状态")
    private String status;
    @ApiModelProperty("channelId 例: 36258218631168")
    private Long channelId;

    @ApiModelProperty("预估文本  例: 预估$0.10; USD; 2-6工作日送达")
    private String estimatedText;

    private BigDecimal estimatedPrice;

    @JSONField(name = "shipLogisticsType")
    private String shipLogisticsType;
    @JSONField(name = "shippingCompanyName")
    private String shippingCompanyName;
    @JSONField(name = "shipCompanyId")
    private Integer shipCompanyId;

}