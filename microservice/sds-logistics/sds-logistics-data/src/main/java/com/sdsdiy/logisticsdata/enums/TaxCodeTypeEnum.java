package com.sdsdiy.logisticsdata.enums;

import com.sdsdiy.common.base.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
@AllArgsConstructor
public enum TaxCodeTypeEnum implements BaseEnum<String> {
    //
    NOT_FILL("notFill", "不用填"),
    OPTIONAL("optional", "选填"),
    MUST("must", "必填"),
    ;
    public final String code;
    @Getter
    public final String desc;

    public static TaxCodeTypeEnum getByCode(String code) {
        for (TaxCodeTypeEnum one : values()) {
            if (one.equalsCode(code)) {
                return one;
            }
        }
        return NOT_FILL;
    }

    @Override
    public String getCode() {
        return code;
    }
}
