package com.sdsdiy.logisticsdata.vo.logistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: bin_lin
 * @date: 2024/12/12 15:22
 * @desc:
 */
@Data
@ApiModel(value = "LogisticsGroupToSelectResp", description = "物流分组列表")
public class LogisticsGroupToSelectResp {

    private Long tenantId;
    @ApiModelProperty("租户名称")
    private String tenantName;
    private List<LogisticsIssuingAreaGroupDto> logisticsAreas;



}
