package com.sdsdiy.logisticsdata.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AeChoiceAddressVO {
    private Long id;
    private String area;
    private String country;
    private String streetAddress;
    private String addressType;
    private String city;
    private String detailAddress;
    private String areaCode;
    private String postcode;
    private String cityCode;
    private String provCode;
    /**
     * 第三方的area_id
     */
    private String locationId;
    private String countryIsoCode;
    private String defaultAddress;
    private String contactName;
    private String phone;
    private String mobile;
    private String prov;
}