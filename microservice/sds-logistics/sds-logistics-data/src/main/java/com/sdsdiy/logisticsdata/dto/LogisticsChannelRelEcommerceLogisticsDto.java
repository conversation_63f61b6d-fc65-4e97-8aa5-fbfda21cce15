package com.sdsdiy.logisticsdata.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * <p>
 * SDS物流渠道和第三方平台物流商映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "LogisticsChannelRelEcommerceLogisticsDto", description = "SDS物流渠道和第三方平台物流商映射表DTO")
public class LogisticsChannelRelEcommerceLogisticsDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "logistics_channel表ID")
    private Long logisticsChannelId;

    @ApiModelProperty(value = "平台枚举")
    private String platformCode;

    @ApiModelProperty(value = "ecommerce_logistics表ID")
    private Long ecommerceLogisticsId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private EcommerceLogisticsDto ecommerceLogisticsDto;
}
