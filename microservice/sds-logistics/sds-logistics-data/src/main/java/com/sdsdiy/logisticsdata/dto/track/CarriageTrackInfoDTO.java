package com.sdsdiy.logisticsdata.dto.track;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/20
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CarriageTrackInfoDTO implements Serializable {
    private String time;
    private String info;

}
