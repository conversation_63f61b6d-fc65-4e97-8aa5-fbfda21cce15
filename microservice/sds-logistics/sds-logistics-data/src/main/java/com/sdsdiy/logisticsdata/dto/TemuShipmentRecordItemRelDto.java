package com.sdsdiy.logisticsdata.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * <p>
 * temu的在线下单包裹和订单子单关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-23
 */
@Data
@NoArgsConstructor
public class TemuShipmentRecordItemRelDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "包裹号")
    private String packageSn;

    @ApiModelProperty(value = "temu 父单号")
    private String parentOrderSn;

    @ApiModelProperty(value = "temu 子单号")
    private String orderSn;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除状态，0：未删除，1：删除")
    private Integer isDelete;

}
