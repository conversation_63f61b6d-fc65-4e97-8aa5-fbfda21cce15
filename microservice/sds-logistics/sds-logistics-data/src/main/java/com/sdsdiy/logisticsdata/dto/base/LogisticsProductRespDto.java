package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 物流产品关联表(LogisticsProductRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-06-28 10:10:38
 */
@Data
public class LogisticsProductRespDto implements Serializable {
    
        private Long id;
        private Long logisticId;
        private Long productId;
        private Long productParentId;
        @ApiModelProperty(value = "1关联2取消关联")
        private Integer status;

}