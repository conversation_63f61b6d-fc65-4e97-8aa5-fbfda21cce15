package com.sdsdiy.logisticsdata.dto.track;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 运单第三方追踪额度
 * </p>
 *
 * <AUTHOR>
 * @since 2024/09/20
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "CarriageThirdTrackQuotaDTO", description = "运单第三方追踪额度DTO")
public class CarriageThirdTrackQuotaDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @DtoDefault
    @ApiModelProperty(value = "id")
    private Long id;

    private Long createUid;

    @ApiModelProperty(value = "租户id")
    @DtoDefault
    private Long tenantId;
    @ApiModelProperty(value = "总数量")
    @DtoDefault
    private Long totalValue;
    @ApiModelProperty(value = "已用量")
    @DtoDefault
    private Long usedValue;
    @ApiModelProperty(value = "状态：1-可用")
    @DtoDefault
    private Integer status;
    @DtoDefault
    @ApiModelProperty(value = "物流ids，数组json")
    private String logisticsIds;
}
