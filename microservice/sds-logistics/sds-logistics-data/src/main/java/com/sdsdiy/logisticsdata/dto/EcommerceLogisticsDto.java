package com.sdsdiy.logisticsdata.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * <p>
 * 第三方平台物流商配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "EcommerceLogisticsDto", description = "第三方平台物流商配置DTO")
public class EcommerceLogisticsDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "平台枚举")
    private String platformCode;

    @ApiModelProperty(value = "服务商ID")
    private String serviceProviderId;

    @ApiModelProperty(value = "服务商名称")
    private String serviceProviderName;

    @ApiModelProperty(value = "服务商品牌名称")
    private String companyName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;




}
