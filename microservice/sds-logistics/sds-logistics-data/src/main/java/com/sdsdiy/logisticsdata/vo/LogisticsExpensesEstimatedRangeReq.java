package com.sdsdiy.logisticsdata.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: bin_lin
 * @date: 2024/11/12 18:27
 * @desc:
 */
@Data
public class LogisticsExpensesEstimatedRangeReq {
    @ApiModelProperty("第三方单号")
    private String outOrderNo;
    @ApiModelProperty("店铺id")
    private Long merchantStoreId;
    @ApiModelProperty("店铺sellerId")
    private String sellerId;
    @ApiModelProperty("物流codeId")
    private List<String> logisticsCodeIds;
}
