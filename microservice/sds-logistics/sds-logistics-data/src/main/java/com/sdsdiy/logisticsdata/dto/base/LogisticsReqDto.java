package com.sdsdiy.logisticsdata.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * 物流信息表(LogisticsReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-06-28 10:10:38
 */
@Data
public class LogisticsReqDto implements Serializable {

    private Long id;

    @NotNull(message = "渠道名不能为空")
    @ApiModelProperty(value = "渠道名")
    private String name;
    @NotNull(message = "商户id不能为空")
    @ApiModelProperty(value = "商户id")
    private Long serviceProviderId;
    @NotNull(message = "折扣不能为空")
    @ApiModelProperty(value = "折扣")
    private Double discount;
    @NotNull(message = "备注不能为空")
    @ApiModelProperty(value = "备注")
    private String remark;
    @NotNull(message = "1启用2不启用3删除不能为空")
    @ApiModelProperty(value = "1启用2不启用3删除")
    private Integer status;
    @NotNull(message = "包装人工费不能为空")
    @ApiModelProperty(value = "包装人工费")
    private Double packingCharges;
    @NotNull(message = "产品代码不能为空")
    @ApiModelProperty(value = "产品代码")
    private String codeId;
    @NotNull(message = "算法类型不能为空")
    @ApiModelProperty(value = "算法类型")
    private String algorithmType;
    @NotNull(message = "阶梯加时最小的间隔不能为空")
    @ApiModelProperty(value = "阶梯加时最小的间隔")
    private Integer minAddWeight;
    @NotNull(message = "1特快2不是不能为空")
    @ApiModelProperty(value = "1特快2不是")
    private Integer expressStatus;
    @NotNull(message = "是否是自家物流不能为空")
    @ApiModelProperty(value = "是否是自家物流")
    private Integer beCommon;
    @NotNull(message = "1 fba物流 2 普通物流不能为空")
    @ApiModelProperty(value = "1 fba物流 2 普通物流")
    private Integer type;
    @NotNull(message = "对应的速卖通code不能为空")
    @ApiModelProperty(value = "对应的速卖通code")
    private String aliexpressCode;
    @NotNull(message = "对应的速卖通warehouse_carrier_service不能为空")
    @ApiModelProperty(value = "对应的速卖通warehouse_carrier_service")
    private String warehouseCarrierService;
    @NotNull(message = "退款要收的手续费不能为空")
    @ApiModelProperty(value = "退款要收的手续费")
    private Integer refundPercent;
    @NotNull(message = "退款后是否重新生成运单号不能为空")
    @ApiModelProperty(value = "退款后是否重新生成运单号")
    private Integer refundResetNum;
    @NotNull(message = "标准名称，用于物流同步不能为空")
    @ApiModelProperty(value = "标准名称，用于物流同步")
    private String standardName;
    @NotNull(message = "体积计算开关true(开)false(关)不能为空")
    @ApiModelProperty(value = "体积计算开关true(开)false(关)")
    private Integer bulkOnOff;
    @NotNull(message = "体积系数不能为空")
    @ApiModelProperty(value = "体积系数")
    private Double bulkRate;
    @NotNull(message = "仓id不能为空")
    @ApiModelProperty(value = "仓id")
    private Long issuingBayId;
    @NotNull(message = "区域id不能为空")
    @ApiModelProperty(value = "区域id")
    private Long issuingBayAreaId;
    @NotNull(message = "亚马逊methodName不能为空")
    @ApiModelProperty(value = "亚马逊methodName")
    private String methodName;
    @NotNull(message = "全部产品都可用 1是不能为空")
    @ApiModelProperty(value = "全部产品都可用 1是")
    private Integer allProductStatus;

}