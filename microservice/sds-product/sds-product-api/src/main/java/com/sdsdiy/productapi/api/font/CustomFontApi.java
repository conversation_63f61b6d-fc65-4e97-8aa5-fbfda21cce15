package com.sdsdiy.productapi.api.font;

import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.productapi.dto.font.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 自定义字体表(CustomFont)表api层
 * <AUTHOR>
 * @since 2024-02-04 10:55:30
 */
@RequestMapping("/microservice/customFonts")
public interface CustomFontApi {
    @ApiOperation("字体列表")
    @GetMapping("")
    PageResultDto<CustomFontPageRespDto> page(@SpringQueryMap CustomFontPageParam param);

    @ApiOperation("字体保存")
    @PostMapping("")
    void save(@RequestBody CustomFontSaveReqDto param);

    @ApiOperation("字体保存")
    @PostMapping("batch")
    List<CustomFontBatchSaveResultDto> batchSave(@RequestBody CustomFontBatchSaveReqDto param);

    @ApiOperation("字体编辑")
    @PutMapping("{id}")
    void edit(@PathVariable Long id,
              @RequestBody CustomFontEditReqDto param);

    @ApiOperation("字体详情")
    @GetMapping("{id}")
    CustomFontDetailRespDto detail(@PathVariable Long id);

    @ApiOperation("字体删除")
    @DeleteMapping("{id}")
    void delete(@PathVariable Long id, @RequestBody CustomFontDeleteReqDto dto);

    @ApiOperation("确认效果")
    @PutMapping("{id}/confirmed")
    void confirm(@PathVariable Long id);

    @ApiOperation("商户字体下拉")
    @GetMapping("merchants/option")
    MerchantFontRespDto fonts(@RequestParam(required = false) Long merchantId);

    @ApiOperation("商户字体下拉")
    @GetMapping("merchantFonts")
    List<CustomFontRespDto> merchantFonts(@RequestParam Long merchantId,@RequestParam(required = false) String status);

    @ApiOperation("系统css初始化")
    @GetMapping("systemFontCssBuildAndSync")
    void systemFontCssBuildAndSync();

    @ApiOperation("全部css初始化")
    @GetMapping("allFontCssBuildAndSync")
    void allFontCssBuildAndSync();

    @ApiOperation("商户css初始化")
    @GetMapping("userFontCssBuildAndSync")
    void userFontCssBuildAndSync(@RequestParam Long merchantId);

    @ApiOperation("全部字体同步海外")
    @GetMapping("syncFontFileToOutSite")
    void syncFontFileToOutSite();

    @GetMapping("getRenderingImage")
    String getRenderingImage(@RequestParam String fileCode);

    @PostMapping("syncCustomFontDataToOutSite")
    void syncCustomFontDataToOutSite(@RequestBody List<Long> ids);

    @GetMapping("syncSystemFontDataToOutSite")
    void syncSystemFontDataToOutSite();
}

