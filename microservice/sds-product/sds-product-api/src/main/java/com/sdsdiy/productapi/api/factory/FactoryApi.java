package com.sdsdiy.productapi.api.factory;

import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.productapi.dto.FactoryDto;
import com.sdsdiy.productdata.dto.factory.FactoryDTO;
import com.sdsdiy.productdata.dto.factory.FactoryReqDTO;
import com.sdsdiy.productdata.dto.factory.FactorySimpleRespDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * (Factory)表api接口
 *
 * <AUTHOR>
 * @since 2021-07-09 14:40:20
 */
@RequestMapping("/microservice/factory")
public interface FactoryApi {

    @PostMapping("findNameByIds")
    List<BaseIdAndNameDTO> findNameByIds(@RequestBody BaseListDto<Long> dto);

    @GetMapping("/findByIds")
    List<FactoryDto> findByIds(@SpringQueryMap IdsSearchHelper idsSearchHelper);

    @GetMapping("/findByIds/{factoryId}")
    FactoryDto getById(@PathVariable("factoryId") Long factoryId);

    @ApiOperation("工厂-简单信息列表")
    @PostMapping("listFactorySimpleRespDTO")
    BaseListDto<FactorySimpleRespDTO> listFactorySimpleRespDTO(@RequestBody FactoryReqDTO reqDTO);

    @GetMapping("/countNumGroup/bayIds")
    Map<Long, Integer> countNumGroupBayIds(@SpringQueryMap BaseListReqDto req);

    @GetMapping("/tenant/{tenantId}")
    List<FactorySimpleRespDTO> getFactories(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/tenant/{tenantId}/pageByName")
    PageListResultDTO<FactorySimpleRespDTO> pageByName(@PathVariable("tenantId") Long tenantId,
                                                       @RequestParam(value = "name", required = false) String name,
                                                       @RequestParam(value = "issuingBayId", required = false) Long issuingBayId,
                                                       @SpringQueryMap QueryParamHelper queryParamHelper);


    @ApiOperation("工厂-根据租房id开启或者关闭赔付 1全部开启 2.全部关闭")
    @PutMapping("/tenant/{tenantId}")
    void updateCompensationStatusByTenantId(@PathVariable("tenantId") Long tenantId
            , @RequestParam(value = "compensationStatus") Integer compensationStatus);

// 9.22注释
//    @PutMapping("/tenant/{tenantId}/issuingBays/{issuingBayId}/bayAreaId/{bayAreaId}")
//    void bindFactories(@PathVariable("tenantId") Long tenantId,
//                       @PathVariable("issuingBayId") Long issuingBayId,
//                       @PathVariable("bayAreaId") Long bayAreaId,
//                       @RequestBody BaseListDto<Long> reqDto);

// 9.22注释
//    @PutMapping("/tenant/{tenantId}/issuingBays/{issuingBayId}/factory/{id}")
//    void unBindFactories(@PathVariable("tenantId") Long tenantId,
//                         @PathVariable("issuingBayId") Long issuingBayId,
//                         @PathVariable("id") Long id);


    @ApiOperation("授权的物流仓下的工厂列表")
    @PostMapping("/listByAuthIssuingBay")
    List<FactorySimpleRespDTO> getListByAuthIssuingBay(@RequestBody FactoryReqDTO reqDTO);


    @PostMapping("/addFactoryOrderNum/{factoryId}")
    void addFactoryOrderNum(@PathVariable("factoryId") Long id);

    @PostMapping("/saveDTO")
    Long saveDTO(@RequestBody FactoryDTO dto);

    @GetMapping("/findAllIdAndTenantId")
    List<FactorySimpleRespDTO> findAllIdAndTenantId();
}
