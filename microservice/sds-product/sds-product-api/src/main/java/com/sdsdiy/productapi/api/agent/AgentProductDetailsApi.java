package com.sdsdiy.productapi.api.agent;

import com.sdsdiy.productapi.dto.agent.AgentProductDetailsReqDto;
import com.sdsdiy.productapi.dto.agent.AgentProductDetailsRespDto;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 独立部署商户产品表(ProductDetails)表api接口
 *
 * <AUTHOR>
 * @since 2021-06-27 17:41:44
 */
@RequestMapping("/microservice/agentProductDetails")
public interface AgentProductDetailsApi {

    @PostMapping()
    void create(@RequestBody @Valid AgentProductDetailsReqDto reqDto);

    @PutMapping("{id}")
    void edit(@PathVariable Long id,
              @RequestBody @Valid AgentProductDetailsReqDto reqDto);

    @PutMapping("merchant/{merchantId}/products/{productId}")
    void update(
            @PathVariable Long merchantId,
            @PathVariable Long productId,
            @RequestBody @Valid AgentProductDetailsReqDto reqDto
    );

    @GetMapping("getByMerchantIdAndProductId")
    AgentProductDetailsRespDto getByMerchantIdAndProductId(
            @RequestParam(value = "merchantId") Long merchantId,
            @RequestParam(value = "productId") Long productId,
            @RequestParam(value = "fields", required = false) String fields
    );

}