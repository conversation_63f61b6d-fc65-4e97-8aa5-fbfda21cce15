package com.sdsdiy.productapi.api.product;


import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.productdata.dto.auth.AuthAllAccumulatePriceLevelDTO;
import com.sdsdiy.productdata.dto.auth.AuthMerchantOnePriceRespDTO;
import com.sdsdiy.productdata.dto.auth.MerchantUnAuthProductRespDTO;
import com.sdsdiy.productdata.dto.distribution.*;
import com.sdsdiy.productdata.dto.price.*;
import com.sdsdiy.productdata.dto.product.ProductIdReqDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductEditPriceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 产品分销 API
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/14
 */
@Api(tags = "产品分销")
@RequestMapping("microservice/productDistributionApi")
public interface ProductDistributionApi {

    @ApiOperation("分销产品全部授权累计档位")
    @GetMapping("/{tenantId}/getAuthAccumulateLevel")
    Integer getAuthAccumulateLevel(@PathVariable Long tenantId);

    @ApiOperation("分销开关")
    @PutMapping("updateIsDistribution")
    void updateIsDistribution(@RequestBody ProductDistributionStatusDTO statusDTO);

    @ApiOperation("分销产品列表")
    @PostMapping("pageDistributionProduct")
    PageListResultDTO<DistributionProductPageRespDTO> pageDistributionProduct(@RequestBody DistributionProductPageReqDTO reqDTO);

    @ApiOperation("分销平台价")
    @PostMapping("variantDistributionPlatformPrice")
    BaseListDto<ProductVariantPlatformPriceDTO> variantDistributionPlatformPrice(@RequestBody ProductPlatformPriceReqDTO reqDto);

    @ApiOperation("设置分销平台价")
    @PostMapping("updateVariantDistributionPlatformPrice")
    void updateVariantDistributionPlatformPrice(@RequestBody BaseListDto<ProductEditPriceDTO> reqDto, @RequestParam Long parentId);

    @ApiOperation("分销累计价")
    @PostMapping("variantDistributionAccumulatePrice")
    BaseListDto<ProductVariantAccumulatePriceDTO> variantDistributionAccumulatePrice(@RequestBody ProductIdReqDTO reqDto);

    @ApiOperation("设置分销累计价")
    @PostMapping("updateVariantDistributionAccumulatePrice")
    void updateVariantDistributionAccumulatePrice(@RequestBody BaseListDto<ProductLadderPriceListDTO> reqDto, @RequestParam Long parentId);

    @ApiOperation("设置分销累计价档位")
    @PostMapping("setDistributionAccumulatePriceLevel")
    void setDistributionAccumulatePriceLevel(@RequestBody AccumulatePriceLevelDTO levelDTO);

    @ApiOperation("授权分销价-商户列表")
    @PostMapping("pageDistributionMerchant")
    PageListResultDTO<DistributionMerchantPageRespDTO> pageDistributionMerchant(@RequestBody DistributionMerchantPageReqDTO reqDTO);

    @ApiOperation("未授权分销一口价")
    @PostMapping("pageDistributionUnAuthOnePrice")
    PageListResultDTO<MerchantUnAuthProductRespDTO> pageDistributionUnAuthOnePrice(@RequestBody DistributionProductAuthPageReqDTO reqDTO);

    @ApiOperation("未授权分销累计档位")
    @PostMapping("pageDistributionUnAuthAccumulateLevel")
    PageListResultDTO<MerchantUnAuthProductRespDTO> pageDistributionUnAuthAccumulateLevel(@RequestBody DistributionProductAuthPageReqDTO reqDTO);

    @ApiOperation("已授权分销一口价")
    @PostMapping("pageDistributionAuthedOnePrice")
    PageListResultDTO<DistributionProductAuthPageRespDTO> pageDistributionAuthedOnePrice(@RequestBody DistributionProductAuthPageReqDTO reqDTO);

    @ApiOperation("已授权分销累计档位")
    @PostMapping("pageDistributionAuthedAccumulateLevel")
    PageListResultDTO<DistributionProductAuthPageRespDTO> pageDistributionAuthedAccumulateLevel(@RequestBody DistributionProductAuthPageReqDTO reqDTO);

    @ApiOperation("加入累计档位授权")
    @PostMapping("addAccumulateLevelAuth")
    void addAccumulateLevelAuth(@RequestBody DistributionAuthAccumulateLevelDTO levelDTO);

    @ApiOperation("移除累计档位授权")
    @DeleteMapping("removeAccumulateLevelAuth")
    void removeAccumulateLevelAuth(@RequestBody DistributionAuthAccumulateLevelDTO levelDTO);

    @ApiOperation("授权累计档位")
    @PutMapping("authAccumulateLevel")
    void authAccumulateLevel(@RequestBody DistributionAuthAccumulateLevelDTO levelDTO);

    @ApiOperation("全部授权/自定义 切换")
    @PutMapping("allAuthAccumulateLevel")
    void allAuthAccumulateLevel(@RequestBody AuthAllAccumulatePriceLevelDTO levelDTO);

    @ApiOperation("加入一口授权")
    @PostMapping("addOnePriceAuth")
    void addOnePriceAuth(@RequestBody DistributionAuthDTO authDTO);

    @ApiOperation("移除一口价授权")
    @DeleteMapping("removeOnePriceAuth")
    void removeOnePriceAuth(@RequestBody DistributionAuthDTO authDTO);

    @ApiOperation("获取授权一口价")
    @PostMapping("getAuthTenantOnePrice")
    AuthMerchantOnePriceRespDTO getAuthTenantOnePrice(@RequestBody DistributionAuthDTO authDTO);

    @ApiOperation("设置授权一口价")
    @PostMapping("setDistributionAuthOnePrice")
    void setDistributionAuthOnePrice(@RequestBody DistributionAuthOnePriceDTO onePriceDTO);

    @ApiOperation("获取产品所有变体的分销信息")
    @PostMapping("getDistributionProductInfo")
    DistributionProductInfoDTO getDistributionProductInfo(@RequestBody ProductIdReqDTO reqDTO);

    @ApiOperation("设置sds分销产品的平台价")
    @PutMapping("tenantSetSdsProductPlatformPrice")
    void tenantSetSdsProductPlatformPrice(@RequestBody PriceCalculateParameterDTO setDTO);

    @ApiOperation("租户分销授权累计数")
    @PutMapping("listTenantDistributionAuthAccumulateNum")
    List<DistributionAuthAccumulateNumRespDTO> listTenantDistributionAuthAccumulateNum(@RequestBody DistributionAuthAccumulateNumReqDTO reqDTO);
}