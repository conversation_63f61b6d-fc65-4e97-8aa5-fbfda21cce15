package com.sdsdiy.productapi.api.customsDeclaration;

import com.sdsdiy.productapi.dto.customsDeclaration.OrderDeclarationDto;
import com.sdsdiy.productapi.param.OrderCustomsDeclarationsQueryParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/***
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/products/order")
public interface OrderCustomsDeclarationApi {

    /**
     * 获取订单的报关信息
     *
     * @param param
     *
     * @return
     */
    @PostMapping("/customsDeclaration")
    List<OrderDeclarationDto> get(@RequestBody OrderCustomsDeclarationsQueryParam param);
}




