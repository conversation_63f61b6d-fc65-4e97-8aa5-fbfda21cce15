package com.sdsdiy.productapi.api.customsDeclaration;

import com.sdsdiy.productapi.dto.customsDeclaration.AdminDeclarationAndProductDto;
import com.sdsdiy.productapi.dto.customsDeclaration.CopyAllCustomsDeclarationDto;
import com.sdsdiy.productdata.dto.ProductCustomsDeclarationDto;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/***
 *
 * <AUTHOR>
 */
@RequestMapping("/microservice/products/admin")
public interface AdminCustomsDeclarationApi {

    /**
     * 获取运营配置的通用报关信息或者物流报关信息
     *
     * @param declarationId
     *
     * @return
     */
    @GetMapping("/customsDeclaration/{declarationId}")
    AdminDeclarationAndProductDto get(
            @PathVariable Long declarationId,
            @RequestParam(value = "tenantId") Long tenantId
    );

    /**
     * 获取产品通用模版信息
     *
     * @param productId
     *
     * @return
     */
    @GetMapping("/{productId}/customsDeclaration/normal")
    AdminDeclarationAndProductDto getNormal(
            @PathVariable Long productId,
            @RequestParam(value = "tenantId") Long tenantId
    );

    /**
     * 获取产品信息和通用报关信息
     *
     * @param productId
     *
     * @return
     */
    @GetMapping("/product/{productId}/logistics/{logisticsId}")
    AdminDeclarationAndProductDto getProductInfoAndNormalDeclaration(
            @PathVariable Long productId,
            @PathVariable Long logisticsId,
            @RequestParam(value = "tenantId") Long tenantId
    );
    @PostMapping("/products/logistics/copy")
    void copyAllCustomsDeclaration(@RequestBody CopyAllCustomsDeclarationDto customsDeclarationDto);
    
    @GetMapping("/customsDeclarationPrice/export")
    List<ProductCustomsDeclarationDto> getAllProductCustomsDeclarationPrice();

}




