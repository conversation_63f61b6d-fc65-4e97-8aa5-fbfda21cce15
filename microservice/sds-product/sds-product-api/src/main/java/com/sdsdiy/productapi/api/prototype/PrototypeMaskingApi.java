package com.sdsdiy.productapi.api.prototype;

import com.sdsdiy.productapi.dto.product.PrototypeMaskingReq;
import com.sdsdiy.productapi.dto.product.PrototypeMaskingResp;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Delete;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date:2022/5/30 15:11
 * @desc:
 */
@RequestMapping("microservice/prototypeMasking")
public interface PrototypeMaskingApi {

    @ApiOperation(value = "新增蒙版信息")
    @PostMapping("")
    void addPrototypeMasking(List<PrototypeMaskingReq> prototypeMasking);

    @ApiOperation(value = "删除蒙版信息")
    @DeleteMapping("")
    void deletePrototypeMasking(List<Long> prototypeIds);

    @ApiOperation(value = "修改蒙版信息")
    @PutMapping ("")
   void updatePrototypeMasking(List<PrototypeMaskingReq> prototypeMasking);

    @ApiOperation(value = "查询蒙版信息")
    @GetMapping("")
    public List<PrototypeMaskingResp> getPrototypeMaskingList(List<Long> prototypeIds);

}
