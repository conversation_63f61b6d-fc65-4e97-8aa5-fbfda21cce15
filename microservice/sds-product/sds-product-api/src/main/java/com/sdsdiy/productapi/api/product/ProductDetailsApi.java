package com.sdsdiy.productapi.api.product;

import com.sdsdiy.productapi.dto.product.ProductDetailsRespDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/12/1
 */
@RequestMapping("microsercive/productDetails")
public interface ProductDetailsApi {

    @GetMapping("")
    ProductDetailsRespDto getByProductId(@RequestParam Long productId, @RequestParam Long tenantId);
}
