package com.sdsdiy.productapi.api.product;

import com.sdsdiy.productapi.dto.product.ProductMicroCustomizeReqDto;
import com.sdsdiy.productapi.dto.product.ProductMicroCustomizeRespDto;
import com.sdsdiy.productapi.dto.product.ProductMicroCustomizeSaveReqDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品微定制表(ProductMicroCustomize)表api层
 *
 * <AUTHOR>
 * @since 2024-05-21 16:30:36
 */
@RequestMapping("/microservice/productMicroCustomizes")
public interface ProductMicroCustomizeApi {

    @ApiOperation("创建")
    @PostMapping("")
    void create(@RequestBody ProductMicroCustomizeSaveReqDto dto);

    @ApiOperation("根据taskid查询")
    @PostMapping("getByTaskIds")
    List<ProductMicroCustomizeRespDto> getByTaskIds(@RequestBody List<Long> designTaskIds);

    @GetMapping("getById")
    ProductMicroCustomizeRespDto getById(@RequestParam Long id);

    @PutMapping("updateDtoById")
    void updateDtoById(@RequestBody ProductMicroCustomizeReqDto dto);
}

