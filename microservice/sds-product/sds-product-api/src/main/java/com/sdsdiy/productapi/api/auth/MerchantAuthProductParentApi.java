package com.sdsdiy.productapi.api.auth;


import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.productdata.dto.auth.MerchantAuthProductRespDTO;
import com.sdsdiy.productapi.param.AuthOnePriceParamList;
import com.sdsdiy.productdata.dto.auth.*;
import com.sdsdiy.productdata.dto.distribution.ProductDistributionStatusDTO;
import com.sdsdiy.productdata.dto.merchantproductparent.MerchantProductParentBatchAddResp;
import com.sdsdiy.productdata.dto.product.ProductIdReqDTO;
import com.sdsdiy.productdata.dto.product.SmallOrderPriceDto;
import com.sdsdiy.userdata.dto.merchant.MerchantPageRespDTO;
import com.sdsdiy.userdata.dto.tenant.TenantCreateMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 商户被授权产品母体表 API
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/02
 */
@Api(tags = "商户被授权产品")
@RequestMapping("microservice/merchantAuthProductParentApi")
public interface MerchantAuthProductParentApi {

    @ApiOperation("校验全部授权是否完成")
    @PostMapping("checkProductAuthMerchant")
    Boolean checkProductAuthMerchant(@RequestParam Long merchantId);

    @ApiOperation("商户加入产品库")
    @PostMapping("addFromProductLibrary")
    void addFromProductLibrary(@RequestBody ProductIdReqDTO ReqDto);

    /**
     * 租户授权 子商户/其他租户的主商户
     * ！！自己加入产品库的不要走这个方法！！
     */
    @ApiOperation("新增授权产品")
    @PostMapping("addAuth")
    void addAuth(@RequestBody ProductIdReqDTO dto);

    @ApiOperation("移除授权")
    @DeleteMapping("deleteAuth")
    void deleteAuth(@RequestBody ProductIdReqDTO dto);

    @ApiOperation("获取授权累计价档位")
    @GetMapping("getAllAccumulatePriceLevel")
    Integer getAllAccumulatePriceLevel(@RequestParam Long merchantId, @RequestParam Long productTenantId);

    @ApiOperation("已授权-自己平台产品列表（公开、私有）")
    @PostMapping("merchantAuthProduct")
    PageListResultDTO<MerchantAuthProductParentRespDTO> pageMerchantAuthProduct(@RequestBody MerchantAuthPageReqDTO select);

    @ApiOperation("未授权-产品列表")
    @PostMapping("merchantUnAuthProduct")
    PageListResultDTO<MerchantUnAuthProductRespDTO> pageMerchantUnAuthProduct(@RequestBody MerchantAuthPageReqDTO reqDTO);

    @ApiOperation("授权商户列表")
    @PostMapping("pageAuthMerchant")
    PageListResultDTO<AuthMerchantPageRespDTO> pageAuthMerchant(@RequestBody AuthMerchantPageReqDTO pageDTO);

    @ApiOperation("获取专属授权价")
    @PostMapping("getAuthFactorySupplyPrice")
    AuthFactorySupplyPriceRespDTO getAuthFactorySupplyPrice(@RequestBody AuthFactorySupplyPriceReqDTO reqDTO);

    @ApiOperation("保存专属授权价")
    @PostMapping("saveAuthFactorySupplyPrice")
    void saveAuthFactorySupplyPrice(@RequestBody AuthFactorySupplyPriceSaveDTO saveDTO);

    @ApiOperation("获取专属授权价")
    @GetMapping("getProductAuthFactorySupplyPrice")
    List<AuthSupplyLadderPriceDTO> getProductAuthFactorySupplyPrice(@SpringQueryMap GetAuthFactorySupplyPriceParam param);

    @ApiOperation("获取授权一口价")
    @PostMapping("getAuthMerchantOnePrice")
    AuthMerchantOnePriceRespDTO getAuthMerchantOnePrice(@RequestBody AuthMerchantOnePriceReqDTO reqDTO);

    @ApiOperation("设置授权一口价")
    @PostMapping("setAuthMerchantOnePrice")
    void setAuthMerchantOnePrice(@RequestBody AuthOnePriceParamList param);

    @ApiOperation("某个产品的授权商户列表")
    @PostMapping("/pageOfProductAuthMerchant")
    PageListResultDTO<MerchantAuthProductRespDTO> pageOfProductAuthMerchant(@RequestBody AuthMerchantPageReqDTO reqDTO);

    @ApiOperation("某个sds产品的授权商户列表")
    @PostMapping("/pageOfSdsProductAuthMerchant")
    PageListResultDTO<MerchantAuthProductRespDTO> pageOfSdsProductAuthMerchant(@RequestBody AuthMerchantPageReqDTO reqDTO);

    @ApiOperation("获取产品累计价档位数")
    @PostMapping("getParentAccumulatePriceLevel")
    Integer getParentAccumulatePriceLevel(@RequestBody ProductIdReqDTO reqDTO);

    @ApiOperation("未授权-sds产品")
    @PostMapping("pageMerchantUnAuthSdsProduct")
    PageListResultDTO<MerchantUnAuthProductRespDTO> pageMerchantUnAuthSdsProduct(@RequestBody MerchantAuthPageReqDTO reqDTO);

    @ApiOperation("已授权-sds产品")
    @PostMapping("pageMerchantAuthedSdsProduct")
    PageListResultDTO<MerchantAuthProductParentRespDTO> pageMerchantAuthedSdsProduct(@RequestBody MerchantAuthPageReqDTO select);

    @ApiOperation(("商户是否有对应的授权产品"))
    @PostMapping("listMerchantAuthParent")
    List<MerchantAuthProductParentRespDTO> listMerchantAuthParent(@RequestBody List<Long> parentIds, @RequestParam Long merchantId);

    @ApiOperation(("商户-产品 授权关系"))
    @PostMapping("getOneMerchantAuthParent")
    MerchantAuthProductParentRespDTO getOneMerchantAuthParent(@RequestParam Long parentId, @RequestParam Long merchantId);

    @ApiOperation("工厂授权商户数")
    @PostMapping("factoryMerchantAuthParenNums")
    Map<Long, Integer> getFactoryMerchantAuthParenNums(@SpringQueryMap BaseListReqDto reqDto);

    @ApiOperation("某个产品的授权商户列表")
    @GetMapping("factory/{factoryId}/pageOfFactoryAuthMerchant")
    PageListResultDTO<MerchantPageRespDTO> pageOfFactoryAuthMerchant(@PathVariable("factoryId") Long factoryId,
                                                                     @SpringQueryMap BasePageSelect basePageSelect);

    @ApiOperation("某个产品的授权商户列表")
    @PostMapping("changeMerchantTenantId")
    void changeMerchantTenantId(@RequestBody TenantCreateMessage message);

    @ApiOperation("处理分销产品数据")
    @PostMapping("dataDealDistributionAuthMerchant")
    void dataDealDistributionAuthMerchant(@RequestBody ProductDistributionStatusDTO distributionDTO);

    @ApiOperation("根据产品母体id批量加入产品库")
    @PostMapping("/batch/merchant")
    MerchantProductParentBatchAddResp batchAddForMerchant(@RequestBody ProductIdReqDTO reqDTO);

    @ApiOperation("小单变体起批量")
    @PostMapping("finalSmallOrderMinNumMap")
    Map<Long, Integer> finalSmallOrderMinNumMap(@RequestBody ProductSmallMinNumDto dto);

    @ApiOperation("小单变体最终阶梯价")
    @PostMapping("finalSmallOrderPricesMap")
    Map<Long, List<SmallOrderPriceDto>> finalSmallOrderPricesMap(@RequestBody ProductSmallMinNumDto dto);

    @ApiOperation("新小单阶梯价格")
    @PostMapping("getNewSmallOrderPriceDtos")
    List<SmallOrderPriceDto> getNewSmallOrderPriceDtos(@RequestBody ProductNewSmallPriceDto dto);

    @ApiOperation("根据产品批量移除授权")
    @DeleteMapping("/deleteMerchantAuth")
    void deleteMerchantAuth(@RequestBody MerchantAuthProductReqDTO reqDTO);
}