package com.sdsdiy.productapi.api.product;

import com.sdsdiy.productapi.dto.product.OrderProductFactoryLimitDto;
import com.sdsdiy.productapi.dto.product.ProductFactoryProductionLimitDetailReqDto;
import com.sdsdiy.productapi.dto.product.ProductFactoryProductionLimitDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 产品工厂产能上限表(ProductFactoryProductionLimit)表api层
 *
 * <AUTHOR>
 * @since 2024-12-10 10:31:36
 */
@RequestMapping("/microservice/productFactoryProductionLimits")
public interface ProductFactoryProductionLimitApi {

    @PostMapping
    void save(@RequestBody ProductFactoryProductionLimitDto dto);

    @PostMapping("detail")
    ProductFactoryProductionLimitDto detail(@RequestBody ProductFactoryProductionLimitDetailReqDto dto);

    @PutMapping("updateUsedCount")
    void updateUsedCount(@RequestBody OrderProductFactoryLimitDto dto);

}

