package com.sdsdiy.productapi.api.product;

import com.sdsdiy.productdata.dto.price.MerchantUpMemberProductPriceReqDTO;
import com.sdsdiy.productdata.dto.price.MerchantUpMemberProductPriceRespDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/7/5
 */
@RequestMapping("microservice/ProductPriceApi")
public interface ProductPriceApi {
    @PostMapping("calUpMemberLevel")
    MerchantUpMemberProductPriceRespDTO calUpMemberLevel(@RequestBody MerchantUpMemberProductPriceReqDTO reqDTO);
}
