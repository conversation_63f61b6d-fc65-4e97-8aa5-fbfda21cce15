package com.sdsdiy.productapi.api.prototype;

import com.sdsdiy.productdata.dto.prototype.PrototypeBaseDTO;
import com.sdsdiy.productdata.dto.prototype.query.ProductPrototypeReqDTO;
import com.sdsdiy.productdata.vo.prototype.DesignPrototypeVo;
import com.sdsdiy.productdata.vo.prototype.PrototypeProductRelVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/27
 */
@RequestMapping("/microservice/prototype")
public interface PrototypeApi {
    @ApiOperation(value = "psd解析智能对象")
    @PostMapping("/psdSmartObject")
    void psdSmartObject();

    @PostMapping("/orginalName")
    void orginalName();

    @GetMapping("")
    List<PrototypeProductRelVo> getByGroupAndProductIds(@RequestParam Long id, @RequestParam String productIds);

    @GetMapping("getByGroupId")
    List<PrototypeProductRelVo> getByGroupId(@RequestParam Long id);

    @GetMapping("getGroupIdById")
    Long getGroupIdById(@RequestParam Long id);

    @ApiOperation("批量查询")
    @GetMapping("getByGroupIds")
    List<PrototypeProductRelVo> getByGroupIds(@RequestParam String ids);

    @GetMapping("getByPrototypeIds")
    List<PrototypeProductRelVo> getByPrototypeIds(@RequestParam String ids);

    @ApiOperation("查询模板表")
    @PostMapping("findPrototypes")
    List<DesignPrototypeVo> findPrototypes(@RequestBody List<Long> prototypeIds);

    @ApiOperation("查询简单模板表")
    @PostMapping("findSimplePrototypes")
    List<DesignPrototypeVo> findSimplePrototypes(@RequestBody List<Long> prototypeIds);

    @ApiOperation("查询模板基础对象")
    @PostMapping("findPrototypeDtoById")
    PrototypeBaseDTO findPrototypeDtoById(@RequestParam Long id);


    @ApiOperation("根据产品变体id查询模板信息")
    @PostMapping("findPrototypesByProduct")
    List<DesignPrototypeVo> findPrototypesByProduct(@RequestBody List<Long> productIds, @RequestParam Long merchantId, @RequestParam Long userId);


    @ApiOperation("根据产品变体id查询模板信息")
    @PostMapping("findPrototypesByProductName")
    List<DesignPrototypeVo> findPrototypesByProductName(@RequestBody ProductPrototypeReqDTO reqDTO);

    @ApiOperation(value = "产品底图数据")
    @PostMapping("updateDesignImageUrl")
    public void updateDesignImageUrl();

    //旧数据迁徙
    @ApiOperation(value = "效果图细节图旧数据迁徙")
    @PostMapping("oldMoveImage")
    public Boolean oldMoveImage();

    @ApiOperation(value = "生产稿件平台模板转私有模板")
    @PostMapping("oldMoveManuscriptPrivate")
    public boolean oldMoveManuscriptPrivate();

    @ApiOperation(value = "删除特殊产品的模板组")
    //特殊的产品数据迁徙
    @PostMapping("deletespecialProduct")
    public boolean deletespecialProduct();

    @ApiOperation(value = "特殊产品测试")
    @PostMapping("specialProduct")
    public boolean special();

    @ApiOperation(value = "删除模板组")
    //特殊的产品数据迁徙
    @PostMapping("onlyDeleteBatch")
    public boolean onlyDeleteBatch(@RequestBody List<Long> ids);


    @ApiOperation(value = "要删除的模板组")
    //特殊的产品数据迁徙
    @PostMapping("deleteGroupList")
    public List<Long> deleteGroupList(@RequestBody List<Long> ids);
}
