package com.sdsdiy.productapi.api.product;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.productapi.dto.product.ProductInquiryListReqDto;
import com.sdsdiy.productapi.dto.product.ProductInquiryReqDto;
import com.sdsdiy.productapi.dto.product.ProductInquiryRespDto;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品询价单表(ProductInquiry)表api接口
 *
 * <AUTHOR>
 * @since 2022-04-29 23:35:19
 */
@Api(tags = "产品询价")
@RequestMapping("microservice/productInquiry")
public interface ProductInquiryApi {

    @PutMapping("/batchSubmit")
    void batchSubmit(@RequestBody() List<ProductInquiryReqDto> productInquiryReqDtoList, @RequestParam("tenantId") Long tenantId, @RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId);

    @DeleteMapping("/{productInquiryId}")
    void deleteById(@PathVariable("productInquiryId") Long productInquiryId);

    @GetMapping("/page")
    PageListResultRespDto<ProductInquiryRespDto> page(@SpringQueryMap ProductInquiryListReqDto productInquiryListReqDto);

    @GetMapping("/pageForTenant")
    PageListResultRespDto<ProductInquiryRespDto> pageForTenant(@SpringQueryMap ProductInquiryListReqDto productInquiryListReqDto);

    @GetMapping("/{productInquiryId}")
    ProductInquiryRespDto getDetailById(@PathVariable("productInquiryId") Long productInquiryId);

    @PostMapping("/audit/{productInquiryId}")
    void audit(@RequestBody() ProductInquiryReqDto productInquiryReqDto, @PathVariable("productInquiryId") Long productInquiryId);

    @GetMapping("/simple/{productInquiryId}")
    ProductInquiryRespDto getSimpleById(@PathVariable("productInquiryId") Long productInquiryId);
}