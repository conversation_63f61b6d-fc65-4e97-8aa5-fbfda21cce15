package com.sdsdiy.productapi.api.prototype;

import com.sdsdiy.productdata.dto.prototype.PrototypePsdDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/31
 */
@RequestMapping("microservice/prototypePsd")
public interface PrototypePsdApi {

    @ApiOperation(("根据id批量查询"))
    @PostMapping("getByIds")
    List<PrototypePsdDto> getByIds(@RequestBody List<Long> ids);

    @GetMapping("findByPrototypeId")
    List<PrototypePsdDto> findByPrototypeId(@RequestParam Long prototypeId);
}
