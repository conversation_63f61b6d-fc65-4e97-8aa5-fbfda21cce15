package com.sdsdiy.productapi.api.prototype;

import com.sdsdiy.productdata.dto.prototype.PrototypeLayerDto;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/4
 */
@RequestMapping("microservice/prototypeLayer")
public interface PrototypeLayerApi {

    @GetMapping("findByPrototypeId")
    List<PrototypeLayerDto> findByPrototypeId(@RequestParam Long prototypeId);

    @PostMapping("findByPrototypeIds")
    List<PrototypeLayerDto> findByPrototypeIds(@RequestBody List<Long> prototypeIds);
}
