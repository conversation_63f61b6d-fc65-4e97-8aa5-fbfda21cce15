package com.sdsdiy.productapi.api.product;


import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.productdata.dto.price.ProductPlatformPriceEditDTO;
import com.sdsdiy.productdata.dto.price.ProductPlatformPriceReqDTO;
import com.sdsdiy.productdata.dto.price.ProductVariantPlatformPriceDTO;
import com.sdsdiy.productdata.dto.product.ProductIdCheckDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductUpdateOnSalePriceReqDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductUpdateOnSalePriceRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


/**
 * <p>
 * 分销产品租户设置的价格 API
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/18
 */
@Api(tags = "分销产品租户设置的价格")
@RequestMapping("microservice/distributionProductTenantPriceApi")
public interface DistributionProductTenantPriceApi {

    @ApiOperation("平台价")
    @PostMapping("variantTenantPlatformPrice")
    BaseListDto<ProductVariantPlatformPriceDTO> variantTenantPlatformPrice(@RequestBody ProductPlatformPriceReqDTO reqDto);

    @ApiOperation("设置-平台价")
    @PostMapping("updateVariantTenantPlatformPrice")
    void updateVariantTenantPlatformPrice(@RequestBody ProductPlatformPriceEditDTO editDTO);

    @ApiOperation("促销价")
    @PostMapping("variantTenantOnSalePrice")
    ProductUpdateOnSalePriceRespDTO variantTenantOnSalePrice(@RequestBody ProductIdCheckDTO reqDTO);

    @ApiOperation("设置-促销价")
    @PostMapping("updateVariantTenantOnSalePrice")
    void updateVariantTenantOnSalePrice(@RequestBody ProductUpdateOnSalePriceReqDTO reqDto);
}