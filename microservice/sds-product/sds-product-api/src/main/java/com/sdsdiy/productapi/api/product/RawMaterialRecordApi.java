package com.sdsdiy.productapi.api.product;

import com.sdsdiy.productdata.vo.RawMaterialRecordVO;
import com.sdsdiy.productdata.vo.RawMaterialStockExcelVO;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

@RequestMapping("microservice/RawMaterialApi")
public interface RawMaterialRecordApi {
    @GetMapping("/getExportExcel")
    List<RawMaterialStockExcelVO> exportExcel(@RequestParam("factoryId") Long factoryId,
                                              @RequestParam(value = "keyword", required = false) String keyword,
                                              @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                                              @RequestParam(value = "startTime", required = false) Date startTime,
                                              @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                                              @RequestParam(value = "endtime", required = false) Date endTime,
                                              @RequestParam(value = "operator", required = false) Long operator,
                                              @RequestParam(value = "sort") String sort);


    /**
     * todo lujp 啥时候替换掉盘点列表接口
     * @param factoryId
     * @param keyword
     * @param startTime
     * @param endTime
     * @param operator
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/rawMaterialStockRecord")
    RawMaterialRecordVO record(@RequestParam("factoryId") Long factoryId,
                               @RequestParam(value = "keyword", required = false) String keyword,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                               @RequestParam(value = "startTime", required = false) Date startTime,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                               @RequestParam(value = "endtime", required = false) Date endTime,
                               @RequestParam(value = "operator", required = false) Long operator,
                               @RequestParam(value = "page") Integer page,
                               @RequestParam(value = "size") Integer size);
}
