package com.sdsdiy.productapi.api.font;

import com.sdsdiy.productapi.dto.font.FontFileRespDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 字体表(FontDict)表api层
 *
 * <AUTHOR>
 * @since 2024-02-04 10:55:30
 */
@RequestMapping("/microservice/fontDicts")
public interface FontDictApi {

    @ApiOperation("字体")
    @GetMapping("files")
    List<FontFileRespDto> fontFiles();
}

