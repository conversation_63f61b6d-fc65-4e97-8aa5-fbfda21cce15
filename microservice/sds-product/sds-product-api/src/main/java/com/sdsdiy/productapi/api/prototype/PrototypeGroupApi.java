package com.sdsdiy.productapi.api.prototype;

import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.common.base.valid.ValidateGroup;
import com.sdsdiy.productdata.dto.prototype.*;
import com.sdsdiy.productdata.dto.prototype.add.PrototypeGroupByProductParam;
import com.sdsdiy.productdata.dto.prototype.query.PrototypeGroupQueryDTO;
import com.sdsdiy.productdata.vo.prototype.ProductManuscriptVo;
import com.sdsdiy.productdata.vo.prototype.ProductPrototypeGroupVo;
import com.sdsdiy.productdata.vo.prototype.PrototypeGroupVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 原形模板组 api调用层
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */

@RequestMapping("/microservice/prototypeGroup")
public interface PrototypeGroupApi {

    @PostMapping("/page")
    PageResult<ProductPrototypeGroupVo> page(@RequestBody @Validated(ValidateGroup.Page.class) PrototypeGroupDto dto);

    @ApiOperation(value = "查询一个模板组全部内容", notes = "")
    @GetMapping("/getById")
    PrototypeGroupVo getById(@RequestParam Long id);

    @ApiOperation(value = "新增建模及绑定")
    @PostMapping("/addAndBind")
    Object addAndBind(@Validated(ValidateGroup.Update.class) @RequestBody PrototypeGroupDto add);

    @ApiOperation(value = "复制建模组")
    @PostMapping("/copy")
    Object copy(@RequestBody PrototypeGroupCopyDto prototypeGroupCopyDto);

    @ApiOperation(value = "查询模板生产稿件", notes = "")
    @GetMapping("/findListProductionManuscript")
    List<ProductManuscriptVo> findListProductionManuscript(@RequestParam Long groupId);

    @ApiOperation(value = "更新")
    @PutMapping("/update")
    Object update(@Validated(ValidateGroup.Update.class) @RequestBody PrototypeGroupDto update);

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteBatch")
    int deleteBatch(@NotEmpty @RequestBody List<Long> ids);

    /**
     * 用下面dto那个
     */
    @GetMapping("/prototypeGroup")
    @Deprecated
    List<PrototypeGroupDto> getPrototypeGroup(@RequestParam Long productId, @RequestParam Long tenantId, @RequestParam Long merchantId, @RequestParam(required = false) Long userId, @RequestParam(value = "childId", required = false) Long childId);

    @ApiOperation("租户的模板组信息")
    @PostMapping("/getPrototypeGroup")
    List<PrototypeGroupDto> getPrototypeGroup(@RequestBody PrototypeGroupQueryDTO queryDTO);

    @ApiOperation(value = "获取产品母体下的所有模板组列表", notes = "")
    @GetMapping("/getByProductParentId")
    List<PrototypeGroupVo> getByProductParentId(@RequestParam Long tenantId, @RequestParam Long productParentId, @RequestParam Boolean finshState, @RequestParam Boolean allBind);

    @ApiOperation(value = "查询产品模板组建模绑定情况", notes = "")
    @GetMapping("/getProductBindGroup")
    List<PrototypeGroupVo> listProductBindGroup(@RequestParam Long tenantId, @RequestParam Long productParentId);

    @ApiOperation("多个产品的模板组信息")
    @GetMapping("/productsPrototypeGroup")
    List<UserDefaultPrototypeGroupDto> getProductsPrototypeGroup(@RequestParam String productIds, @RequestParam(value = "tenantId", required = false) Long tenantId, @RequestParam Long merchantId, @RequestParam Long userId);

    @ApiOperation(value = "查询创建模板接口")
    @GetMapping("/getBind")
    PrototypeGroupVo getBind(@RequestParam Long productId);

    @ApiOperation(value = "引用模板组的内容", notes = "")
    @GetMapping("/cite")
    PrototypeGroupVo cite(@RequestParam Long id, @RequestParam Long productId);

    @ApiOperation(value = "单独更新模板组")
    @PutMapping("/updateOne")
    public Object updateOne(@RequestBody PrototypeGroupOneDto update);


    @ApiOperation(("根据产品id查询"))
    @GetMapping("findByProductId")
    List<PrototypeGroupVo> findByProductId(@RequestParam Long tenantId, @RequestParam Long productId);

    @ApiOperation("根据模板id查询模板组")
    @GetMapping("getGroupByPrototypeId")
    PrototypeGroupDto getGroupByPrototypeId(@RequestParam Long prototypeId);

    @ApiOperation("获取产品对应的蒙版图")
    @GetMapping("/merchants/{merchantId}/productMaskingMap")
    Map<Long, List<PrototypeGroupMaskingDto>> getProductMaskingMap(@PathVariable("merchantId") Long merchantId, @SpringQueryMap BaseListReqDto productParentIds);


    @ApiOperation("根据模板ids查询模板组s")
    @PostMapping("getGroupsByPrototypeIds")
    List<PrototypeGroupDto> getGroupsByPrototypeIds(@RequestBody List<Long> prototypeIds);

    @PostMapping("/addByParent")
    void addByParent(@RequestBody PrototypeGroupByProductParam param);

    @ApiOperation("获取最新的模版组")
    @GetMapping("/products/{productParentId}/finish/lastOne")
    PrototypeGroupDto getFinishLastOneByProductParent(@PathVariable(value = "productParentId") Long productParentId);
    @ApiOperation("获取最新的模版组")
    @GetMapping("/tenants/{tenantId}/products/{productParentId}/finish")
    List<PrototypeGroupDto> getFinishGroupsByProductParent(@PathVariable(value = "tenantId") Long tenantId,
                                                         @PathVariable(value = "productParentId") Long productParentId);
}