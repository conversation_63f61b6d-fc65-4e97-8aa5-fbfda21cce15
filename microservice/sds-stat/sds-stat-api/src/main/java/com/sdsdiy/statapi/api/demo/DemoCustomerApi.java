package com.sdsdiy.statapi.api.demo;

import com.sdsdiy.statapi.dto.demo.DemoCustomerDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 案例客户api
 * @Author:zjy
 */
public interface DemoCustomerApi {

    /**
     * 通过获取指定客户
     * @param id
     * @return
     */
    @GetMapping("/demoCustomers/{id}")
    DemoCustomerDto getDemoCustomersById(@PathVariable(value = "id") Integer id, @RequestParam("fields") String fields);

}
