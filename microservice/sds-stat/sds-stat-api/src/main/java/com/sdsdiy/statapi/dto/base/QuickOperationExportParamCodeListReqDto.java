package com.sdsdiy.statapi.dto.base;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/28 14:17
 */
@Data
public class QuickOperationExportParamCodeListReqDto {
    private Long userId;
    /**
     * 有多个条件
     */
    private List<QuickOperationExportParamCodeReqDto> quickOperationExportParamCodeReqDtoList;

    public String getParamValue(String code){
        if(quickOperationExportParamCodeReqDtoList == null){
            return null;
        }
        for (QuickOperationExportParamCodeReqDto quickOperationExportParamCodeReqDto : quickOperationExportParamCodeReqDtoList) {
            if(quickOperationExportParamCodeReqDto.getParamCodeName().equals(code)){
                return quickOperationExportParamCodeReqDto.getParamCodeValue();
            }
        }
        return null;
    }
}
