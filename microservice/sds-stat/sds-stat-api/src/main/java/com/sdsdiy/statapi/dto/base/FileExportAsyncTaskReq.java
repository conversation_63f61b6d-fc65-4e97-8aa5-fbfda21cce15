package com.sdsdiy.statapi.dto.base;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

/**
 * 导出文件异步任务表(FileExportAsyncTask)ReqDto类
 *
 * <AUTHOR>
 * @since 2024-10-21 12:15:34
 */
@Data
public class FileExportAsyncTaskReq implements Serializable {
    private static final long serialVersionUID = 659688222332060301L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("processing=进行中 fail=失败 completed=完成")
    private String status;
    @ApiModelProperty("pod saas crm factory merchant")
    private String platform;
    @ApiModelProperty("pod=租户id merchant=merchant_id")
    private Long platformId;
    @ApiModelProperty("类型")
    private String businessType;
    @ApiModelProperty("业务描述")
    private String businessDesc;
    @ApiModelProperty("文件名")
    private String fileName;
    @ApiModelProperty("文件md5")
    private String fileMd5;
    @ApiModelProperty("文件类型")
    private String fileType;
    @ApiModelProperty("文件链接")
    private String fileUrl;
    @ApiModelProperty("是否下载0否1是")
    private Integer isDownload;
    @ApiModelProperty("失败日志信息")
    private String failMessage;
    @ApiModelProperty("日志追踪")
    private String logTraceId;
    @ApiModelProperty("是否删除0否1是")
    private Integer isDelete;
    @ApiModelProperty("创建者id")
    private Long createUid;
    @ApiModelProperty("更新者id")
    private Long updateUid;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
