package com.sdsdiy.statapi.api;
import com.sdsdiy.statapi.dto.FactoryOrderWaitingProcessingStatistics;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/***
*工厂订单
* <AUTHOR>
* @date 2020/7/8 19:13
*/
@RequestMapping("/microservice/factory_orders_waiting_processing_statistics")
public interface FactoryOrderWaitingProcessingStatisticsApi {



	@GetMapping("")
	FactoryOrderWaitingProcessingStatistics waitingProcessingStatistics(@RequestParam(value = "factoryId") Long factoryId);

}
