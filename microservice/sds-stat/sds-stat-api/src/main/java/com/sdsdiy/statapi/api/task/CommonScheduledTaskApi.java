package com.sdsdiy.statapi.api.task;

import com.sdsdiy.statdata.dto.task.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 通用定时任务 API
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/20
 */
@RequestMapping("mc/CommonScheduledTaskApi")
public interface CommonScheduledTaskApi {

    @PostMapping("/createTask")
    void createTask(@RequestBody CommonScheduledTaskCreateDTO dto);

    @PostMapping("/createIfNotExist")
    void createIfNotExist(@RequestBody CommonScheduledTaskCreateDTO dto);

    @DeleteMapping("/deleteTask")
    void deleteTask(@RequestBody CommonScheduledTaskDeleteDTO deleteDTO);

    @PostMapping("/queryTask")
    List<CommonScheduledTaskDTO> queryTask(@RequestBody CommonScheduledTaskQueryDTO queryDTO);
    @GetMapping("/findDtoById")
    CommonScheduledTaskDTO findDtoById(@RequestParam Long id);

    @PostMapping("/findDtoByDataIds")
    List<CommonScheduledTaskDTO> findDtoByDataIds(@RequestBody CommonScheduledTaskQueryDataIdDTO dto);
}