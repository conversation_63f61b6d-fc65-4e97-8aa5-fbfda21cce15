package com.sdsdiy.statapi.dto.demo;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

/***
 * 任务Dto 包括 任务基本
 *             任务奖励
 * @Description
 * <AUTHOR>
 * @Date 2020/6/11、16:57
 */
@Data
public class DemoTaskAwardDto extends BaseDTO {

    @DtoDefault
    private Long    id;
    @DtoDefault
    private Integer    taskId;
    @DtoDefault
    private Integer    couponId;

    // 优惠券信息
    @DtoBind(selfField = "couponId",relateField = "id",showField = "name,desp,amount", provider = "com.sdsdiy.statimpl.relationprovider.DemoCouponDtoRelationProvider")
    DemoCouponDto couponInfo;

}
