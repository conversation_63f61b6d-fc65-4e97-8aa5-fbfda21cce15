package com.sdsdiy.statapi.dto.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.statapi.dto.base
 * @Author: lujp
 * @CreateTime: 2022-12-07
 * @Description:
 * @Version: 1.0
 */
@Data
public class MapResponseDTO {

    @JsonProperty("groupName")
    @NotNull
    @DtoDefault
    @ApiModelProperty(value = "groupName")
    private String groupName;

    @JsonProperty("list")
    @NotNull
    @DtoDefault
    @ApiModelProperty(value = "list")
    private List<QuickOperationRespDto> list;
}