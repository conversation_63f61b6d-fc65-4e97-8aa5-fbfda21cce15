package com.sdsdiy.statapi.api.material;

import com.sdsdiy.materialdata.dto.requirement.MaterialRequirementWinBidNumDTO;
import com.sdsdiy.statapi.dto.material.MaterialRequirementStatDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/9
 */
@Api(tags = "素材需求-统计")
@RequestMapping("/microservice/materialRequirementStatApi")
public interface MaterialRequirementStatApi {
    @ApiOperation("新增")
    @PostMapping("create")
    void create(@RequestBody MaterialRequirementStatDTO dto);

    @ApiOperation("浏览数(pv) 累加")
    @PutMapping("addVisitNum/{id}")
    void addVisitNum(@PathVariable Long id, @RequestParam Integer visitNum);

    @ApiOperation("报名数 更新")
    @PutMapping("updateSignNum/{id}")
    void updateSignNum(@PathVariable Long id, @RequestParam Integer signNum);

    @ApiOperation("投稿素材数 累加")
    @PutMapping("updateMaterialNum/{id}")
    void addMaterialNum(@PathVariable Long id, @RequestParam Integer materialNum);

    @ApiOperation("中标数 更新")
    @PutMapping("updateWinBidNum/{id}")
    void updateWinBidNum(@PathVariable Long id, @RequestParam Integer winBidNum);

    @ApiOperation("中标数 批量更新")
    @PutMapping("updateWinBidNumBatch")
    void updateWinBidNumBatch(@RequestBody List<MaterialRequirementWinBidNumDTO> list);

    @ApiOperation("获取单个")
    @GetMapping("getDto/{id}")
    MaterialRequirementStatDTO getDto(@PathVariable Long id);

    @ApiOperation("获取list")
    @PostMapping("listDtoByIds")
    List<MaterialRequirementStatDTO> listDtoByIds(@RequestBody List<Long> ids);
}
