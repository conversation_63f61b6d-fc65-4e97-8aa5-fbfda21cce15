package com.sdsdiy.statapi.vo.issuingbayboard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.statapi.vo
 * @Author: lujp
 * @CreateTime: 2023-08-03
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class IssuingBayBoardUserRankVo {
    @ApiModelProperty("质检排行")
    private List<IssuingBoardUserRankVO> qcRankList;
    @ApiModelProperty("打单排行")
    private List<IssuingBoardUserRankVO> printOrderRankList;
    @ApiModelProperty("分拣排行")
    private List<IssuingBoardUserRankVO> classifyOrderRankList;
}