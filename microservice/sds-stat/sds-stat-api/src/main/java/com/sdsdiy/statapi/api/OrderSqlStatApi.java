package com.sdsdiy.statapi.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/orders")
public interface OrderSqlStatApi {

    @GetMapping("overTimesQty")
    public Integer overTimesQty();

    @GetMapping("willOverTimesQty")
    public Integer willOverTimesQty();

    @GetMapping("countCheckOutNoPrint")
    public Integer countCheckOutNoPrint();

    @GetMapping("accOverTimesQty")
    public Integer accOverTimesQty();
    @GetMapping("countPrintNoShip")
    public Integer countPrintNoShip();
}
