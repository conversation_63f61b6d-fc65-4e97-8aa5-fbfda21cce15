package com.sdsdiy.statapi.api.issuing.bay;


import com.sdsdiy.common.base.entity.dto.BaseDownloadDTO;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.statdata.dto.issuing.bay.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 仓库工作量日统计 API
 * </p>
 *
 * <AUTHOR>
 * @since 2023/08/07
 */
@RequestMapping("/microservice/IssuingBayStatApi")
public interface IssuingBayStatApi {

    @ApiOperation("今日数据")
    @GetMapping("/issuingBayToday")
    PodIssuingBayTodayDTO issuingBayToday(@RequestParam("issuingBayId") Long issuingBayId);

    @ApiOperation("吞吐及时率-列表")
    @PostMapping("throughputList")
    IssuingBayThroughputDayRespDTO throughputList(@RequestBody IssuingBayThroughputDayReqDTO reqDTO);

    @ApiOperation("吞吐及时率-导出")
    @PostMapping("throughputExport")
    BaseDownloadDTO throughputExport(@RequestBody IssuingBayThroughputDayReqDTO reqDTO);

    @ApiOperation("工作量-列表")
    @PostMapping("workloadList")
    PageResultDto<IssuingBayWorkloadDayRespDTO> workloadList(@RequestBody IssuingBayWorkloadDayReqDTO reqDTO);

    @ApiOperation("工作量-导出")
    @PostMapping("workloadExport")
    BaseDownloadDTO workloadExport(@RequestBody IssuingBayWorkloadDayReqDTO reqDTO);

    /**
     * 累加
     */
    @PostMapping("workloadAddBatch")
    void workloadAddBatch(@RequestBody List<IssuingBayWorkloadAddDTO> list);

    /**
     * 更新覆盖，一般处理旧数据用
     */
    @PutMapping("workloadUpdateBatch")
    void workloadUpdateBatch(@RequestBody List<IssuingBayWorkloadDayStatDTO> list);

    @PostMapping("statThroughputOldData")
    void statThroughputOldData(@RequestParam("days") Integer days);

    @PostMapping("qcNumStatDataDeal")
    void qcNumStatDataDeal(@RequestParam("from") Long from, @RequestParam("to") Long to);
}