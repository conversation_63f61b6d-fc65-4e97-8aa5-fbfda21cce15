package com.sdsdiy.statapi.api.demo;

import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.statapi.dto.demo.DemoTaskDto;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 案例任务api
 * @Author:zjy
 */
public interface DemoTaskApi {

    /**
     * 获取任务列表Post
     * @param queryParamHelper
     * @return
     */
    @PostMapping("/tasks")
    PageListResultDTO<DemoTaskDto> getList(@SpringQueryMap QueryParamHelper queryParamHelper);

    /**
     * 获取任务列表Get
     * @param fields
     * @return
     */
    @GetMapping("/tasks2")
    PageListResultDTO<DemoTaskDto> getList2(@RequestParam(value = "fields",required = false) String fields);


}
