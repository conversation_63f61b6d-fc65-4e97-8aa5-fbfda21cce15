package com.sdsdiy.statapi.vo.issuingbayboard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.statapi.vo.issuingbayboard
 * @Author: lujp
 * @CreateTime: 2023-08-03
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class UserRankVO {
    @ApiModelProperty("用户名")
    private String name;
    @ApiModelProperty("时间范围和次数列表")
    private List<DateRangeAndCountVO> rangeAndCount;
}