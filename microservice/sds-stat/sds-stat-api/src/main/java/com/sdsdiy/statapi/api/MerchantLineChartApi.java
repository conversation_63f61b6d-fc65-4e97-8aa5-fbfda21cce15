package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.OrderCountDto;
import com.sdsdiy.statapi.dto.StatisticsDto;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 商户端-概览-订单统计和素材数统计
 * @Date 14:03 2020/8/28
 */
@RequestMapping("/microservice/statistics")
public interface MerchantLineChartApi {

    /***
     * <AUTHOR>
     * @Description 商户端-概览-订单统计
     * @Date 21:58 2020/8/26
     * @param viewType 查看类型，1：平台，2：账号，3：店铺
     * @param ids 平台id或子账号id或店铺id(仅限有权限查看所有数据用户，不传则查查看类型下所有数据)
     * @param dateType 时间 day-日 week-周 month-月
     * @return java.util.Map<java.lang.String, java.lang.Object>
     */
    @RequestMapping(value = "/ordersLineChart", method = RequestMethod.GET)
    List<OrderCountDto> orderCounts(@RequestParam(value = "viewType") Integer viewType,
                                           @RequestParam(value = "ids") String ids,
                                           @RequestParam(value = "merchantId") Long merchantId,
                                           @RequestParam(value = "storeIds") String storeIds,
                                           @RequestParam("dateType") String dateType);

    /**
     * @param userIds  用户id，逗号分隔
     * @param dateType 时间，day-日 week-周 month-月
     * @return com.ziguang.base.dto.ResourceStatisticsDto
     * <AUTHOR>
     * @Description 商户端-概览-素材数统计
     * @Date 20:10 2020/8/26
     */
    @RequestMapping(value = "/materials")
    List<StatisticsDto> getMaterialStatistics(@RequestParam(value = "myUserIds") String myUserIds,
                                              @RequestParam(value = "userIds") String userIds,
                                              @RequestParam(value = "merchantId") Long merchantId,
                                              @RequestParam(value = "dateType") String dateType);
}