package com.sdsdiy.statapi.api.crm;



/**
 * <p>
 * crm 业务员日统计 API
 * </p>
 *
 * <AUTHOR>
 * @since 2024/03/27
 */
public interface CrmUserAchievementRecordDayApi {

    //@PostMapping("/inner/CrmUserAchievementRecordDayApi/add")
    //void add(@RequestBody CrmUserAchievementRecordDayDTO dto);

    //@PutMapping("/inner/CrmUserAchievementRecordDayApi/update")
    //void update(@RequestBody CrmUserAchievementRecordDayDTO dto);

    //@GetMapping("/inner/CrmUserAchievementRecordDayApi/get")
    //CrmUserAchievementRecordDayDTO get(@RequestParam Long id);

    //@PostMapping("/inner/CrmUserAchievementRecordDayApi/page")
    //PageListResultDTO<CrmUserAchievementRecordDayDTO> page(@RequestBody BasePageSelect pageSelect);

}