package com.sdsdiy.statapi.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/orders")
public interface ImportOrderSqlStatApi {

    @GetMapping("withoutRelateOrderQty")
    Integer withoutRelateOrderQty(@RequestParam("merchantId")Long merchantId,@RequestParam("storeIds")String storeIds);
}
