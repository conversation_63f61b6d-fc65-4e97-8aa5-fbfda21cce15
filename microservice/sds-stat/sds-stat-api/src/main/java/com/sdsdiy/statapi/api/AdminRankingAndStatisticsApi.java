package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.FactoryRankingRespDto;
import com.sdsdiy.statapi.dto.StatisticsDto;
import com.sdsdiy.statapi.dto.UserPerformanceRankingRespDto;
import com.sdsdiy.statapi.dto.logistics.LogisticsRankingRespDto;
import com.sdsdiy.statapi.vo.issuingbayboard.IssuingBayBoardUserRankVo;
import com.sdsdiy.statapi.vo.issuingbayboard.UserRankExcelVO;
import com.sdsdiy.statapi.vo.issuingbayboard.UserRankVO;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 运营端-概览-订单统计和排行榜
 * @Date 14:03 2020/8/28
 */
@RequestMapping("/microservice/rankingAndSatistics")
public interface AdminRankingAndStatisticsApi {

	@GetMapping("/performanceRanking")
	List<UserPerformanceRankingRespDto> getUserPerformanceRanking(@RequestParam(value = "tenantId", required = false) Long tenantId,
																  @RequestParam(value = "deptId", required = false) Long deptId,
																  @RequestParam(value = "dateType", required = false) String dateType,
																  @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
																  @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime);

	@GetMapping("/orderStatistics")
	List<StatisticsDto> orderStatistics(@RequestParam(value = "dateType") String dateType);

	@GetMapping("/factoryRanking")
	List<FactoryRankingRespDto> getFactoryRanking(@RequestParam(value = "productTypeId",required = false) String productTypeId,
												  @RequestParam(value = "dateType") String dateType);

	@GetMapping("/logisticsRanking")
	List<LogisticsRankingRespDto> logisticsRanking(@RequestParam(value = "dateType", required = false) String dateType,
												   @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
												   @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
												   @RequestParam(value = "issuingBayId", required = false) Long issuingBayId);

	@GetMapping("/issuingBayBoardUserRank")
	IssuingBayBoardUserRankVo  issuingBayBoardUserRank(@RequestParam("issuingBayId") Long issuingBayId,
													   @RequestParam("tenantId") Long tenantId);

	@GetMapping("/userRank")
	List<UserRankVO> userRank(@RequestParam("issuingBayId") Long issuingBayId,
							  @ApiParam("时间范围开始时间") @RequestParam(value = "startTime", required = false) Long startTime,
							  @ApiParam("时间范围结束时间") @RequestParam(value = "endTime", required = false) Long endTime,
							  @ApiParam("分段 month week day") @RequestParam(value = "unit", defaultValue = "day") String unit,
							  @ApiParam("排行类型 质检 quality 打单 printOrder 分拣classifyOrder") @RequestParam("type") String type);

	@GetMapping("/userRank/export")
	List<UserRankExcelVO> export(@RequestParam("issuingBayId") Long issuingBayId,
								 @ApiParam("时间范围开始时间") @RequestParam(value = "startTime", required = false) Long startTime,
								 @ApiParam("时间范围结束时间") @RequestParam(value = "endTime", required = false) Long endTime,
								 @ApiParam("分段 month week day") @RequestParam(value = "unit", defaultValue = "day") String unit,
								 @ApiParam("排行类型 质检 quality 打单 printOrder 分拣classifyOrder") @RequestParam("type") String type);

	@PostMapping("/syncOrderPrintRecord")
	void syncOrderPrintRecord(@RequestParam(value = "startTime") Long startTime,
							  @RequestParam(value = "endTime") Long endTime);

	@PostMapping("/fixRecordTime")
    void fixRecordTime(@RequestParam("errorDayTimestamp") Long errorDayTimestamp,
					   @RequestParam(value = "issuingBayId") Long issuingBayId);
}