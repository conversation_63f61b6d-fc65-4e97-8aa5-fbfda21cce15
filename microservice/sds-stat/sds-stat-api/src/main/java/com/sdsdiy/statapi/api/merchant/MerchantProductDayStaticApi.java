package com.sdsdiy.statapi.api.merchant;

import com.sdsdiy.statdata.dto.query.ProductStaticDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 商户产品每日数据统计Api
 */
@RequestMapping("/microservice/merchantProductDayStatic")
public interface MerchantProductDayStaticApi {

    @GetMapping("/mapStatAllProductByTime")
    Map<Long, ProductStaticDto> mapStatAllProductByTime(@RequestParam() Long startTime, @RequestParam() Long endTime);

}
