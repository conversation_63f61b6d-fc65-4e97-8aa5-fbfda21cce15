package com.sdsdiy.statapi.api;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.orderapi.dto.PreferentialActivityEffectRespDto;
import com.sdsdiy.statapi.dto.PagePreferentialActivityOrderStatReqDto;
import com.sdsdiy.statapi.dto.PreferentialActivityOrderStatDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/16
 */
@RequestMapping("/microservice/preferentia/activity/order/stat")
public interface PreferentialActivityOrderStatApi {

    @GetMapping("")
    PreferentialActivityOrderStatDto findByActivityId(@RequestParam(value = "activityId") Long activityId);

    @PostMapping("")
    void addActivityStat(@RequestBody PreferentialActivityOrderStatDto activityStatDto);

    @PutMapping("")
    void updateActivityStat(@RequestBody PreferentialActivityOrderStatDto activityStatDto);

    @PostMapping("/saveOrUpdate")
    void saveOrUpdate(@RequestBody PreferentialActivityOrderStatDto activityStatDto);

    @ApiOperation("活动趋势列表")
    @GetMapping("/stat")
    List<PreferentialActivityEffectRespDto> statisticsActivity(@RequestParam(value = "type") String type, @RequestParam(value = "activityId", required = false) Long activityId);

    @ApiOperation("活动效果列表")
    @GetMapping("/effect")
    PageListResultRespDto<PreferentialActivityOrderStatDto> activityStatList(@SpringQueryMap PagePreferentialActivityOrderStatReqDto reqDto);

    @ApiOperation("单个活动累计效果")
    @GetMapping("/cumulative/effect")
    PreferentialActivityOrderStatDto activityCumulativeEffect(@RequestParam(value = "activityId") Long activityId);
}
