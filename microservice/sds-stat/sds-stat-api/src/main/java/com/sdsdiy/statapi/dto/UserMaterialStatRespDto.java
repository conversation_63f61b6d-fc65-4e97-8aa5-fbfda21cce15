package com.sdsdiy.statapi.dto;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

/**
 * 素材统计表(UserMaterialStatRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-09-07 18:30:12
 */
@Data
public class UserMaterialStatRespDto extends BaseDTO {

    @DtoDefault
    private Long merchantId;
    @DtoDefault
    private Long userId;
    @DtoDefault
    private Long date;
    @DtoDefault
    private Double length;
    @DtoDefault
    private Integer count;

}