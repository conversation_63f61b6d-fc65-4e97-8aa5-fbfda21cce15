package com.sdsdiy.statapi.api.material;

import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.statapi.dto.material.MaterialStatReqDto;
import com.sdsdiy.statapi.dto.material.MaterialStatRespDto;
import com.sdsdiy.statdata.dto.material.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 素材统计表(MaterialStat)表api接口
 * <AUTHOR>
 * @since 2022-02-21 14:26:13
 */
@RequestMapping("/microservice/materialStat")
public interface MaterialStatApi {

    @GetMapping("/list")
    @ApiOperation("根据条件查询素材统计信息列表")
    List<MaterialStatRespDto> getList(@SpringQueryMap MaterialStatReqDto materialStatReqDto);


    @PostMapping("materialAnalyzeStat")
    PageResultDto<MerchantMaterialStatRespDTO> materialAnalyzeStat(@RequestBody MerchantMaterialStatReqDTO reqDTO);

    @PostMapping("materialDesignAnalyze")
    PageResultDto<MerchantMaterialDesignAnalyzeRespDTO> materialDesignAnalyze(@RequestBody MerchantMaterialDesignAnalyzeReqDTO reqDTO);

    @PostMapping("materialOrderAnalyze")
    PageResultDto<MerchantMaterialOrderAnalyzeRespDTO> materialOrderAnalyze(@RequestBody MerchantMaterialOrderAnalyzeReqDTO reqDTO);
}