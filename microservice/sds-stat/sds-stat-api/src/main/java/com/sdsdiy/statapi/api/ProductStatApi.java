package com.sdsdiy.statapi.api;

import com.sdsdiy.orderapi.dto.OrderStatDTO;
import com.sdsdiy.statapi.dto.HotProductStatDTO;
import com.sdsdiy.statapi.dto.ProductDesignStatDTO;
import com.sdsdiy.statapi.dto.ProductSummaryDTO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/product")
public interface ProductStatApi {

    /**
     * 产品摘要 （总设计数，总产品数）
     *
     * @return
     */
    @GetMapping("/product_summary")
    @ResponseBody
    ProductSummaryDTO productSummary();


    /**
     * 根据产品id 获取 7天设计数
     *
     * @return
     */
    @GetMapping("/design/nums")
    @ResponseBody
    List<ProductDesignStatDTO> statDesignByProductIds(@RequestParam(value = "pids") String ids);

    /**
     * 获取 15天热卖商品
     *
     * @return
     */
    @GetMapping("/hot/nums")
    @ResponseBody
    List<HotProductStatDTO> getHotProduct(@RequestParam(value = "size",required = false)Integer size);


}
