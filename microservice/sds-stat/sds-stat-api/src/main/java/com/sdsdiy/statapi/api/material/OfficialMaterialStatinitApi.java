package com.sdsdiy.statapi.api.material;

import com.sdsdiy.statapi.dto.material.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * shanbin_sun
 * 官方素材旧数据处理
 */
@Api(tags = "官方素材-旧数据处理")
@RequestMapping("/microservice/officialMaterialStatInit")
public interface OfficialMaterialStatinitApi {


    @ApiOperation("官方素材设计数旧数据处理")
    @PostMapping("/ofDesignNumoldData")
    void  ofDesignNumoldData(@RequestBody OfDesignNumoldDataDto ofDesignNumoldDataDto );
}
