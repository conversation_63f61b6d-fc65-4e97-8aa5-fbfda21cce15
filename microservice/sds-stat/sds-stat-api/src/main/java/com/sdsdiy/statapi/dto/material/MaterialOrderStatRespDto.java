package com.sdsdiy.statapi.dto.material;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 素材订单统计(MaterialOrderStat)RespDto类
 * <AUTHOR>
 * @since 2022-02-18 14:18:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MaterialOrderStat对象", description = "素材订单统计结果")
public class MaterialOrderStatRespDto implements Serializable {
    private static final long serialVersionUID = -1033473203120060829L;
    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "素材id")
    @DtoDefault
    private Long materialId;
    @ApiModelProperty(value = "订单id")
    private Long orderId;
    @ApiModelProperty(value = "订单编号")
    @DtoDefault
    private String orderNo;
    @ApiModelProperty(value = "第三方订单号")
    @DtoDefault
    private String outOrderNo;
    @ApiModelProperty(value = "收件人")
    @DtoDefault
    private String consignee;
    @ApiModelProperty(value = "移动电话")
    @DtoDefault
    private String mobilePhone;
    @ApiModelProperty(value = "邮政编码")
    @DtoDefault
    private String postcode;
    @ApiModelProperty(value = "国家")
    @DtoDefault
    private String country;
    @ApiModelProperty(value = "省")
    @DtoDefault
    private String province;
    @ApiModelProperty(value = "市")
    @DtoDefault
    private String city;
    @ApiModelProperty(value = "详细地址")
    @DtoDefault
    private String detail;
    @ApiModelProperty(value = "出货数")
    @DtoDefault
    private Integer shipmentNum;
    @ApiModelProperty(value = "支付时间")
    @DtoDefault
    private Long payTime;
    @ApiModelProperty(value = "支付日期")
    private Date payDate;

    public void dataConvert(){
        if(this.payTime != null){
            setPayDate(new Date(this.payTime));
        }
    }
}
