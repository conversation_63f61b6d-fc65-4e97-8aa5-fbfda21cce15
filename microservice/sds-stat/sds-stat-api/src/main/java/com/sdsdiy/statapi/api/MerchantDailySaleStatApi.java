package com.sdsdiy.statapi.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025/3/10
 */
@RequestMapping("/microservice/MerchantDailySaleStat")
public interface MerchantDailySaleStatApi {

    @GetMapping()
    void merchantSevenSale();

    @PutMapping()
    void saveMerchantSaleStepPeriod();

    @GetMapping("/saveHistoryData")
    void saveHistoryData(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);

    @GetMapping("/deleteStatData")
    void deleteStatData(@RequestParam("number") Integer number);
}
