package com.sdsdiy.statapi.dto.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/26 12:05
 */
@Data
public class QuickOperationExportParamCodeReqDto {
    @JsonProperty("paramCodeName")
    @DtoDefault
    @ApiModelProperty(value = "paramCode对应的值")
    private String paramCodeName;

    @JsonProperty("paramCodeValue")
    @DtoDefault
    @ApiModelProperty(value = "用户填的值")
    private String paramCodeValue;
}
