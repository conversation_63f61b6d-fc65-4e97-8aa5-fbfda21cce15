package com.sdsdiy.statapi.dto.base;

import java.util.Date;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * crm 月度绩效统计明细(CrmUserAchievementDetailedDto)RespDto类
 *
 * <AUTHOR>
 * @since 2021-08-12 20:14:59
 */
@Data
public class CrmUserAchievementDetailedDto {
    /**
     * 主键id
     */
    @DtoDefault
    private Long id;
    /**
     * 类型 BuyMember
     * <p>
     * 购买会员  BuyDomainName 购买域名  order 平台订单   SelfDeliveryOrder 自发货订单  orderAfterSale 平台订单售后 SelfDeliveryOrderAfterSale 自发货订单售后
     */
    @DtoDefault
    private String type;
    /**
     * 商户id
     */
    @DtoDefault
    private Long merchantId;
    /**
     * 商户名称
     */
    @DtoDefault
    private String merchantName;
    /**
     * 备注
     */
    @DtoDefault
    private String remarks;
    /**
     * 收支类型 收入  income  支出 expenditure
     */
    @DtoDefault
    private String incomeExpenditureType;
    /**
     * 创建时间
     */
    @DtoDefault
    private Date createTime;
    /**
     * 业务员id
     */
    @DtoDefault
    private Long crmUserId;

}
