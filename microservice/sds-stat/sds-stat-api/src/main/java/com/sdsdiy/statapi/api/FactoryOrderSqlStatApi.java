package com.sdsdiy.statapi.api;

import com.sdsdiy.orderdata.dto.FactoryOrderStatRespDTO;
import com.sdsdiy.orderdata.dto.factory.order.FactoryOrdersStaticReqDto;
import com.sdsdiy.orderdata.dto.factory.order.FactoryOrdersStaticRespDto;
import org.springframework.web.bind.annotation.*;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/factory_orders")
public interface FactoryOrderSqlStatApi {
    @GetMapping("overTimesQty")
    Integer overTimesQty();

    @GetMapping("countShipNoCheck")
    Integer countShipNoCheck();

    @GetMapping("inManuscript")
    Integer inManuscript();

    @GetMapping("overTimesQtyByFactoryId")
    Integer overTimesQtyByFactoryId(@RequestParam("factoryId") Long factoryId);


    @GetMapping("inAfterServiceQty")
    Integer inAfterServiceQty(@RequestParam("factoryId") Long factoryId);

    @GetMapping("confirmWaring")
    Integer confirmWaring(@RequestParam("factoryId") Long factoryId, @RequestParam(value = "taskId", required = false) Long taskId);

    @GetMapping("shipWaring")
    Integer shipWaring(@RequestParam("factoryId") Long factoryId, @RequestParam(value = "taskId", required = false) Long taskId);

    @GetMapping("willOverTimesQtyByFactoryId")
    Integer willOverTimesQtyByFactoryId(@RequestParam("factoryId") Long factoryId);

    @GetMapping("willOverTimesQtyByFactoryIdAndStatus")
    Integer willOverTimesQtyByFactoryIdAndStatus(@RequestParam("factoryId") Long factoryId, @RequestParam("status") Integer status, @RequestParam(value = "taskId", required = false) Long taskId);

    @PostMapping
    FactoryOrdersStaticRespDto statFactoryOrderWarnings(@RequestBody FactoryOrdersStaticReqDto params);


    @GetMapping("inProductStat")
    FactoryOrderStatRespDTO inProductStat(@RequestParam("factoryId") Long factoryId);

    @GetMapping("finishStat")
    FactoryOrderStatRespDTO finishStat(@RequestParam("factoryId") Long factoryId);

    @GetMapping("afterServiceRefundStat")
    FactoryOrderStatRespDTO afterServiceRefundStat(@RequestParam("factoryId") Long factoryId);

    @GetMapping("afterServiceResendStat")
    FactoryOrderStatRespDTO afterServiceResendStat(@RequestParam("factoryId") Long factoryId);

    @GetMapping("compensationStat")
    FactoryOrderStatRespDTO compensationStat(@RequestParam("factoryId") Long factoryId);

}
