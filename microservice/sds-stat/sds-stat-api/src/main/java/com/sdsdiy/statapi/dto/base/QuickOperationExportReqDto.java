package com.sdsdiy.statapi.dto.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/25 18:14
 */
@Data
public class QuickOperationExportReqDto {
    /**
     * 名称
     */
    @JsonProperty("name")
    @NotNull
    @DtoDefault
    @ApiModelProperty(value = "name")
    private String name;
    /**
     * sql语句
     */
    @ApiModelProperty(value = "系统参数")
    private String systemParam;
    /**
     * sql语句
     */
    @ApiModelProperty(value = "执行类型")
    @NotNull
    private String executeType;

    @JsonProperty("groupId")
    @DtoDefault
    @ApiModelProperty(value = "groupId")
    private Long groupId;

    @JsonProperty("groupName")
    @DtoDefault
    @ApiModelProperty(value = "groupName")
    private String groupName;

    @JsonProperty("quickOperationExportParamReqDtoList")
    @DtoDefault
    @ApiModelProperty(value = "导出条件")
    private List<QuickOperationExportParamReqDto> quickOperationExportParamReqDtoList;
}

