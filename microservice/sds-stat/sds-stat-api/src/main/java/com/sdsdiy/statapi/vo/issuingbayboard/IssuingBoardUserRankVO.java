package com.sdsdiy.statapi.vo.issuingbayboard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.statapi.vo.issuingbayboard
 * @Author: lujp
 * @CreateTime: 2023-08-03
 * @Description:
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class IssuingBoardUserRankVO {
    @ApiModelProperty("员工名")
    private String name;
    @ApiModelProperty("今日数量")
    private Integer quantity;
    @ApiModelProperty("过去七天趋势")
    private List<Integer> lastSevenDayQuantity;
}