package com.sdsdiy.statapi.dto.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/25 16:30
 */
@Data
public class QuickOperationExportListRespDto {
    @JsonProperty("id")
    @DtoDefault
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 名称
     */
    @JsonProperty("name")
    @NotNull
    @DtoDefault
    @ApiModelProperty(value = "name")
    private String name;

    /**
     * 名称
     */
    @JsonProperty("groupName")
    @NotNull
    @DtoDefault
    @ApiModelProperty(value = "groupName")
    private String groupName;


    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    @DtoDefault
    @ApiModelProperty(value = "createTime")
    private Date createTime;
}
