package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.BlockingDetailResp;
import com.sdsdiy.statapi.dto.BlockingStatisticsRespDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;


/**
 * @author: bin_lin
 * @date: 2020/9/1 21:37
 * @desc:
 */
@RequestMapping("/microservice/order/logistics")
public interface OrderLogisticsStatApi {
    @GetMapping("/blocking/statistics")
    BlockingStatisticsRespDto blockingStatistics();

    @GetMapping("/blocking/detail")
    List<BlockingDetailResp> blockingDetail();

    @GetMapping("/blocking/applyCarriageNoFailNum")
    Integer applyCarriageNoFailNum();

}
