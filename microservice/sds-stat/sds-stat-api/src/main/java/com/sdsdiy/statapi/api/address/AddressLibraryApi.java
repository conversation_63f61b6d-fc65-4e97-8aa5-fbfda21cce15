package com.sdsdiy.statapi.api.address;

import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.statdata.dto.address.AddressLibraryDTO;
import com.sdsdiy.statdata.dto.address.AddressLibrarySaveDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 地址库 API
 * </p>
 *
 * <AUTHOR>
 * @since 2025/01/02
 */
@RequestMapping("mc/AddressLibraryApi")
public interface AddressLibraryApi {

    @PostMapping("/save")
    void save(@RequestBody AddressLibraryDTO dto);

    @PostMapping("/saveDtoBatch")
    void saveDtoBatch(@RequestBody AddressLibrarySaveDTO saveDTO);

    @GetMapping("/findDtoByCode")
    AddressLibraryDTO findDtoByCode(
            @RequestParam String type
            , @RequestParam String code
            , @RequestParam String fields);

    @PostMapping("/findDtoByCodes")
    List<AddressLibraryDTO> findDtoByCodes(
            @RequestParam String type
            , @RequestBody BaseListQueryDTO<String> reqDTO);

    @GetMapping("/findCodeByNameOrDetail")
    List<String> findCodeByNameOrDetail(@RequestParam String type, @RequestParam String keyword);

    @PostMapping("/findNameByCodes")
    Map<String, String> findNameByCodes(
            @RequestParam String type
            , @RequestBody BaseListQueryDTO<String> reqDTO);
}