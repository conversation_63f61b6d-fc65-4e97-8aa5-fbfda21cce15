package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.MerchantProductPeriodicallyDto;
import com.sdsdiy.statapi.dto.input.HotParamInputDTO;
import com.sdsdiy.statapi.dto.input.StatParamInputDTO;
import com.sdsdiy.statapi.dto.mq.UserOptDto;
import com.sdsdiy.statapi.bo.ProductSummaryBO;
import com.sdsdiy.statapi.vo.StatVO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat")
public interface StatApi {

    /**
     * ping
     *
     * @return
     */
    @GetMapping("/ping")
    @ResponseBody
    Map<String, Object> ping();


    /**
     * 用户操作记录
     *
     * @param userOptDto
     * @return
     */
    @GetMapping("/onUserOpt")
    @ResponseBody
    String onUserOpt(@SpringQueryMap UserOptDto userOptDto);

    /**
     * 获取系统菜单
     *
     * @return
     */
    @GetMapping("/menus")
    @ResponseBody
    Map getMenu();

    /**
     * 注册商户统计
     */
    @GetMapping("/merchants")
    @ResponseBody
    StatVO registerMerchantStat(@SpringQueryMap @Valid StatParamInputDTO statParamInputDTO);

    /**
     * 登录用户统计
     */
    @GetMapping("/merchant_login_records")
    @ResponseBody
    StatVO loginUsersStat(@SpringQueryMap @Valid StatParamInputDTO statParamInputDTO);

    /**
     * 合成统计
     *
     * @param statParamInputDTO
     * @return
     */
    @GetMapping("/design_task")
    @ResponseBody
    StatVO designTaskStat(@SpringQueryMap @Valid StatParamInputDTO statParamInputDTO);

    /**
     * 素材统计 - 走统计表 需要通过消息加数据
     * date
     * add_material_num  上传素材数
     * user_count        上传素材人数
     * merchant_count    上传素材商户数
     */
    @GetMapping("/materials")
    @ResponseBody
    StatVO materials(@SpringQueryMap @Valid StatParamInputDTO statParamInputDTO);

    /**
     * 图片操作 - 走统计表 需要通过消息加数据
     * 关键字 keyword_count
     * 抠图数
     * 放大数 zoom_count
     * 风格化数 stylized_count
     *
     * @param statParamInputDTO
     * @return
     */
    @GetMapping("/image_opt")
    @ResponseBody
    StatVO imageOpt(@SpringQueryMap @Valid StatParamInputDTO statParamInputDTO);


    /**
     * 商户订单统计
     * count
     * product_count
     * amount
     * pct
     */
    @GetMapping("/orders")
    @ResponseBody
    StatVO merchantOrders(@SpringQueryMap @Valid StatParamInputDTO statParamInputDTO);

    //=============================== 热卖类统计 ===========================================================

    /**
     * 热卖变体
     * date
     * count
     * category_id
     * category_name 类别名称
     * product_id
     * name
     * img_url
     */
    @GetMapping("/hot_products")
    @ResponseBody
    StatVO hotProducts(@SpringQueryMap @Valid HotParamInputDTO hotParamInputDTO);

    /**
     * 热卖母体
     * date
     * count
     * product_id
     * name
     * img_url
     * parent_id
     * color
     * size
     */
    @GetMapping("/hot_parent_products")
    @ResponseBody
    StatVO hotParentProducts(@SpringQueryMap @Valid HotParamInputDTO hotParamInputDTO);

    /**
     * 热卖商户统计
     * merchant_name
     * order_count
     * product_count
     * order_amount
     * pct
     */
    @GetMapping("/hot_merchants")
    @ResponseBody
    StatVO hotMerchants(@SpringQueryMap @Valid HotParamInputDTO hotParamInputDTO);

    /**
     * 热卖国家
     * country
     * address_id
     * order_count
     * order_amount
     * pct
     */
    @GetMapping("/hot_countries")
    @ResponseBody
    StatVO hotCountries(@SpringQueryMap @Valid HotParamInputDTO hotParamInputDTO);

    /**
     * 热卖物流
     * logistics_id 物流id
     * logistic 物流名称
     * order_count 订单数
     * carriage_amount 物流总额
     */
    @GetMapping("/hot_logistics")
    @ResponseBody
    StatVO hotLogistics(@SpringQueryMap @Valid HotParamInputDTO hotParamInputDTO);

    /**
     * 热卖工厂
     *
     * @param hotParamInputDTO
     * @return
     */
    @GetMapping("/hot_factories")
    @ResponseBody
    StatVO hotFactories(@SpringQueryMap @Valid HotParamInputDTO hotParamInputDTO);

    /**
     * 产品统计摘要
     *
     * @return
     */
    @GetMapping("/product_summary")
    @ResponseBody
    List<ProductSummaryBO> productSummary();


    /**
     * ping
     *
     * @return
     */
    @GetMapping("/orderStatistics")
    @ResponseBody
    Map<String, Object> orderStatistics(@RequestParam("storeIds")String storeIds);

}
