package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.StatisticsDto;
import com.sdsdiy.statapi.dto.base.FactoryOrderOperateStatisticsRespDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 工厂订单统计表(FactoryOrderOperateStatisticsRespDto)表api接口
 *
 * <AUTHOR>
 * @since 2020-09-01 16:45:24
 */
@RequestMapping("/microservice/factory_order_operate_statistics")
public interface FactoryOrderOperateStatisticsApi {

	@GetMapping("")
	List<StatisticsDto> factoryOrderStatistics(@RequestParam(value = "dateType") String dateType,@RequestParam(value = "factoryId") Long factoryId);

	@GetMapping("/oldDataProcessing")
	Boolean oldDataProcessing(@RequestParam(value = "sendTime") Long sendTime,@RequestParam(value = "endTime") Long endTime);
}