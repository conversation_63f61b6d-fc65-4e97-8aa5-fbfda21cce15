package com.sdsdiy.statapi.dto.material;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/7/9
 */
@Data
public class MaterialRequirementStatDTO {
    @DtoDefault
    private Long id;
    @DtoDefault
    @ApiModelProperty(value = "中标数")
    private Integer winBidNum;
    @DtoDefault
    @ApiModelProperty(value = "报名数")
    private Integer signNum;
    @DtoDefault
    @ApiModelProperty(value = "投稿素材数")
    private Integer materialNum;
    @DtoDefault
    @ApiModelProperty(value = "浏览数(pv)")
    private Integer visitNum;

    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    @ApiModelProperty(value = "商户账号id")
    private Long merchantSysUserId;

    public MaterialRequirementStatDTO() {
        this.winBidNum = 0;
        this.signNum = 0;
        this.materialNum = 0;
        this.visitNum = 0;
    }

    public MaterialRequirementStatDTO(Long id, Long merchantId, Long merchantSysUserId) {
        this.id = id;
        this.merchantId = merchantId;
        this.merchantSysUserId = merchantSysUserId;
    }
}
