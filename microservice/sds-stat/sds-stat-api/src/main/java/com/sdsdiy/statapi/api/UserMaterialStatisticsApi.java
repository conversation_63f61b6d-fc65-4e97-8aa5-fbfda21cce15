package com.sdsdiy.statapi.api;

import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.statapi.dto.UserDesignDateStatisticsRespDTO;
import com.sdsdiy.statapi.dto.UserDesignStatisticsDTO;
import com.sdsdiy.statapi.dto.UserMaterialStatRespDto;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/user_material_stat")
public interface UserMaterialStatisticsApi {

    /**
     * latestOrder
     *
     * @return
     */
    @GetMapping("groupMerchantUserStatistics")
    @ResponseBody
    List<UserMaterialStatRespDto> groupMerchantUserStatistics(@RequestParam("merchantId") Long merchantId, @RequestParam(value = "startTime", required = false) Long startTime, @RequestParam(value = "endTime", required = false) Long endTime, @RequestParam(value = "userIds", required = false) String userIds);


    @PostMapping("/findSumCountByGroupMerchantId")
    Map<Long, Integer> findSumCountByGroupMerchantId(@RequestBody IdsSearchHelper idsSearchHelper);
}
