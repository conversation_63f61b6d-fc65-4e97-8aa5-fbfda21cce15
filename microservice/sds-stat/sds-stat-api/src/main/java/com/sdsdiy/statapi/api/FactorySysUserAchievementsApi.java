package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.base.FactorySysUserAchievementsSumDto;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * (FactorySysUserAchievementsRespDto)表api接口
 *
 * <AUTHOR>
 * @since 2020-09-01 11:28:23
 */
@RequestMapping("/microservice/factory_sys_user_achievements")
public interface FactorySysUserAchievementsApi {
	@GetMapping("")
	List<FactorySysUserAchievementsSumDto> listSum(@RequestParam(value = "dateType")String dateType,
												   @RequestParam(value ="factoryId")Long factoryId,
												   @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
												   @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime);
}