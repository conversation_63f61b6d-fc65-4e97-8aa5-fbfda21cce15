package com.sdsdiy.statapi.dto.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * crm 月度绩效统计明细(CrmUserAchievementDetailedDto)RespDto类
 *
 * <AUTHOR>
 * @since 2021-08-12 20:14:59
 */
@Data
public class CrmUserAchievementDetailedRespDto extends QueryParamHelper {

    /**
     * 类型 BuyMember
     * <p>
     * 购买会员  BuyDomainName 购买域名  order 平台订单   SelfDeliveryOrder 自发货订单  orderAfterSale 平台订单售后 SelfDeliveryOrderAfterSale 自发货订单售后
     */
    private String type;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * keyword
     */
    private String keyword;

    /**
     * 收支类型 收入  income  支出 expenditure
     */
    private String incomeExpenditureType;
    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * 业务员id
     */
    private Long crmUserId;

}
