package com.sdsdiy.statapi.api;

import com.sdsdiy.orderdata.dto.FactoryOrderStatRespDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/factory_order_reject_history")
public interface FactoryOrderRejectHistoryApi {

    @GetMapping("stat")
    FactoryOrderStatRespDTO stat(@RequestParam("factoryId")Long factoryId);
}
