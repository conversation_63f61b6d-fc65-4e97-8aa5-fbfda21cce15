package com.sdsdiy.statapi.dto.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/4/26 12:17
 */
@Data
public class QuickOperationExportParamRespDto {
    /**
     * 参数名称（展示前端）
     */
    @JsonProperty("param")
    @DtoDefault
    @ApiModelProperty(value = "参数名称")
    private String param;
    /**
     * 参数值（前端无需展示此值，此值为提交时用）
     */
    @JsonProperty("paramCode")
    @DtoDefault
    @ApiModelProperty(value = "参数值")
    private String paramCode;
    /**
     * 备注（展示前端）
     */
    @JsonProperty("remarks")
    @DtoDefault
    @ApiModelProperty(value = "备注")
    private String remarks;
}
