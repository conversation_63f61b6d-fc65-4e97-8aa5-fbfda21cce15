package com.sdsdiy.statapi.api.es;


import com.sdsdiy.statdata.dto.es.EsUpdateTaskDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * es更新任务 API
 * </p>
 *
 * <AUTHOR>
 * @since 2024/01/18
 */
public interface EsUpdateTaskApi {

    @PostMapping("/inner/EsUpdateTaskApi/save")
    void save(@RequestBody EsUpdateTaskDTO dto);

    @PostMapping("/inner/EsUpdateTaskApi/delete")
    void delete(@RequestBody EsUpdateTaskDTO dto);

}