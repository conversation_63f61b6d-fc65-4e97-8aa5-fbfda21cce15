package com.sdsdiy.statapi.api.factory;


import com.sdsdiy.common.base.entity.dto.BaseListQueryDTO;
import com.sdsdiy.statdata.dto.factory.FactoryAvgShippingTimeReqDTO;
import com.sdsdiy.statdata.dto.factory.FactoryAvgShippingTimeRespDTO;
import com.sdsdiy.statdata.dto.factory.FactoryShipDayStatDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 工厂发货日统计 API
 * </p>
 *
 * <AUTHOR>
 * @since 2023/08/07
 */
@RequestMapping("/microservice/FactoryShipStatApi")
public interface FactoryShipStatApi {

    @PostMapping("/findDtoByFactoryIds")
    List<FactoryShipDayStatDTO> findDtoByFactoryIds(
            @RequestParam("dayTime") Long dayTime
            , @RequestBody BaseListQueryDTO<Long> reqDTO);

    @PostMapping("/findFactoryAvgShippingTime")
    List<FactoryAvgShippingTimeRespDTO> findFactoryAvgShippingTime(@RequestBody List<FactoryAvgShippingTimeReqDTO> reqList);

    @PostMapping("/avgShippingTimeOldData")
    void avgShippingTimeOldData(@RequestParam("days") Integer days);
}