package com.sdsdiy.statapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.common.dtoconvert.annotation.DtoBind;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.util.List;

/***
 * 统计菜单
 * @Description
 * <AUTHOR>
 * @Date 2020/6/22、10:04
 */
@Data
public class StatMenuDto {

    @DtoDefault
    private Integer id;

    @DtoDefault
    private String  name;
    private Integer parentId;

    @DtoDefault
    private String  icon;

    @DtoDefault
    private String  href;

    @DtoBind(selfField = "parentId",relateField = "id", provider = "com.sdsdiy.statimpl.relationprovider.StatMenuDtoRelationProvider")
    private StatMenuDto parent;

    @DtoBind(selfField = "id",relateField = "parentId",showField ="parent", provider = "com.sdsdiy.statimpl.relationprovider.StatMenuDtoRelationProvider")
    @JsonProperty("sub_menus")
    private List<StatMenuDto> children;

    @DtoDefault
    private String  parentIds;


}
