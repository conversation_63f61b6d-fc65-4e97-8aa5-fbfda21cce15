package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.UserQcCountAddParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.statapi.api
 * @Author: lujp
 * @CreateTime: 2023-08-08
 * @Description:
 * @Version: 1.0
 */
@RequestMapping("/microservice/stat/UserQcCountApi")
public interface UserQcCountApi {

    @PutMapping("/saveOrAddCount")
    void saveOrAddCount(@RequestParam("issuingBayId") Long issuingBayId,
                        @RequestParam("userId") Long userId,
                        @RequestParam("dayTimeStamp") Long dayTimeStamp,
                        @RequestParam("count") Integer count);

    @PutMapping("/saveOrAddCountBatch")
    void saveOrAddCountBatch(@RequestBody List<UserQcCountAddParam> paramList);

    @GetMapping("/findCurrentUserTodayPrintCount")
    Integer findCurrentUserTodayPrintCount();
}