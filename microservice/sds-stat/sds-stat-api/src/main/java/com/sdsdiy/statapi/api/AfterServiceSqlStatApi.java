package com.sdsdiy.statapi.api;

import com.sdsdiy.statapi.dto.AfterServiceStatDTO;
import org.apache.ibatis.annotations.Select;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 数据统计Api
 */
@RequestMapping("/microservice/stat/afterService")
public interface AfterServiceSqlStatApi {

    @GetMapping("inAudit")
    AfterServiceStatDTO inAudit();

    @GetMapping("auditInTime")
    AfterServiceStatDTO auditInTime();
}
