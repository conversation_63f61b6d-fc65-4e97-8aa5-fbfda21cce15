package com.sdsdiy.statapi.dto.base;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * crm 业务员月度统计(CrmUserAchievementRecordDto)RespDto类
 *
 * <AUTHOR>
 * @since 2021-08-12 20:17:24
 */
@Data
public class CrmUserAchievementRecordDto {
    /**
     * id
     */
    @DtoDefault
    private Long id;
    /**
     * 月份  2021年03月
     */
    @DtoDefault
    private Date time;
    /**
     * 业务员iD
     */
    @DtoDefault
    private Long crmUserId;
    /**
     * 业务员名称
     */
    @DtoDefault
    private String crmUserName;
    /**
     * 创建用户
     */
    private Long createUid;
    /**
     * 修改用户
     */
    private Long updateUid;
    /**
     * 创建时间
     */
    @DtoDefault
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 商户开户数
     */
    @DtoDefault
    private Long merchantNum;
    /**
     * 开通会员金额
     */
    @DtoDefault
    private BigDecimal merchantMoney;
    /**
     * 独立部署开户数
     */
    @DtoDefault
    private Long agentMerchantNum;
    /**
     * 平台订单数
     */
    @DtoDefault
    private Long orderNum;
    /**
     * 订单金额
     */
    @DtoDefault
    private BigDecimal orderMoney;
    /**
     * 自发货订单数
     */
    @DtoDefault
    private Long selfDeliveryOrderNum;
    /**
     * 自发货金额
     */
    @DtoDefault
    private BigDecimal selfDeliveryOrderMoney;
    /**
     * 平台售后次数
     */
    @DtoDefault
    private Long orderAfterSaleNum;
    /**
     * 平台售后金额
     */
    @DtoDefault
    private BigDecimal orderAfterSaleMoney;

    /**
     * 操作类型 CrmUserAchievementTypeConstant
     */
    private String type;

    private Long merchantId;

    /**
     * 会员等级
     */
    private String level;

    /**
     * 会员年限
     */
    private Integer duration;
    /**
     * 域名开始时间
     */
    private Date sendDate;
    /**
     * 域名结束时间
     */
    private Date endDate;
    /**
     * 订单号
     */
    private String orderNo;

    @ApiModelProperty(value = "套餐价格")
    private BigDecimal price;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 售后类型
     */
    private String afterSalesMode;

    @ApiModelProperty(value = "会员操作")
    private Integer optionType;

    private String adminName;

    @DtoDefault
    @ApiModelProperty(value = "平台支付订单数")
    private Long orderPaidNum;

    @DtoDefault
    @ApiModelProperty(value = "平台支付订单金额")
    private BigDecimal orderPaidMoney;

    @ApiModelProperty(value = "支付订单数")
    private Long paidOrderNum;

    @ApiModelProperty(value = "支付订单总金额")
    private BigDecimal paidOrderAmount;
}
