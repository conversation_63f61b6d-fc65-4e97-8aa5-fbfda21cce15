package com.sdsdiy.statapi.api;

import java.util.Date;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.statapi.dto.base.CrmUserAchievementDetailedDto;
import com.sdsdiy.statapi.dto.base.CrmUserAchievementDetailedRespDto;
import com.sdsdiy.statapi.dto.base.CrmUserAchievementRecordDto;
import com.sdsdiy.statapi.dto.base.CrmUserAchievementRecordRespDto;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * crm 月度绩效统计明细(CrmUserAchievementDetailedDto)表api接口
 *
 * <AUTHOR>
 * @since 2021-08-12 20:15:00
 */
@RequestMapping("/microservice/crmUserAchievementDetailed")
public interface CrmUserAchievementDetailedApi {

    @GetMapping("")
    PageListResultRespDto<CrmUserAchievementDetailedDto> list(@SpringQueryMap CrmUserAchievementDetailedRespDto dto);

}
