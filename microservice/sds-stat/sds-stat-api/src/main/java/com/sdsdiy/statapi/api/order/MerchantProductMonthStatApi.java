package com.sdsdiy.statapi.api.order;


import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.statdata.dto.merchant.product.MerchantProductMonthStatDTO;
import com.sdsdiy.statdata.dto.merchant.product.MerchantProductMonthStatReqDTO;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 商户产品变体月统计 API
 * </p>
 *
 * <AUTHOR>
 * @since 2023/07/17
 */
@RequestMapping("/microservice/MerchantProductMonthStatApi")
public interface MerchantProductMonthStatApi {

    @PostMapping("monthStat")
    void monthStat(@RequestParam Long time, @RequestBody BaseListDto<Long> dto);

    @PutMapping("merchantProductMonthStat")
    PageResultDto<MerchantProductMonthStatDTO> merchantProductMonthStat(@RequestBody MerchantProductMonthStatReqDTO reqDTO);

}