package com.sdsdiy.statapi.dto.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.util.Date;

/**
 * 工厂订单统计表(FactoryOrderOperateStatisticsRespDto)RespDto类
 *
 * <AUTHOR>
 * @since 2020-09-01 16:45:24
 */
@Data
public class FactoryOrderOperateStatisticsRespDto {
	/**
	 * id
	 */
	@DtoDefault
	private Long id;
	/**
	 * 工厂id
	 */
	@DtoDefault
	private Long factoryId;
	/**
	 * 数量
	 */
	@DtoDefault
	private Integer num;
	/**
	 * 0派单数1接单数2下载稿件数3生产数4发货数
	 */
	@DtoDefault
	private String type;
	@DtoDefault
	private Date createTime;
	@DtoDefault
	private String time;

	@DtoDefault
	private Date sendingTime;




}