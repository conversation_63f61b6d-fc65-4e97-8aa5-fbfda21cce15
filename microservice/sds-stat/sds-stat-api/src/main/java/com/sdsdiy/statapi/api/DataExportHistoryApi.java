package com.sdsdiy.statapi.api;

import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.statapi.dto.export.DataExportHistoryDto;
import com.sdsdiy.statapi.dto.export.DataExportHistoryReqDto;
import com.sdsdiy.statapi.dto.export.DataExportHistoryRespDto;
import com.sdsdiy.userapi.dto.UserDto;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商户账单导出记录表(ExcelExportHistory)表api接口
 *
 * <AUTHOR>
 * @since 2021-01-12 16:06:48
 */
@RequestMapping("/microservice/merchants")
public interface DataExportHistoryApi {

    @PostMapping("/dataExportHistory/{merchantId}")
    Long create(@PathVariable("merchantId") Long merchantId,
                @Valid @RequestBody DataExportHistoryReqDto reqDto);

    @PutMapping("/dataExportHistory/{merchantId}/{id}")
    void edit(@PathVariable("merchantId") Long merchantId,
              @PathVariable("id") Long id,
              @Valid @RequestBody DataExportHistoryReqDto reqDto);

    @GetMapping("dataExportHistory/{merchantId}/{id}")
    DataExportHistoryDto findById(@PathVariable("merchantId") Long merchantId,
                                  @PathVariable("id") Long id);

    @GetMapping("/dataExportHistory/{merchantId}")
    PageListResultDTO<DataExportHistoryRespDto> page(@PathVariable("merchantId") Long merchantId,
                                                     @SpringQueryMap DataExportHistoryReqDto pageSelect);

    @GetMapping("dataExportHistory/{merchantId}/uid")
    List<UserDto> getUpdateUid(@PathVariable("merchantId") Long merchantId,
                               @RequestParam(required = false) Long searchUserId);
}