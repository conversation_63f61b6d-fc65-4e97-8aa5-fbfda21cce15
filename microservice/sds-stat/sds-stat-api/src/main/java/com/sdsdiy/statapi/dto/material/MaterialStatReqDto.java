package com.sdsdiy.statapi.dto.material;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NoArgsConstructor;

/**
 * 素材统计表(MaterialStat)ReqDto类
 *
 * <AUTHOR>
 * @since 2022-02-21 14:41:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialStatReqDto implements Serializable {
    private static final long serialVersionUID = -78042867119100580L;
    /**
    * 主键id
    */
    private Long id;
    /**
    * 素材id
    */
    private Long materialId;
    /**
    * 订单数
    */
    private Integer orderNum;
    /**
    * 出货数
    */
    private Integer shipmentNum;
    /**
    * 最近支付时间
    */
    private Long recentPayTime;
    /**
     * 素材id列表
     */
    private List<Long> materialIdList;

}