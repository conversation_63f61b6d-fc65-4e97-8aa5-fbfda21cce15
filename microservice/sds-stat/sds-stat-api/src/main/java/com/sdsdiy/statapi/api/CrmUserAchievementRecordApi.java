package com.sdsdiy.statapi.api;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.statapi.dto.base.CrmUserAchievementRecordDto;
import com.sdsdiy.statapi.dto.base.CrmUserAchievementRecordRespDto;
import com.sdsdiy.statapi.dto.crm.CrmUserAchievementRecordSaveDTO;
import org.springframework.web.bind.annotation.*;

/**
 * crm 业务员月度统计(CrmUserAchievementRecordDto)表api接口
 *
 * <AUTHOR>
 * @since 2021-08-12 20:17:24
 */
@RequestMapping("/microservice/crmUserAchievementRecord")
public interface CrmUserAchievementRecordApi {

    @PostMapping("listMonth")
    PageListResultRespDto<CrmUserAchievementRecordDto> listMonth(@RequestBody CrmUserAchievementRecordRespDto dto);


    @PostMapping("saveAchievementRecord")
    void saveAchievementRecord(@RequestBody CrmUserAchievementRecordSaveDTO saveDTO);

    @PutMapping("/updateCrmUser")
    void updateCrmUser(@RequestBody CrmUserAchievementRecordDto crmUserAchievementRecordDto);


    @PostMapping("listDay")
    PageListResultRespDto<CrmUserAchievementRecordDto> listDay(@RequestBody CrmUserAchievementRecordRespDto dto);

    @PostMapping("dayOldDataHandler/{time}")
    void dayOldDataHandler(@PathVariable("time") Long time);
}
