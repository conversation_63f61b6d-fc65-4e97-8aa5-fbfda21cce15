package com.sdsdiy.statapi.api;

import com.sdsdiy.statdata.dto.export.FileExportAsyncTaskAddParam;
import com.sdsdiy.statdata.dto.export.FileExportAsyncTaskOneParam;
import com.sdsdiy.statdata.dto.export.FileExportAsyncTaskResp;
import com.sdsdiy.statdata.dto.export.FileExportAsyncTaskUpdateParam;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 导出文件异步任务表(FileExportAsyncTask)表api接口
 *
 * <AUTHOR>
 * @since 2024-10-21 12:15:32
 */

@RequestMapping("/microservice/fileExportAsyncTask")
public interface FileExportAsyncTaskApi {

    @GetMapping("one")
    FileExportAsyncTaskResp getOne(@SpringQueryMap FileExportAsyncTaskOneParam param);

    @PostMapping("")
    FileExportAsyncTaskResp addInit(@RequestBody @Validated FileExportAsyncTaskAddParam param);

    @PutMapping("")
    void update(@RequestBody @Validated FileExportAsyncTaskUpdateParam param);

}
