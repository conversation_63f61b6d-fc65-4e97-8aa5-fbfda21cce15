package com.sdsdiy.statapi.api.demo;


import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.statapi.dto.demo.DemoOrderDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 测试订单api
 * @Author:zjy
 */
public interface DemoOrderApi {

    /**
     * 获取订单简单
     * @param id
     * @return
     */
    @GetMapping("/simpleDemoOrders/{id}")
    DemoOrderDto getDtoSimpleById(@PathVariable(value = "id") Long id, @RequestParam("fields") String fields);

    /**
     * 获取订单详情
     * @param id
     * @return
     */
    @GetMapping("/demoOrders/{id}")
    DemoOrderDto getDtoById(@PathVariable(value = "id") Long id, @RequestParam("fields") String fields);

    /**
     * 获取订单列表
     * @param
     * @param queryParamHelper
     * @return
     */
    @GetMapping("/demoOrders")
    PageListResultDTO<DemoOrderDto> getList(@ModelAttribute QueryParamHelper queryParamHelper);







}
