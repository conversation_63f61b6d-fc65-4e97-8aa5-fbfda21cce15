package com.sdsdiy.statapi.dto.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/***
 * 订单类统计通用
 * @Description
 * <AUTHOR>
 * @Date 2020/6/19、13:40
 */
@Data
public class OrderStatOutputDto {

    /**
     * 产品名称
     */
    @JsonProperty("product_name")
    private String productName;
    /**
     * 产品名称
     */
    @JsonProperty("issuingBayAreaName")
    private String issuingBayAreaName;
    /**
     * 产品母体名称
     */
    @JsonProperty("parent_product_name")
    private String parentProductName;

    /**
     * 产品类别
     */
    @JsonProperty("category_id")
    private Integer categoryId;
    /**
     * 产品类别名称
     */
    @JsonProperty("category_name")
    private String categoryName;
    /**
     * 产品图片
     */
    @JsonProperty("img_url")
    private String imgUrl;


    @JsonProperty("img_urls")
    private List imgUrls;

    /**
     * 颜色
     */
    private String color;

    /**
     * 颜色名称
     */
    @JsonProperty("color_name")
    private String colorName;

    /**
     * 尺寸
     */
    private String size;
    /**
     * 国家名称
     */
    @JsonProperty("country_name")
    private String country;
    /**
     * 商户名称
     */
    @JsonProperty("merchant_name")
    private String merchantName;
    /**
     * 转成测试的时间
     */
    private Long turnIntoTestAt;
    /**
     * 订单数
     */
    @JsonProperty("order_count")
    private Integer orderCount;
    /**
     * 商品数
     */
    @JsonProperty("product_count")
    private Integer productCount;
    /**
     * 订单金额
     */
    @JsonProperty("order_amount")
    private Double orderAmount;

    /**
     * 客单价
     */
    private Double pct;




    /**
     * 物流名称
     */
    @JsonProperty("logistic_name")
    private String logisticName;
    /**
     * 物流费
     */
    @JsonProperty("carriage_amount")
    private String carriageAmount;

    /**
     * 工厂名称
     */
    @JsonProperty("factory_name")
    private String factoryName;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "租户名称")
    private String tenantName;
    @ApiModelProperty(value = "区域租户id")
    private Long issuingBayAreaTenantId;
    /**
     * 供应总额
     */
    private Double price;

    /**
     * 数据转换通用
     */
    public void dataConvert(){
        pctKeepTwoDecimal();
    }

    private void pctKeepTwoDecimal(){
        if(pct!=null){
            pct = (double) Math.round(pct * 100) / 100;
        }
    }


    /**
     *  图片字符串转List
     */
    private void convertImgToList(){
        if(imgUrl!=null && !imgUrl.isEmpty()){
            System.out.println(imgUrl);
            imgUrl = imgUrl.substring(1,imgUrl.length()-1);
            String[] urls = imgUrl.split(",");
            imgUrls = new ArrayList();
            Arrays.stream(urls).forEach(
                s -> {
                   imgUrls.add(s.substring(1,s.length()-1));
                }
            );
            imgUrl = null;
        }

    }


}
