package com.sdsdiy.userimpl.task;

import com.sdsdiy.userimpl.service.NotificationOldDataService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 */
@Slf4j
@Component
public class NotificationTask {
    @Resource
    private NotificationOldDataService notificationOldDataService;

    @XxlJob("notification-to-old-data")
    public ReturnT<String> notificationToOldData(String param) {
        XxlJobLogger.log("begin notificationToOldData Begin task");
        log.info("[开始]通知转移老数据");
        notificationOldDataService.bathSaveByNotification();
        XxlJobLogger.log("begin notificationToOldData end task");
        log.info("[结束]通知转移老数据");
        return new ReturnT<>();
    }
}
