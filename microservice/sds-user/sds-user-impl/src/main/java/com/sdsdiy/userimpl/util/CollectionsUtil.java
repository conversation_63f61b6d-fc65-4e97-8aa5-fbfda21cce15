package com.sdsdiy.userimpl.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.userimpl.util
 * @Description: TODO
 * @date 2021/11/1 15:18
 */
public class CollectionsUtil {
    public static <K, V> List<V> getAllFormMap(List<K> keys, Map<K, V> source) {
        List<V> values = Collections.emptyList();
        if (keys != null && !keys.isEmpty() && source != null && !source.isEmpty()) {
            values = new ArrayList<>();
            for (K k : keys) {
                values.add(source.get(k));
            }
        }
        return values;
    }
}
