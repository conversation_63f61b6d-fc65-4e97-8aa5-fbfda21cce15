package com.sdsdiy.userimpl.entity.bo.saas.storeSettleIn;

import com.sdsdiy.userapi.dto.storeSettleIn.supplierProfitSplitRatioConfig.SupplierProfitSplitRatioConfigRespDto;
import com.sdsdiy.userimpl.entity.po.saas.storeSettleIn.SupplierProfitSplitRatioConfig;
import com.sdsdiy.userimpl.entity.po.saas.storeSettleIn.SupplierSettleInCompanyInfo;
import com.sdsdiy.userimpl.entity.po.saas.storeSettleIn.SupplierSettleInIndividualInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@NoArgsConstructor
public class SupplierProfitSplitRatioConfigBo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商类型
     */
    private String type;

    /**
     * 公司类型：individual-个人 company-企业
     */
    private String companyType;

    /**
     * 是否主商户：0：否 1：是
     */
    private Integer mainFlag;

    /**
     * 供应商分账比例配置
     */
    private SupplierProfitSplitRatioConfig ratioConfig;
    
}
