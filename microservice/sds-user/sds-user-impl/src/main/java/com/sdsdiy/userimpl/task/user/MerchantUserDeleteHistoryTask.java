package com.sdsdiy.userimpl.task.user;

import com.sdsdiy.userimpl.service.user.MerchantUserDeleteTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: zmy
 */
@Slf4j
@Component
public class MerchantUserDeleteHistoryTask {
    @Resource
    private MerchantUserDeleteTaskService taskService;

    /**删除业务处理*/
    @XxlJob("merchant-user-delete-handle-data")
    public ReturnT<String> handleDatas(String param) {
        XxlJobLogger.log("begin merchant user delete history update task");
        taskService.sendHandleDataMsg();
        XxlJobLogger.log("end merchant user delete history update task");
        String msg="";
        return new ReturnT<>(msg);
    }

    /**失败结果处理*/
    @XxlJob("merchant-user-delete-fail-data")
    public ReturnT<String> handleFailDatas(String param) {
        XxlJobLogger.log("begin merchant user delete history update task");
        taskService.handleFailData();
        XxlJobLogger.log("end merchant user delete history update task");
        String msg="";
        return new ReturnT<>(msg);
    }
    /**删除素材业务处理*/
    @XxlJob("merchant-user-delete-handle-material-delete")
    public ReturnT<String> deleteMaterial(String param) {
        XxlJobLogger.log("begin merchant user delete handle material delete task");
        taskService.deleteMaterial();
        XxlJobLogger.log("end merchant user delete handle material delete task");
        String msg="";
        return new ReturnT<>(msg);
    }

    /**更新删除素材结果*/
    @XxlJob("merchant-user-delete-handle-material-result")
    public ReturnT<String> updateHandleMaterialResult(String param) {
        XxlJobLogger.log("begin merchant user delete update material result task");
        taskService.updateHandleMaterialResult();
        XxlJobLogger.log("end merchant user delete update material result task");
        String msg="";
        return new ReturnT<>(msg);
    }

    /**更新处理结果*/
    @XxlJob("merchant-user-delete-finish")
    public ReturnT<String> updateStatus(String param) {
        XxlJobLogger.log("begin merchant user delete history finish task");
        taskService.updateStatus();
        XxlJobLogger.log("end merchant user delete history finish task");
        String msg="";
        return new ReturnT<>(msg);
    }

}
