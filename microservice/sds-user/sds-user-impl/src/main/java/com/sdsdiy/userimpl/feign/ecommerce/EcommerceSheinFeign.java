package com.sdsdiy.userimpl.feign.ecommerce;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.ecommerceapi.api.EcommerceSheinApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2024/6/18
 */
@FeignClient(value = "SERVICE-ECOMMERCE", contextId = "EcommerceSheinFeign", url = MicroServiceEndpointConstant.SERVICE_ECOMMERCE)
public interface EcommerceSheinFeign extends EcommerceSheinApi {
}
