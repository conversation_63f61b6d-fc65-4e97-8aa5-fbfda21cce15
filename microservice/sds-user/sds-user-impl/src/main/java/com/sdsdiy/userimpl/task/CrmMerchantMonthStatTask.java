package com.sdsdiy.userimpl.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.helper.BeanUtils;
import com.sdsdiy.userapi.constant.enums.CrmClueType;
import com.sdsdiy.userapi.dto.crm.dto.CrmMerchantClueEsDto;
import com.sdsdiy.userimpl.entity.po.CrmMerchantClueMonthStaticRecode;
import com.sdsdiy.userimpl.entity.po.crm.CrmMerchantClue;
import com.sdsdiy.userimpl.service.CrmMerchantClueMonthStaticRecodeService;
import com.sdsdiy.userimpl.service.crm.CrmMerchantClueEsService;
import com.sdsdiy.userimpl.service.crm.CrmMerchantClueService;
import com.sdsdiy.userimpl.service.crm.manager.CrmMerchantClueManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: hzh
 * @Date: 2022/1/24 14:57
 * 每月对统计数量清零
 */
@Component
@Slf4j
public class CrmMerchantMonthStatTask {

    @Resource
    private CrmMerchantClueManager crmMerchantClueManager;
    @Resource
    private CrmMerchantClueEsService crmMerchantClueEsService;
    @Resource
    private CrmMerchantClueService crmMerchantClueService;
    @Resource
    private CrmMerchantClueMonthStaticRecodeService crmMerchantClueMonthStaticRecodeService;

    @XxlJob("crmMerchantMonthStatTask")
    public ReturnT<Boolean> merchantMainBusinessTask(String param) {
        XxlJobLogger.log("【开始】crmMerchantMonthStatTask");
        log.info("【开始】crmMerchantMonthStatTask");

        crmMerchantMonthStatTask();

        ReturnT<Boolean> ret = new ReturnT<>(true);
        ret.setMsg("执行成功！");
        log.info("【结束】crmMerchantMonthStatTask");
        XxlJobLogger.log("【结束】crmMerchantMonthStatTask");
        return ret;
    }

    public void crmMerchantMonthStatTask() {
        //上个月的月末时间
        long lastMonthTime = DateUtil.endOfMonth(DateUtil.lastMonth()).getTime();
        List<CrmMerchantClue> merchantClueList = crmMerchantClueManager.listMerchantPage(new Page<>(1, 1000));
        for (int i = 2; CollectionUtil.isNotEmpty(merchantClueList); i++) {
            List<CrmMerchantClueEsDto> clueEss = BeanUtils.toList(merchantClueList, CrmMerchantClueEsDto.class);
            List<Long> merchantIds = merchantClueList.stream().filter(clue -> CrmClueType.MERCHANT.type.equalsIgnoreCase(clue.getType()))
                    .map(CrmMerchantClue::getMerchantId).collect(Collectors.toList());
            List<String> ids = merchantClueList.stream().map(c -> c.getId() + "").collect(Collectors.toList());
            Map<Long, CrmMerchantClueEsDto> clueEsMap = crmMerchantClueEsService.listByIds(ids).stream().collect(Collectors.toMap(CrmMerchantClueEsDto::getId, Function.identity(), (a, b) -> b));
            Map<Long, CrmMerchantClueMonthStaticRecode> recodeMap = crmMerchantClueMonthStaticRecodeService.getLastMonthMap(merchantIds, lastMonthTime);
            ArrayList<CrmMerchantClueMonthStaticRecode> saveRecodes = Lists.newArrayList();
            //批量获取近三月的数据
            List<Long> saveRecordMerchantIds = Lists.newArrayList();
            for (CrmMerchantClueEsDto crmMerchantClueEsDto : clueEss) {
                CrmMerchantClueMonthStaticRecode clueMonthStaticRecode = recodeMap.get(crmMerchantClueEsDto.getMerchantId());
                CrmMerchantClueEsDto clueEsDto = clueEsMap.get(crmMerchantClueEsDto.getId());
                if (clueEsDto == null || clueMonthStaticRecode != null) {
                    continue;
                }
                crmMerchantClueEsDto.setOrderNum(0);
                crmMerchantClueEsDto.setSynthesisNum(0);
                crmMerchantClueEsDto.setLastOrderNum(clueEsDto.getOrderNum() == null ? 0 : clueEsDto.getOrderNum());
                crmMerchantClueEsDto.setBeforeLastOrderNum(clueEsDto.getLastOrderNum() == null ? 0 : clueEsDto.getLastOrderNum());
                crmMerchantClueEsDto.setLastMonthSynthesisNum(clueEsDto.getSynthesisNum() == null ? 0 : clueEsDto.getSynthesisNum());
                crmMerchantClueEsDto.setBeforeLastMonthSynthesisNum(clueEsDto.getLastMonthSynthesisNum() == null ? 0 : clueEsDto.getLastMonthSynthesisNum());
                CrmMerchantClueMonthStaticRecode saveRecode = new CrmMerchantClueMonthStaticRecode();
                if (crmMerchantClueEsDto.getMerchantId() != 0 && !saveRecordMerchantIds.contains(crmMerchantClueEsDto.getMerchantId())) {
                    saveRecode.setMerchantId(crmMerchantClueEsDto.getMerchantId());
                    saveRecode.setOrderNum(clueEsDto.getOrderNum() == null ? 0 : clueEsDto.getOrderNum());
                    saveRecode.setDesignNum((clueEsDto.getSynthesisNum() == null ? 0 : clueEsDto.getSynthesisNum()));
                    saveRecode.setDayShort(DateUtil.format(new Date(lastMonthTime), "yyyyMMdd"));
                    saveRecode.setCreatedTime(lastMonthTime);
                    saveRecodes.add(saveRecode);
                    saveRecordMerchantIds.add(crmMerchantClueEsDto.getMerchantId());
                }
            }
            crmMerchantClueMonthStaticRecodeService.saveBatch(saveRecodes);
            if (CollectionUtil.isNotEmpty(clueEss)) {
                crmMerchantClueEsService.updateBatch(clueEss);
            }
            merchantClueList = crmMerchantClueManager.listMerchantPage(new Page<>(i, 1000));
        }
    }

    @XxlJob("crmMerchantLastTowMonthTask")
    public ReturnT<Boolean> crmMerchantLastTowMonthTask(String param) {
        XxlJobLogger.log("【开始】crmMerchantLastTowMonthTask");
        log.info("【开始】crmMerchantLastTowMonthTask");
        Date date = new Date();
        if (!StringUtils.isEmpty(param)) {
            date = DateUtil.parseDateTime(param);
        }
        List<CrmMerchantClue> merchantClueList = crmMerchantClueManager.lambdaQuery()
                .ne(CrmMerchantClue::getType, CrmClueType.CLUE.type)
                .ne(CrmMerchantClue::getMerchantId, 0L)
                .eq(CrmMerchantClue::getIsDelete, 0).list();

        List<List<CrmMerchantClue>> splitMerchantList = CollectionUtil.split(merchantClueList, 100);
        for (List<CrmMerchantClue> clues : splitMerchantList) {
            crmMerchantClueService.updateLostTwoMonthOrderAndDesignNumEs(clues, date);
        }
        ReturnT<Boolean> ret = new ReturnT<>(true);
        ret.setMsg("执行成功！");
        log.info("【结束】crmMerchantLastTowMonthTask");
        XxlJobLogger.log("【结束】crmMerchantLastTowMonthTask");
        return ret;
    }

}
