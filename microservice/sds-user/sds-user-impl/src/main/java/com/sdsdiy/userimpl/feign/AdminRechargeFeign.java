package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.orderapi.api.PaymentApi;
import com.sdsdiy.paymentapi.api.AdminRechargeApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/6/29
 */
@FeignClient(name = "service-payment", contextId = "AdminRechargeFeign", url = MicroServiceEndpointConstant.SERVICE_PAYMENT)
public interface AdminRechargeFeign extends AdminRechargeApi {
}
