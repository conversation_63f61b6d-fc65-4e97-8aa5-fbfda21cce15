package com.sdsdiy.userimpl.util;

import cn.hutool.core.util.StrUtil;
import com.sdsdiy.userapi.dto.IPEntityDto;
import com.sdsdiy.userimpl.service.ip.IPUtils;
import com.sdsdiy.userimpl.service.ip.IpipUtilsV2;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
public class IpUtil {
    public static IPEntityDto getIpMsg(String ip) {
        if (StrUtil.isBlank(ip)) {
            return new IPEntityDto();
        }
        IPEntityDto msg = IpipUtilsV2.getCountryCodeByIP(ip);
        if (msg == null) {
            msg = new IPEntityDto();
        }
        //判断国家 省  市 是否有空值
        if (StrUtil.isBlank(msg.getCountryName()) || StrUtil.isBlank(msg.getProvinceName()) || StrUtil.isBlank(msg.getCityName())) {
            //查询IP库
            IPEntityDto ipEntityDto = IPUtils.getIPMsg(ip);
            if (StrUtil.isBlank(msg.getCountryName())) {
                msg.setCountryName(ipEntityDto.getCountryName());
            }
            if (StrUtil.isBlank(msg.getProvinceName())) {
                msg.setProvinceName(ipEntityDto.getProvinceName());
            }
            if (StrUtil.isBlank(msg.getCityName())) {
                msg.setCityName(ipEntityDto.getCityName());
            }
        }
        return msg;
    }
}
