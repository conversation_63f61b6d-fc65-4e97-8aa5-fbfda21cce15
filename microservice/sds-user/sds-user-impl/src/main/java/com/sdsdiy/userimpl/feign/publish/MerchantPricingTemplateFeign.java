package com.sdsdiy.userimpl.feign.publish;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.publishapi.api.pricing.PricingTemplateApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/6/29
 */
@FeignClient(name = "service-publish", contextId = "MerchantPricingTemplateFeign", url = MicroServiceEndpointConstant.SERVICE_PUBLISH)
public interface MerchantPricingTemplateFeign extends PricingTemplateApi {
}
