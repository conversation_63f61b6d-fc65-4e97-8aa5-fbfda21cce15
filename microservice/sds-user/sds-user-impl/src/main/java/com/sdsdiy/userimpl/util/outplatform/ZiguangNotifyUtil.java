package com.sdsdiy.userimpl.util.outplatform;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.core.base.util.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @program: microservice-parent
 * @Package com.sdsdiy.userimpl.util
 * @Description: TODO
 * @date 2021/11/1 15:18
 */
@Log4j2
public class ZiguangNotifyUtil implements ClientHttpNotifyApi{

    public final static String md5_signature = "BpQWy6AtvKcixjePQ4ZMuvBqUyIsXWWX";

    public static String generateSignForJson(String jsonString, String signKey) {
        log.info("签名JSON:{}", jsonString);
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonString, Feature.OrderedField);
        Map maps = jsonObject.getInnerMap();
        ArrayList<String> arrayList = new ArrayList<>();
        Object key;
        Object value;
        for (Object map : maps.entrySet()) {
            key = ((Map.Entry) map).getKey();
            if (key.equals("signature")) {
                continue;
            }
            value = ((Map.Entry) map).getValue();
            if (value == null) {//将原始报文中的signature字段去除掉
                continue;
            }
            if(StringUtils.isBlank(value.toString())){
                continue;
            }
            arrayList.add(key + "=" + value + "&");//使用URL键值对的格式（key1=value1&key2=value2…）拼接成字符串signBlock。
        }
        if (CollectionUtils.isEmpty(arrayList)) {
            return null;
        }
        String[] strArray = arrayList.toArray(new String[arrayList.size()]);
        Arrays.sort(strArray);
        StringBuffer stringBuffer = new StringBuffer();
        for (String param : strArray) {
            stringBuffer.append(param);
        }
        // 最后添加key值进行加密
        if (StringUtils.isEmpty(signKey)) {
            signKey = md5_signature;
        }
        String params = stringBuffer.toString() + "key=" + signKey;
        log.info("签名参数:{}", params);
        String sign = StringUtils.getMd5(params).toUpperCase();
        log.info("签名值:{}", sign);
        return sign;
    }
    @Override
    public String notifyClient(String uri,String body){
        String url = "http://161.189.12.212:8010/api/order/openapi" + uri;
        String sign = generateSignForJson(body,md5_signature);
        log.info(url + " request body: " + body + " sign=" + sign);

        try {
            HttpRequest request = HttpUtil.createPost(url);
            request.header("Content-Type","application/json");
            request.header("signature",sign);
            request.body(body);
            HttpResponse response = request.execute();
            if(!response.isOk()){
                throw new BusinessException(response.body());
            }
            String responseBody = response.body();
            log.info(url + " response body: " + responseBody);
        }catch (Exception e){
            log.error("CommonNotifyUtil.notifyClient failed",e);
            throw e;
        }
        return CommonStatus.ONLINE.getDesc();
    }
}
