package com.sdsdiy.userimpl.task;

import com.es.util.BeanUtils;
import com.sdsdiy.common.base.helper.JsonUtil;
import com.sdsdiy.userapi.dto.crm.dto.CrmMerchantClueEsDto;
import com.sdsdiy.userimpl.service.crm.CrmMerchantClueEsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.userdata.constant.CrmStatConstant.CRM_STAT_HASH_KEY;

/**
 * @Author: hzh
 * @Date: 2022/1/24 14:57
 * 每分钟统计次数
 */
@Component
@Slf4j
public class CrmMerchantMinuteStatTask {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private CrmMerchantClueEsService crmMerchantClueEsService;

    @XxlJob("CrmMerchantMinuteStatTask")
    public ReturnT<Boolean> crmMerchantMinteStatTask(String param) {
        XxlJobLogger.log("【开始】CrmMerchantMinuteStatTask");
        log.info("【开始】CrmMerchantMinuteStatTask");

        crmMerchantMinteStatTask();

        ReturnT<Boolean> ret = new ReturnT<>(true);
        ret.setMsg("执行成功！");
        log.info("【结束】CrmMerchantMinuteStatTask");
        XxlJobLogger.log("【结束】CrmMerchantMinuteStatTask");
        return ret;
    }

    public void crmMerchantMinteStatTask() {
        LocalDateTime dateTime = LocalDateTime.now().minusMinutes(1L);
        LocalDateTime now = dateTime.withSecond(0).withNano(0);
        String date = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        ScanOptions options = ScanOptions.scanOptions().match(CRM_STAT_HASH_KEY + "*").count(10000).build();
        //Set<String> keys = redisTemplate.keys(CRM_STAT_HASH_KEY + "*");
        RedisConnectionFactory factory = redisTemplate.getConnectionFactory();
        RedisConnection rc = Objects.requireNonNull(factory).getConnection();
        Cursor<byte[]> cursor = rc.scan(options);
        List<String> result = new ArrayList<>();
        while (cursor.hasNext()) {
            result.add(new String(cursor.next()));
        }
       // result.addAll(keys);
        List<String> collect = result.stream().filter(a -> StringUtils.substringAfter(a, ":").compareTo(date) < 0).collect(Collectors.toList());
        collect.sort(String::compareTo);
        log.info("crmMerchantMinteStatTask 处理的数据:{}",collect);
        for (String value : collect) {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(value);
            TreeMap<Object, Object> treeMap = new TreeMap<>(Comparator.comparing(Object::toString));
            treeMap.putAll(entries);
            Map<String, Map<String, Object>> map = new HashMap<>();
            entries.forEach((k, v) -> {
                String s = (String) k;
                String[] split = s.split("-");
                String crmMerchantId = split[0];
                String name = split[1];
                Map<String, Object> objectMap = map.computeIfAbsent(crmMerchantId, a -> {
                    Map<String, Object> hashMap = new HashMap<>();
                    hashMap.put("id", Long.parseLong(crmMerchantId));
                    return hashMap;
                });
                objectMap.put(name, v);
            });
            map.forEach((k, v) -> {
                CrmMerchantClueEsDto clueEsDto = crmMerchantClueEsService.doGetById(k);
                if (clueEsDto.getSynthesisNum() == null) {
                    clueEsDto.setSynthesisNum(0);
                }
                if (clueEsDto.getOrderNum() == null) {
                    clueEsDto.setOrderNum(0);
                }
                CrmMerchantClueEsDto crmMerchantClueEsDto = BeanUtils.mapToBean(v, CrmMerchantClueEsDto.class);
                crmMerchantClueEsDto.setId(clueEsDto.getId());
                if (crmMerchantClueEsDto.getSynthesisNum() != null) {
                    crmMerchantClueEsDto.setSynthesisNum(clueEsDto.getSynthesisNum() + crmMerchantClueEsDto.getSynthesisNum());
                }
                if (crmMerchantClueEsDto.getOrderNum() != null) {
                    crmMerchantClueEsDto.setOrderNum(clueEsDto.getOrderNum() + crmMerchantClueEsDto.getOrderNum());
                }
                if (crmMerchantClueEsDto.getDesignTime() != null) {
                    crmMerchantClueEsDto.setDesignTime(crmMerchantClueEsDto.getDesignTime());
                }
                if (crmMerchantClueEsDto.getPayOrderTime() != null) {
                    crmMerchantClueEsDto.setPayOrderTime(crmMerchantClueEsDto.getPayOrderTime());
                }
                log.info("更新crmMerchantMinteStatTask:{}",JsonUtil.toJson(crmMerchantClueEsDto));
                crmMerchantClueEsService.updateById(crmMerchantClueEsDto);
            });
            redisTemplate.delete(value);
        }

    }

}
