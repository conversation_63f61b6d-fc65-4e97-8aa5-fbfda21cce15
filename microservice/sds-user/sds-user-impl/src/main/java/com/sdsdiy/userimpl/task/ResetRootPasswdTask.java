package com.sdsdiy.userimpl.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.sdsdiy.common.base.constant.RedisConstant;
import com.sdsdiy.common.base.helper.Digest;
import com.sdsdiy.common.base.helper.Encodes;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.coreconfig.util.DingDingUtil;
import com.sdsdiy.userimpl.service.MerchantSysUserService;
import com.sdsdiy.userimpl.service.tenant.TenantSysUserService;
import com.sdsdiy.userimpl.util.RandomPwdUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: jie_zheng
 * @Date: 2021/12/13 20:56
 * @Description: 凌晨3点执行 重置root 密码
 */
@Slf4j
@Component
public class ResetRootPasswdTask {

    private final static String RESET_ROOT_API = "https://oapi.dingtalk.com/robot/send?access_token=57c3d2c8fd98ba0b32c06a7655fabb03bd0f122e590bdff57a89a30ab5656791";

    @Resource
    private MerchantSysUserService merchantSysUserService;


    @Resource
    private TenantSysUserService tenantSysUserService;

    @Resource
    private RedisUtil redisUtil;


    /**
     * 更新租户的状态,过期，status设为失效invalid
     */
    @XxlJob("reset-root-password")
    public ReturnT<String> resetRootPassword(String param) {
        XxlJobLogger.log("begin agentMerchant status update task");
        log.info("更新 merchant Root 密码");
        StringBuilder dingTalkMsg = new StringBuilder();
        DateTime beginOfDay = DateUtil.beginOfDay(new Date());
        long beginOfDayTime = beginOfDay.getTime();

        byte[] salt = Digest.generateSalt(8);
        String passwd = RandomPwdUtil.getRandomPwd(8);
        String hashPassword = Encodes.encodeHex(Digest.sha1(passwd.getBytes(), salt, 1024));
        String encodeSalt = Encodes.encodeHex(salt);
        merchantSysUserService.modifyRootPassword(encodeSalt, hashPassword);
        dingTalkMsg.append("修改密码-商户ROOT密码[" + passwd + "] success \n");
        log.info("更新 商户 Root 密码");

        salt = Digest.generateSalt(8);
        passwd = RandomPwdUtil.getRandomPwd(8);
        hashPassword = Encodes.encodeHex(Digest.sha1(passwd.getBytes(), salt, 1024));
        encodeSalt = Encodes.encodeHex(salt);
        tenantSysUserService.modifyRootPassword(encodeSalt, hashPassword);
        dingTalkMsg.append("修改密码-租户ROOT密码[" + passwd + "] success \n");



        passwd = RandomPwdUtil.getSimpleRandomPwd(8);
        redisUtil.set(RedisConstant.KEY_COMMON_PASSWORD,passwd);
        dingTalkMsg.append("修改密码-官方素材[" + passwd + "] success \n");


//        DingDingUtil.sendDDMessage(RESET_ROOT_API, dingTalkMsg.toString(), false);
        log.info("[结束]更新 租户 root 密码");
        String msg = "更新root 密码成功,截止时间在[" + beginOfDay + "]之前的租户";
        dingTalkMsg.append(msg);
        DingDingUtil.sendDDMessage(RESET_ROOT_API, dingTalkMsg.toString(), false);
        log.info(dingTalkMsg.toString());



        return new ReturnT<>(msg);
    }
}
