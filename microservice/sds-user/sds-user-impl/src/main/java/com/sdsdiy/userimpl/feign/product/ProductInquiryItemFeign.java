package com.sdsdiy.userimpl.feign.product;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.core.config.ExceptionErrorDecoder;
import com.sdsdiy.productapi.api.product.ProductInquiryItemApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "SERVICE-PRODUCT", contextId = "ProductInquiryItemFeign", url = MicroServiceEndpointConstant.SERVICE_PRODUCT, configuration = ExceptionErrorDecoder.class)
public interface ProductInquiryItemFeign extends ProductInquiryItemApi {


}
