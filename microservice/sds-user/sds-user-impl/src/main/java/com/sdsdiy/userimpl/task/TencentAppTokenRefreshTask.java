package com.sdsdiy.userimpl.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.userdata.dto.factory.TenantAppAccessTokenDTO;
import com.sdsdiy.userimpl.config.TencentWXTemplate;
import com.sdsdiy.userimpl.service.tenant.TenantModifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: jie_zheng
 * @Date: 2021/08/26
 * @Description:
 */
@Slf4j
@Component
public class TencentAppTokenRefreshTask {

    private static final String FORMAT_TENCENT_APP_TOKEN_KEY = "tencent:app:%s";

    @Resource
    private TencentWXTemplate tencentWXTemplate;


    @Resource
    private RedisUtil redisUtil;

    /**
     * 更新租户的状态,过期，status设为失效invalid
     */
    @XxlJob("tencent-app-token-refresh")
    public ReturnT<String> tencentAppTokenRefresh(String param) {
        XxlJobLogger.log("begin  TencentAppTokenRefreshTask ");
        log.info("[开始]刷新微信小程序token");
        DateTime beginOfDay = DateUtil.beginOfDay(new Date());
        TenantAppAccessTokenDTO accessToken = null;
        try {
            accessToken = tencentWXTemplate.getAccessToken();
            String key = String.format(FORMAT_TENCENT_APP_TOKEN_KEY, accessToken.getAppId());
            redisUtil.set(key, accessToken.getAccessToken(), accessToken.getExpiresSec());
        } catch (Exception e) {
            log.error("error get wx app token", e);
        }
        Assert.validateNull(accessToken, "access token is not null");
        XxlJobLogger.log("end TencentAppTokenRefreshTask", JSON.toJSONString(accessToken));
        log.info("[结束]更新租户的状态");

        String msg = "更新租户的状态,截止时间在[" + beginOfDay + "]之前的租户";
        return new ReturnT<>(msg);
    }
}
