package com.sdsdiy.userimpl.feign.order;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.orderapi.api.print.fo.FoProductionPrintTemplateApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2024/4/10
 */
@FeignClient(value = "SERVICE-ORDER", contextId = "FoProductionPrintTemplateFeign", url = MicroServiceEndpointConstant.SERVICE_ORDER)
public interface FoProductionPrintTemplateFeign extends FoProductionPrintTemplateApi {


}