
package com.sdsdiy.userimpl.util;


import com.sdsdiy.common.base.entity.dto.SqlMsg;
import com.sdsdiy.common.base.helper.IdGenerator;
import com.sdsdiy.core.mq.core.FifoMsg;

import com.sdsdiy.core.mq.core.RocketMQTemplate;

import com.sdsdiy.core.mq.queue.OutsiteTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class UserSqsSender {

    @Value("${admin.apihost}")
    private String adminApiHost;

    private final RocketMQTemplate rocketMQTemplate;
    public final static String PRODUCT_DATABASE = "sds_product";

    public void sendToSqsSync(String table, String type, List<Long> ids, String database) {
        for (Long id : ids) {
            SqlMsg sqlMsg = new SqlMsg();
            sqlMsg.setId(id);
            sqlMsg.setTable(table);
            sqlMsg.setApiHost(adminApiHost);
            sqlMsg.setDatabase(database);
            sqlMsg.setType(type);
            String md5 = IdGenerator.nextStringId();
            sqlMsg.setMd5(md5);

            FifoMsg fifoMsg = new FifoMsg(sqlMsg, table);
            rocketMQTemplate.sendDelay(RocketMqTopicConst.EVENT_OUT_DELAY, OutsiteTagConst.FIFO_OUTSITE_SQL_ACCEPT, fifoMsg, Duration.ofSeconds(1));
        }
    }

}


