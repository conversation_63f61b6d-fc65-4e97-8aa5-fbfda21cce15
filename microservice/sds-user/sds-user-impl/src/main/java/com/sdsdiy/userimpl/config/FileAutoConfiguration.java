package com.sdsdiy.userimpl.config;

import com.alibaba.fastjson.JSON;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * s3 自动配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({OtherPlatformsProperties.class})
public class FileAutoConfiguration {

    /**
     * OSS操作模板
     *
     * @return OSS操作模板
     */
    @Bean
    public TencentWXTemplate tencentWXTemplate(OtherPlatformsProperties properties) {
        System.out.println("init image external tools bean" + JSON.toJSONString(properties));
        return new TencentWXTemplate(properties);
    }
}
