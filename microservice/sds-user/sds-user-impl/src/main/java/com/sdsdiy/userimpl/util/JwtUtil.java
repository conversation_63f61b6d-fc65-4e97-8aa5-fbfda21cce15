package com.sdsdiy.userimpl.util;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Maps;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/20
 */
public class JwtUtil {
    //Sample method to construct a JWT
    public static String createJWT(Map<String, Object> map, long ttlMillis) {

        //The JWT signature algorithm we will be using to sign the token
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        long nowMillis = System.currentTimeMillis();
        //We will sign our JWT with our ApiKey secret
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary("_321cxaAx356@1^d3468");
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());


        JwtBuilder builder = Jwts.builder()
                .setClaims(map)
                .signWith(signatureAlgorithm, signingKey);

        //if it has been specified, let's add the expiration
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            builder.setExpiration(exp);
        }

        //Builds the JWT and serializes it to a compact, URL-safe string
        return builder.compact();
    }

    public static Claims parseJWT(String jwt) {
        Claims claims;
        //This line will throw an exception if it is not a signed JWS (as expected)
        try {
            claims = Jwts.parser()
                    .setSigningKey(DatatypeConverter.parseBase64Binary("_321cxaAx356@1^d3468"))
                    .parseClaimsJws(jwt).getBody();
        } catch (Exception e) {
            return null;
        }

        return claims;
    }

    public static void main(String[] args) throws Exception {
        Map<String, Object> info = Maps.newHashMap();
        info.put("phone", "123456789");
        info.put("code", "123");
        String token = JwtUtil.createJWT(info, 5 * 60 * 1000);
//        Thread.sleep(3000L);
        System.out.println(JSON.toJSONString(JwtUtil.parseJWT("eyJhbGciOiJIUzI1NiJ9.eyJtZXJjaGFudE5vIjoi5pys5Zyw5rWL6K-VdXNlciIsInBob25lIjoiMTgwNjUwMjIxNjEiLCJwaG9uZUNvdW50cnlDb2RlIjoiODYiLCJ2ZXJpZnlDb2RlIjoiNTAwMiIsImV4cCI6MTYyNTEwNjY0MX0.5f6CHvQT6y-dZAxekFZ3pqXleZPmaHVIj-0nJkO7qp8")));
    }

}
