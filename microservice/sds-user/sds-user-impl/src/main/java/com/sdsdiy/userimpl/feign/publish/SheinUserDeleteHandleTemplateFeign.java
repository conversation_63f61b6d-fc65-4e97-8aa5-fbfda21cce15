package com.sdsdiy.userimpl.feign.publish;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.publishapi.api.shein.SheinUserDeleteHandleTemplateApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "service-publish", contextId = "SheinUserDeleteHandleTemplateFeign", url = MicroServiceEndpointConstant.SERVICE_PUBLISH)
public interface SheinUserDeleteHandleTemplateFeign extends SheinUserDeleteHandleTemplateApi {
}
