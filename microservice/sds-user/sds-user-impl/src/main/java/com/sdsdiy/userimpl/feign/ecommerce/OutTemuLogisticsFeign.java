package com.sdsdiy.userimpl.feign.ecommerce;

import com.sdsdiy.common.base.constant.MicroServiceNameConst;
import com.sdsdiy.outecoapi.api.temu.TemuLogisticsApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = MicroServiceNameConst.OUTECO, contextId = "OutTemuLogisticsFeign", url = "${outeco.domain.us}")
public interface OutTemuLogisticsFeign extends TemuLogisticsApi {
}
