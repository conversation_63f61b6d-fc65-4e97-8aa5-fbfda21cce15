package com.sdsdiy.userimpl.task;

import com.sdsdiy.userimpl.service.MerchantMainBusinessService;
import com.sdsdiy.userimpl.service.crm.CrmUserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * shanbin_sun 商户主营业务
 */
@Component
@Slf4j
public class MerchantMainBusinessTask {


    @Resource
    private MerchantMainBusinessService merchantMainBusinessService;


    @XxlJob("user_merchant_main_business")
    public ReturnT<Boolean> merchantMainBusinessTask(String param) {
        XxlJobLogger.log("【开始】商户主营业务");
        log.info("【开始】商户主营业务");

        merchantMainBusinessService.init();

        ReturnT<Boolean> ret = new ReturnT<>(true);
        ret.setMsg("执行成功！");
        log.info("【结束】商户主营业务");
        XxlJobLogger.log("【结束】商户主营业务");
        return ret;
    }

}
