package com.sdsdiy.userimpl.task;

import cn.hutool.core.collection.CollUtil;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.user.UserTagConst;
import com.sdsdiy.userapi.constant.merchant.MerchantUserAuthLoginConstant;
import com.sdsdiy.userapi.dto.msg.MerchantUserAuthLoginWeXinTokenMsg;
import com.sdsdiy.userimpl.entity.po.merchant.MerchantUserAuthLogin;
import com.sdsdiy.userimpl.service.merchant.MerchantUserAuthLoginService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: hzh
 * @Date: 2022/1/24 14:57
 * 每月对统计数量清零
 */
@Component
@Slf4j
public class MerchantAuthLoginWeXinTokenUpdateTask {

    @Resource
    private MerchantUserAuthLoginService merchantUserAuthLoginService;
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @XxlJob("merchantAuthLoginWeXinTokenUpdate")
    public ReturnT<Boolean> merchantAuthLoginWeXinTokenUpdateTask(String param) {
        XxlJobLogger.log("【开始】merchantAuthLoginWeXinTokenUpdateTask");
        log.info("【开始】merchantAuthLoginWeXinTokenUpdateTask");
        ReturnT<Boolean> ret = new ReturnT<>(true);
        List<MerchantUserAuthLogin> merchantUserAuthLogins = merchantUserAuthLoginService.getWaitRefreshAccessToken();
        if(CollUtil.isEmpty(merchantUserAuthLogins)){
            ret.setMsg("不存在refreshToken有效的数据");
            log.info("【结束】merchantAuthLoginWeXinTokenUpdateTask");
            XxlJobLogger.log("【结束】merchantAuthLoginWeXinTokenUpdateTask");
            return ret;
        }
        sendMsg(merchantUserAuthLogins);
        ret.setMsg("执行成功！");
        log.info("【结束】merchantAuthLoginWeXinTokenUpdateTask");
        XxlJobLogger.log("【结束】merchantAuthLoginWeXinTokenUpdateTask");
        return ret;
    }

    private void sendMsg(List<MerchantUserAuthLogin> merchantUserAuthLogins) {
        List<MerchantUserAuthLogin> updateAuths= Lists.newArrayList();
        List<MerchantUserAuthLoginWeXinTokenMsg> msgs= Lists.newArrayList();
        merchantUserAuthLogins.forEach(merchantUserAuthLogin -> {
            MerchantUserAuthLoginWeXinTokenMsg msg=new MerchantUserAuthLoginWeXinTokenMsg();
            msg.setId(merchantUserAuthLogin.getId());
            msg.setUserId(merchantUserAuthLogin.getMerchantSysUserId());
            msgs.add(msg);

            MerchantUserAuthLogin update=new MerchantUserAuthLogin();
            update.setId(merchantUserAuthLogin.getId());
            update.setRefreshTokenStatus(MerchantUserAuthLoginConstant.RefreshTokenStatusEnum.ONGOING.getCode());
            updateAuths.add(update);
            });

        merchantUserAuthLoginService.updateByIds(updateAuths);
        rocketMQTemplate.sendExtensiveBatch(RocketMqTopicConst.EVENT_USER, UserTagConst.TAG_MERCHANT_AUTH_LOGIN_WEXIN_TOKEN_REFRESH,msgs);
    }


}
