package com.sdsdiy.userimpl.task;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.user.UserTagConst;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userdata.dto.merchant.MerchantSetMealUpdateDto;
import com.sdsdiy.userimpl.service.MerchantService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 套餐每日更新
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantPermissionSetMealTask {

    private final MerchantService merchantService;
    private final RocketMQTemplate rocketMQTemplate;

    @LogTraceId
    @XxlJob("merchant-set-meal-update")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("商户权限日处理开始");
        log.info("商户权限日处理开始");
        sendMq(param);
        XxlJobLogger.log("商户权限日处理结束");
        return ReturnT.SUCCESS;
    }

    public void sendMq(String param) {
        List<Long> merchantIds;
        if (StrUtil.isNotBlank(param)) {
            merchantIds = Arrays.stream(param.split(",")).map(Long::valueOf).collect(Collectors.toList());
            merchantService.updateMerchantIsUpdate(merchantIds,BasePoConstant.NO);
        } else {
            merchantService.updateMerchantIsUpdate(null,BasePoConstant.NO);
            List<MerchantRespDto> merchantRespDtos = merchantService.allMerchant("", "");
            merchantIds = merchantRespDtos.stream().map(BaseDTO::getId).collect(Collectors.toList());
        }
        List<MerchantSetMealUpdateDto> dtos= Lists.newArrayList();
        for (Long merchantId : merchantIds) {
            MerchantSetMealUpdateDto dto=new MerchantSetMealUpdateDto();
            dto.setMerchantId(merchantId);
            dtos.add(dto);
        }
        rocketMQTemplate.sendExtensiveBatch(RocketMqTopicConst.EVENT_USER, UserTagConst.MERCHANT_SET_MEAL_UPDATE,dtos);
        log.info("商户权限日处理结束");
    }
}