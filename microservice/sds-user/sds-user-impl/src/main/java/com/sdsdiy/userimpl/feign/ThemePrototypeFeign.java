package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.productapi.api.prototype.ThemePrototypeApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 */
@FeignClient(value = "service-product", contextId = "ThemePrototypeFeign", url = MicroServiceEndpointConstant.SERVICE_PRODUCT)
public interface ThemePrototypeFeign extends ThemePrototypeApi {

}