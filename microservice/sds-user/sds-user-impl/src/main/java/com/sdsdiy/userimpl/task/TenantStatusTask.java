package com.sdsdiy.userimpl.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.sdsdiy.userimpl.service.tenant.TenantModifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Author: zmy
 * @Date: 2021/6/21 20:56
 * @Description:
 */
@Slf4j
@Component
public class TenantStatusTask {

    @Resource
    private TenantModifyService tenantModifyService;

    /**更新租户的状态,过期，status设为失效invalid*/
    @XxlJob("tenant-status")
    public ReturnT<String> updateStatus(String param) {
        XxlJobLogger.log("begin agentMerchant status update task");
        log.info("[开始]更新租户的状态");
        DateTime beginOfDay = DateUtil.beginOfDay(new Date());
        long beginOfDayTime = beginOfDay.getTime();
        boolean result = tenantModifyService.updateStatusTask(beginOfDayTime);
        XxlJobLogger.log("end tenant status update task,updateCount>0:{}",result);
        log.info("[结束]更新租户的状态");
        String msg="更新租户的状态,截止时间在["+beginOfDay+"]之前的租户";
        return new ReturnT<>(msg);
    }
}
