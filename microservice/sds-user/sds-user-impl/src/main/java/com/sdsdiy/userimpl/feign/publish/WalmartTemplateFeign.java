package com.sdsdiy.userimpl.feign.publish;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.publishapi.api.walmart.WalmartTemplateApi;
import org.springframework.cloud.openfeign.FeignClient;


@FeignClient(name = "service-publish", contextId = "WalmartTemplateFeign", url = MicroServiceEndpointConstant.SERVICE_PUBLISH)
public interface WalmartTemplateFeign extends WalmartTemplateApi {
}
