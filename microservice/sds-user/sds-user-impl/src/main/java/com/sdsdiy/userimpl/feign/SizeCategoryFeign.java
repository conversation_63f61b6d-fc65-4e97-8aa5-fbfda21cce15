package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.productapi.api.SizeCategoryApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/11/2
 */
@FeignClient(name = "service-product", contextId = "ColorFeign",url = MicroServiceEndpointConstant.SERVICE_PRODUCT)
public interface SizeCategoryFeign extends SizeCategoryApi {
}
