package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.materialapi.api.official.OfficialMaterialApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/6/18
 */
@FeignClient(name = "service-material", contextId = "OfficialMaterialFeign", url = MicroServiceEndpointConstant.SERVICE_MATERIAL)
public interface OfficialMaterialFeign extends OfficialMaterialApi {
}
