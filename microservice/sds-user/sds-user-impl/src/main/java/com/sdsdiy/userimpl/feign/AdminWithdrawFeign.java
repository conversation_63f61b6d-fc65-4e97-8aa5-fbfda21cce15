package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.paymentapi.api.AdminWithdrawApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/11/12
 */
@FeignClient(name = "service-payment", contextId = "AdminWithdrawFeign", url = MicroServiceEndpointConstant.SERVICE_PAYMENT)
public interface AdminWithdrawFeign extends AdminWithdrawApi {
}
