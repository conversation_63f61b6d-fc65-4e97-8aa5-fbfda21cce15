package com.sdsdiy.userimpl.task;

import com.sdsdiy.userimpl.service.crm.CrmUserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * shanbin_sun 转化率
 */
@Component
@Slf4j
public class ConversionRateCalculationTask {


    @Resource
    private CrmUserService crmUserService;


    @XxlJob("user_conversion_rate_calculation")
    public ReturnT<Boolean> officialMaterialCalendarTask(String param) {
        XxlJobLogger.log("【开始】crm转换率计算初始化");
        log.info("【开始】crm转换率计算初始化");

        crmUserService.conversionRateCalculation();

        ReturnT<Boolean> ret = new ReturnT<>(true);
        ret.setMsg("执行成功！");
        log.info("【结束】crm转换率计算初始化");
        XxlJobLogger.log("【结束】crm转换率计算初始化");
        return ret;
    }

}
