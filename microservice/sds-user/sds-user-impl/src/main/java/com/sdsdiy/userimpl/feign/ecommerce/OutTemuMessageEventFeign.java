package com.sdsdiy.userimpl.feign.ecommerce;

import com.sdsdiy.common.base.constant.MicroServiceNameConst;
import com.sdsdiy.outecoapi.api.temu.TemuMessageEventApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = MicroServiceNameConst.OUTECO, contextId = "OutTemuMessageEventFeign", url = "${outeco.domain.us}")
public interface OutTemuMessageEventFeign extends TemuMessageEventApi {
}
