package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.productapi.api.ProductApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @Author: zmy
 * @Date: 2020/5/22 18:16
 * @Description:
 */
@FeignClient(name = "service-product", contextId = "ProductFeign", url = MicroServiceEndpointConstant.SERVICE_PRODUCT)
public interface ProductFeign extends ProductApi {

}
