package com.sdsdiy.userimpl.feign.product;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.productapi.api.ProductQueryApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "SERVICE-PRODUCT", contextId = "ProductQueryFeign", url = MicroServiceEndpointConstant.SERVICE_PRODUCT)
public interface ProductQueryFeign extends ProductQueryApi {
}
