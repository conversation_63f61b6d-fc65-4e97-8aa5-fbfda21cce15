package com.sdsdiy.userimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.productapi.api.CategoryApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2021/6/29
 */
@FeignClient(name = "service-product", contextId = "CategoryFeign", url = MicroServiceEndpointConstant.SERVICE_PRODUCT)
public interface CategoryFeign extends CategoryApi {
}
