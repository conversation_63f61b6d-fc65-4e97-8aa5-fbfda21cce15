package com.sdsdiy.userimpl.feign.publish;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.publishapi.api.temu.TemuUserDeleteHandleTemplateApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "service-publish", contextId = "TemuUserDeleteHandleTemplateFeign", url = MicroServiceEndpointConstant.SERVICE_PUBLISH)
public interface TemuUserDeleteHandleTemplateFeign extends TemuUserDeleteHandleTemplateApi {
}
