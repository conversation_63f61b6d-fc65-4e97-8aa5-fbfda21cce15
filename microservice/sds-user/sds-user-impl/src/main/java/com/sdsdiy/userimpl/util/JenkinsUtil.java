package com.sdsdiy.userimpl.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/7
 */
@Log4j2
@Component
public class JenkinsUtil {
    /**
     * 独立部署首页-刷新商户index文件seo
     */
    public static String JENKINS_REFRESH_MERCHANT_URL;

    @Value("${jenkins.refresh_merchant_url}")
    private void setJenkinsRefreshMerchantUrl(String jenkinsRefreshMerchantUrl) {
        JENKINS_REFRESH_MERCHANT_URL = jenkinsRefreshMerchantUrl;
    }

    public static void build(String url) {
        HttpResponse response = HttpRequest.get(url).execute();
        if (response.isOk()) {
            return;
        }
        log.error(response.getStatus() + "-jenkins build fail：" + response.body());
    }

    public static void buildWithParam(String url, Map<String, Object> param) {
        HttpResponse response = HttpRequest.get(url + "&" + HttpUtil.toParams(param)).execute();
        if (response.isOk()) {
            return;
        }
        log.error(response.getStatus() + "-jenkins build fail：" + response.body());
    }

//    public static void main(String[] args) {
//        Map<String, Object> param = new HashMap<>(1);
//        param.put("domain", "yiduoyun");
//        buildWithParam(JENKINS_REFRESH_MERCHANT_URL, param);
    // http://jenkinsjntm.sdsdiy.com/job/k8s-test/job/web-refresh-merchant/buildWithParameters?token=abc&domain=yiduoyun
//    }
}
