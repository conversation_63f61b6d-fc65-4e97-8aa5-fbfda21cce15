package com.ps.ps.listener;


import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.ps.dto.UserDesignSaveDto;
import com.ps.ps.service.TaskService;
import com.ps.ps.service.UserDesignSaveService;
import com.ps.support.mq.MqListenerRegisterCondition;
import com.ps.system.service.UserService;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.ziguang.base.dto.CompounDTO;
import com.ziguang.base.dto.DesignTaskDTO;
import com.ziguang.base.model.User;
import com.ziguang.base.model.UserDesignSave;
import com.ziguang.base.support.JsonUtil;
import com.ziguang.base.support.LockUtil;
import com.ziguang.base.support.contant.CommonStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;

import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

//
//


/***
 *素材出单增加
 * 成品下单数增加
 * <AUTHOR>
 * @date 2020/9/2 15:09
 */

@Service
@Slf4j
@Conditional(MqListenerRegisterCondition.class)
@RocketMQMessageListener(topic = RocketMqTopicConst.EVENT_USER_DESIGN_TO_MQ, consumerGroup = "GID_EVENT_USER_DESIGN_TO_MQ")
public class DesignTaskEventListener implements RocketMQListener {

    @Resource
    private UserDesignSaveService userDesignSaveService;
    @Resource
    TaskService taskService;
    @Resource
    UserService userService;

    @Autowired
    private RedissonClient redissonClient;

    private static final long PAYMENT_LOCK_TIME = 5000L;
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    //等待10ms
    private static final long LOCK_WAIT_TIME = 10L;

    @LogTraceId
    public void onMessage(UserDesignSaveDto userDesignSaveDto) {
        Long id = userDesignSaveDto.getId();
        log.info("userDesignSave-id={}",id);
        UserDesignSave userDesignSave = userDesignSaveService.get(id);
        LockUtil lockUtil = new LockUtil(redissonClient, "designtask:"+id, LOCK_WAIT_TIME, PAYMENT_LOCK_TIME);
        if (!lockUtil.lock()) {
            log.info("public class DesignTaskEventListener  lock {}", id);
            return ;
        }
        if(userDesignSave == null || userDesignSave.getStatus().equalsIgnoreCase(CommonStatus.ONLINE.getDesc())){
            return;
        }
        if(3 < userDesignSave.getFailedTime()){
            //错误三次不再处理
            return;
        }
        try {
            CompounDTO item = JsonUtil.toObject(userDesignSave.getItemData(),CompounDTO.class);
            User user = userService.findById(userDesignSave.getUserId());
            userDesignSaveDto.setTaskId(userDesignSave.getTaskId());
            log.info("userDesignSaveDto json={}",JSONUtil.toJsonStr(userDesignSaveDto));
            DesignTaskDTO designTaskDTO = taskService.syncPlatformSave(userDesignSaveDto, id, user.getMerchantId(), user.getId(), Lists.newArrayList(item), user, true, true, false);
            UserDesignSave update = new UserDesignSave();
            update.setStatus(CommonStatus.ONLINE.getDesc());
            if(!NumberUtils.greaterZero(userDesignSaveDto.getTaskId())){
                update.setTaskId(designTaskDTO.getId());
            }
            update.setId(id);
            userDesignSaveService.update(update);

        }catch (Exception e){
            log.error("DesignTaskEventListener error",e);
            UserDesignSave update = new UserDesignSave();
            update.setFailedTime(userDesignSave.getFailedTime() + 1);
            update.setId(id);
            userDesignSaveService.update(update);
//            mqTemplate.asyncSendMessageAfterCommit(TaskConstant.EVENT_USER_DESIGN_TO_MQ,userDesignSaveDto);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_USER_DESIGN_TO_MQ,userDesignSaveDto);
        }finally {
            lockUtil.unlock();
        }

    }



    @Override
    @LogTraceId
    public void consumeMsg(MessageView messageView) {
        ByteBuffer body = messageView.getBody();
        String receivedString = StandardCharsets.UTF_8.decode(body).toString();
        log.info("receivedString={}",receivedString);
        UserDesignSaveDto userDesignSave = JSONUtil.toBean(receivedString, UserDesignSaveDto.class);
        this.onMessage(userDesignSave);
        
    }



}

