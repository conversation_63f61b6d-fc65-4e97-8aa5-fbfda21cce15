package com.ps.ps.listener;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.dto.gateway.designproduct.GatewayProductDesignBatchHelper;
import com.ps.dto.gateway.designproduct.GatewayProductDesignBatchParam;
import com.ps.dto.gateway.designproduct.GatewayProductDesignBatchPrototypeLayerParam;
import com.ps.dto.gateway.designproduct.GatewayProductDesignBatchPrototypeParam;
import com.ps.ps.service.MerchantProductParentService;
import com.ps.ps.service.MerchantService;
import com.ps.ps.service.MaterialService;
import com.ps.ps.service.UserDesignSaveService;
import com.ps.ps.service.userconfig.CombineRuleService;
import com.ps.support.Assert;
import com.ps.support.contant.TaskConstant;
import com.ps.support.mq.MqListenerRegisterCondition;
import com.ps.support.mq.MqTemplate;
import com.ps.support.utils.StringUtils;
import com.ps.system.service.UserService;
import com.ps.util.MapUtil;
import com.sdsdiy.common.base.helper.IdSearchHelper;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.designproduct.DesignProductConsumerConst;
import com.sdsdiy.core.mq.queue.designproduct.DesignProductTagConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.ziguang.base.dto.*;
import com.ziguang.base.model.*;
import com.ziguang.base.support.LockUtil;
import com.ziguang.sdsdesignproduct.api.DesignTaskApiInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;

import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

//
//


/***
 *素材出单增加
 * 成品下单数增加
 * <AUTHOR>
 * @date 2020/9/2 15:09
 */

@Service
@Slf4j
@Conditional(MqListenerRegisterCondition.class)
@RocketMQMessageListener(topic = RocketMqTopicConst.DESIGN_PRODUCT,
        consumerGroup = DesignProductConsumerConst.GID_GATEWAY_DESIGN_BATCH,
        tag = DesignProductTagConst.EVENT_GATEWAY_DESIGN_BATCH)
public class GatewayDesignTaskListener implements RocketMQListener {

    @Resource
    private UserDesignSaveService userDesignSaveService;
    @Autowired
    private DesignTaskApiInterface designTaskApiInterface;

    @Resource
    MaterialService materialService;

    @Autowired
    S3ServiceV2 s3ServiceV2;

    @Autowired
    MerchantService merchantService;

    @Autowired
    MerchantProductParentService merchantProductParentService;
    @Resource
    UserService userService;
    @Autowired
    private RedissonClient redissonClient;

    private static final long PAYMENT_LOCK_TIME = 5000L;
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    //等待10ms
    private static final long LOCK_WAIT_TIME = 10L;

    @Override
    public void consumeMsg(MessageView messageView) {
        IdSearchHelper dto = RocketMQUtil.getBodyBean(messageView, IdSearchHelper.class);
        onMessage(dto.getId());
    }
    @LogTraceId
    public void onMessage(Long id) {
        UserDesignSave userDesignSave = userDesignSaveService.get(id);
        LockUtil lockUtil = new LockUtil(redissonClient, "designtask:" + id, LOCK_WAIT_TIME, PAYMENT_LOCK_TIME);
        if (!lockUtil.lock()) {
            log.info("public class DesignTaskEventListener  lock {}", id);
            return;
        }
        if (userDesignSave == null) {
            return;
        }
        if (3 < userDesignSave.getFailedTime()) {
            //错误三次不再处理
            return;
        }
        try {
            User user = userService.findById(userDesignSave.getUserId());
            Merchant merchant = merchantService.findById(user.getMerchantId());
            GatewayProductDesignBatchParam param = JSONObject.parseObject(userDesignSave.getItemData(), GatewayProductDesignBatchParam.class);
            MerchantProductParent merchantProductParent = merchantProductParentService.findByMerchantIdProductId(user.getMerchantId(), param.getProductParentId(), null);
            Assert.validateNull(merchantProductParent, "请先加入产品库");
            List<MerchantProductCompoundDto> merchantProductCompoundDtos = merchantProductParentService.gatewayCompoundData(user,user.getMerchantId(), merchantProductParent, merchant.getTenantId(), param.getPrototypeGroupId());
            ObjectMapper mapper = new ObjectMapper();
            log.info("gateway merchantProductChildrenDtos res={}", JSONUtil.toJsonStr(merchantProductCompoundDtos));
            CompounTaskDTO compounTaskDTO = new CompounTaskDTO();
            CompounDTO compounDTO = new CompounDTO();
            compounTaskDTO.setCompounDTOList(Lists.newArrayList(compounDTO));
            compounDTO.setPrototypes(Lists.newArrayList());
            compounDTO.setMerchantProductParentId(merchantProductParent.getId());
            compounDTO.setCombineRuleType(CombineRuleService.MULTI_MATERIAL);

            MapUtil<Long, GatewayProductDesignBatchHelper> designBatchHelperMapUtil = MapUtil.instance();
            for (MerchantProductCompoundDto merchantProductCompoundDto : merchantProductCompoundDtos) {
                Long prototypeId = merchantProductCompoundDto.getPrototypeId();
                if (!designBatchHelperMapUtil.exit(prototypeId)) {
                    designBatchHelperMapUtil.add(prototypeId, new GatewayProductDesignBatchHelper(prototypeId));
                }
                GatewayProductDesignBatchHelper gatewayProductDesignBatchHelper = designBatchHelperMapUtil.getV(prototypeId);
                gatewayProductDesignBatchHelper.addProductId(merchantProductCompoundDto.getProduct().getId());
                for (PrototypePsd prototypePsd : merchantProductCompoundDto.getPrototypePsds()) {
                    gatewayProductDesignBatchHelper.addPsdId(prototypePsd.getId());
                }
                for (PrototypeLayer prototypeLayer : merchantProductCompoundDto.getPrototypeLayers()) {
                    gatewayProductDesignBatchHelper.addDistinctLayer(prototypeLayer.getName(), prototypeLayer);
                }
            }
            for (GatewayProductDesignBatchPrototypeParam prototype : param.getPrototypes()) {
                for (GatewayProductDesignBatchHelper value : designBatchHelperMapUtil.getValues()) {
                    TaskDto taskDto = new TaskDto();
                    taskDto.setProductIds(value.getProductIds());
                    taskDto.setPrototypeId(value.getPrototypeId());
                    taskDto.setPsdIds(value.getPsdIds());
                    List<TaskLayerDto> taskLayerDtos = Lists.newArrayList();
                    taskDto.setLayers(taskLayerDtos);
                    for (GatewayProductDesignBatchPrototypeLayerParam layer : prototype.getLayers()) {
                        String fileCode = null;
                        ImageUpload imageUpload = null ;
                        if(NumberUtils.greaterZero(layer.getMaterialId())){
                            Material material = materialService.findById(layer.getMaterialId());
                            if(material != null && StringUtils.isNotBlank(material.getFileCode())){
                                fileCode = material.getFileCode();
                                imageUpload = new ImageUpload();
                                imageUpload.setWidth(material.getWidth());
                                imageUpload.setHeight(material.getHeight());
                                imageUpload.setFileCode(fileCode);
                            }
                        }

                        if(StringUtils.isBlank(fileCode)){
                            if (StringUtils.isBlank(layer.getImageUrl())) {
                                continue;
                            }
                            imageUpload = s3ServiceV2.getInfoByUrlLocal(layer.getImageUrl(), null);
                        }


                        PrototypeLayer prototypeLayer = value.getLayerByName(layer.getLayerName());
                        if(prototypeLayer == null){
                            continue;
                        }
                        TaskLayerDto taskLayerDto = new TaskLayerDto();
                        taskLayerDto.setLayerId(prototypeLayer.getId());
                        taskLayerDto.setFitLevel(1);
                        taskLayerDto.setImageHeight(imageUpload.getHeight().toString());
                        taskLayerDto.setImageWidth(imageUpload.getWidth().toString());
                        taskLayerDto.setContent(s3ServiceV2.getCommonFilePath(imageUpload.getFileCode()));
                        //填充
                        taskLayerDto.setResizeMode(layer.getResizeMode());
                        taskLayerDtos.add(taskLayerDto);
                    }
                    if(CollectionUtil.isNotEmpty(taskLayerDtos)) {
                        compounDTO.getPrototypes().add(taskDto);
                    }
                }


            }


            String logMessage = "add task json:" + mapper.writeValueAsString(compounTaskDTO);
            log.info(logMessage);
            DesignTaskSaveRequest designTaskSaveRequest = new DesignTaskSaveRequest();
            designTaskSaveRequest.setItems(compounTaskDTO.getCompounDTOList());
            designTaskSaveRequest.setTaskId(userDesignSave.getTaskId());
            designTaskSaveRequest.setItems(compounTaskDTO.getCompounDTOList());
            designTaskSaveRequest.setMerchantId(user.getMerchantId());
            designTaskSaveRequest.setUserId(user.getId());
            designTaskSaveRequest.setUser(user);
            designTaskSaveRequest.setSyncResult(false);
            designTaskSaveRequest.setRebuild(false);
            designTaskApiInterface.save(designTaskSaveRequest);

            userDesignSaveService.updateSuccess(id);
        } catch (Exception e) {
            log.error("DesignTaskEventListener error", e);
            UserDesignSave update = new UserDesignSave();
            update.setFailedTime(userDesignSave.getFailedTime() + 1);
            update.setErrorMsg(e.getMessage());
            update.setId(id);
            userDesignSaveService.update(update);

            IdSearchHelper idSearchHelper = new IdSearchHelper();
            idSearchHelper.setId(id);
            //mqTemplate.sendMessage(TaskConstant.EVENT_GATEWAY_DESIGN_BATCH, idSearchHelper);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.DESIGN_PRODUCT, DesignProductTagConst.EVENT_GATEWAY_DESIGN_BATCH, idSearchHelper);
        }finally {
            lockUtil.unlock();
        }

    }

    //@KafkaListener(topics = TaskConstant.EVENT_GATEWAY_DESIGN_BATCH, groupId = "GID_GATEWAY_DESIGN_BATCH")
    public void onMessage(ConsumerRecord<String, String> record, Acknowledgment ack, @Header(KafkaHeaders.RECEIVED_TOPIC) String topic) {
        Optional<String> message = Optional.ofNullable(record.value());
        if (message.isPresent()) {
            IdSearchHelper idSearchHelper = JSONUtil.toBean(message.get(), IdSearchHelper.class);
            onMessage(idSearchHelper.getId());
        }
        ack.acknowledge();
    }

}

