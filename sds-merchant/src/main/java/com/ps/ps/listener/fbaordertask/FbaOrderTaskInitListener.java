package com.ps.ps.listener.fbaordertask;

import cn.hutool.json.JSONUtil;
import com.ps.ps.service.FbaOrderTaskService;
import com.ps.ps.service.OrderService;
import com.ps.support.mq.MqListenerRegisterCondition;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.queue.OrderGroupConst;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.sdsdiy.orderapi.constant.event.FbaOrderTaskConstant;
import com.sdsdiy.orderapi.constant.event.message.FbaOrderTaskEventMessage;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.ziguang.base.model.FbaOrderTask;
import com.ziguang.base.model.Order;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;

import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/8/13
 */
@Service
@Slf4j
@Conditional(MqListenerRegisterCondition.class)
@RocketMQMessageListener(topic = RocketMqTopicConst.EVENT_ORDER,
        tag = OrderTagConst.TOPIC_FBA_ORDER_TASK_CREATE,
        consumerGroup = OrderGroupConst.GID_CONSUMER_FBA_ORDER_TASK_CREATE)
public class FbaOrderTaskInitListener implements RocketMQListener {
    @Resource
    private FbaOrderTaskService fbaOrderTaskService;
    @Resource
    private OrderService orderService;
    @Override
    public void consumeMsg(MessageView messageView) {
        FbaOrderTaskEventMessage dto = RocketMQUtil.getBodyBean(messageView, FbaOrderTaskEventMessage.class);
        onMessage(dto);
    }
    public void onMessage(FbaOrderTaskEventMessage msg) {
        log.info("创建fbaordertask消费开始，orderId={}", msg.getOrderId());
        Long orderId = msg.getOrderId();
        Order order = orderService.findById(orderId);
        if(null==order||order.getStatus()== OrderStatus.NONE.getStatus()){
            return;
        }
        FbaOrderTask fbaOrderTask = fbaOrderTaskService.findByOrderId(order.getId());
        if (fbaOrderTask == null) {
            fbaOrderTaskService.create(order);
        }
        log.info("创建fbaordertask消费结束，orderId={}", msg.getOrderId());
    }

}
