package com.ps.ps.config;

import brave.Tracing;
import com.ps.traceid.TraceAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description: 切面
 * @Author: zmy
 * @Date: 2021/12/14 13:05
 */
@Configuration
public class AOPConfiguration {
    /**
     * 注入traceId切面bean
     */
    @Bean
    public TraceAspect traceIdAspect(Tracing tracing){
        return new TraceAspect(tracing);
    }
}
