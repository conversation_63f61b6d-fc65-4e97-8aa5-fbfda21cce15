package com.ps.ps.config;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ps.ps.shiro.NoLoginFilter;
import com.ps.ps.shiro.ShiroDbRealm;
import com.ps.ps.shiro.TokenManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.shiro.authc.pam.AtLeastOneSuccessfulStrategy;
import org.apache.shiro.authc.pam.ModularRealmAuthenticator;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.spring.boot.autoconfigure.ShiroAutoConfiguration;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.crazycake.shiro.RedisCacheManager;
import org.crazycake.shiro.RedisManager;
import org.crazycake.shiro.RedisSessionDAO;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.web.filter.DelegatingFilterProxy;

import java.util.List;
import java.util.Map;

@Configuration
@DependsOn("shiroDbRealm")
public class ShiroConfig extends ShiroAutoConfiguration {

    private Logger logger = LogManager.getLogger(ShiroConfig.class);
    @Autowired
    private ShiroDbRealm userRealm;

    private RedisManager redisManager;

    @Bean
    @DependsOn({"lifecycleBeanPostProcessor"})
    public static DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator creator = new DefaultAdvisorAutoProxyCreator();
        //这一句比较重要
        creator.setProxyTargetClass(true);
        return creator;


    }

    @Bean
    public ModularRealmAuthenticator modularRealmAuthenticator() {
        ModularRealmAuthenticator modularRealmAuthenticator = new ModularRealmAuthenticator();
        modularRealmAuthenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());
        return modularRealmAuthenticator;
    }

    /**
     * Shiro 安全管理器
     */
    @Override
    @Bean(name = "securityManager")
    @DependsOn("realms")
    public DefaultWebSecurityManager securityManager(List<Realm> realms) {
        logger.info("securityManager  enter");
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setAuthenticator(modularRealmAuthenticator());

        manager.setSessionManager(getTokenManager());
//        // 设置自定义的 SubjectFactory
//        manager.setSubjectFactory(subjectFactory());
//
//        // 设置自定义的 SessionManager
//        manager.setSessionManager(sessionManager());
//
//        // 禁用 Session
//        ((DefaultSessionStorageEvaluator)((DefaultSubjectDAO)manager.getSubjectDAO()).getSessionStorageEvaluator())
//                .setSessionStorageEnabled(false);

        // 设置自定义的 Realm
        manager.setRealms(realms);

        return manager;
    }

    /**
     * Shiro 安全管理器
     */
    @Bean
    @DependsOn("redisManager")
    public TokenManager getTokenManager() {
        TokenManager tokenManager = new TokenManager();
        tokenManager.setCacheManager(getRedisCacheManager(redisManager));
        tokenManager.setGlobalSessionTimeout(86400000L);
        tokenManager.setSessionDAO(getRedisSessionDao(redisManager));
        return tokenManager;
    }

    //    @Bean
    public NoLoginFilter accessTokenFilter() {
        return new NoLoginFilter();
    }

    @Bean
    public FilterRegistrationBean delegatingFilterProxy() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);
        proxy.setTargetBeanName("shiroFilter");
        filterRegistrationBean.setFilter(proxy);
        return filterRegistrationBean;
    }


    /**
     * 设置过滤规则
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);

        //自定义拦截器 参考 ShiroLoginFilter.java
        Map<String, String> filterChainDefinitionMap = Maps.newLinkedHashMap();
        //以下是过滤链，按顺序过滤，所以/**需要放最后
        //开放的静态资源
        filterChainDefinitionMap.put("/telephone_country_code", "anon");
        filterChainDefinitionMap.put("/", "anon");

        filterChainDefinitionMap.put("/resources/**", "anon");
        filterChainDefinitionMap.put("/favicon.ico", "anon");
        filterChainDefinitionMap.put("/businessCooperaApply", "anon");
        filterChainDefinitionMap.put("/noauth/endproducts/**", "anon");
        filterChainDefinitionMap.put("/noauth/endproducts/*", "anon");
//        filterChainDefinitionMap.put("/ps/endproducts/packge", "anon");
        filterChainDefinitionMap.put("/site_stat/order/latest", "anon");
        filterChainDefinitionMap.put("/site_stat/product_summary", "anon");
        filterChainDefinitionMap.put("/ps/endproducts/export", "anon");
        filterChainDefinitionMap.put("/ps/excel/*/download", "anon");
        filterChainDefinitionMap.put("/mabang/notify", "anon");
        filterChainDefinitionMap.put("/fonts/merchants/option", "anon");
        filterChainDefinitionMap.put("/amazon_sync_notify/**", "anon");
        filterChainDefinitionMap.put("/merchants/user/mfa/loginCheck", "anon");
        filterChainDefinitionMap.put("/loginAction", "anon");
        filterChainDefinitionMap.put("/alipay/return", "anon");
        filterChainDefinitionMap.put("/alipay/success", "anon");
        filterChainDefinitionMap.put("/actuator/metrics/process.uptime", "anon");
        filterChainDefinitionMap.put("/aliexpress/access_token", "anon");
        filterChainDefinitionMap.put("/aliexpress/product_access_token", "anon");
        filterChainDefinitionMap.put("/merchant_stores/amazon_merchant/statistics", "anon");
        filterChainDefinitionMap.put("/merchant_stores/aliexpress_merchant/statistics", "anon");
        filterChainDefinitionMap.put("/merchant_stores/new_aliexpress_merchant/statistics", "anon");
        filterChainDefinitionMap.put("/merchants/offlinePayPayment/*/one", "anon");
        filterChainDefinitionMap.put("/merchant_stores/test", "anon");
        filterChainDefinitionMap.put("/messages_to_reset", "anon");
        filterChainDefinitionMap.put("/system/image/cut_down", "anon");
        filterChainDefinitionMap.put("/cut/filecode/content", "anon");
        filterChainDefinitionMap.put("/system/image/cut_upload", "anon");
        filterChainDefinitionMap.put("/system/image/fit_extend_down", "anon");
        filterChainDefinitionMap.put("/system/image/fit_extend_upload", "anon");
        filterChainDefinitionMap.put("/system/image/resize_extend_upload", "anon");
        filterChainDefinitionMap.put("/system/image/resize_extend_down", "anon");
        filterChainDefinitionMap.put("/system/image/showMask", "anon");
        filterChainDefinitionMap.put("/system/image/down_image", "anon");
        filterChainDefinitionMap.put("/system/image/mask", "anon");
        filterChainDefinitionMap.put("/orders/*/excel", "anon");
        filterChainDefinitionMap.put("/orders/early_paytime", "anon");
        filterChainDefinitionMap.put("/orders/early_factorytime", "anon");
        filterChainDefinitionMap.put("/orders/early_compensation_time", "anon");
        filterChainDefinitionMap.put("/merchant/*/task_child_layers/**", "anon");
        filterChainDefinitionMap.put("/system/image/mask_down", "anon");
        filterChainDefinitionMap.put("/system/image/mask_upload", "anon");
        filterChainDefinitionMap.put("/password", "anon");
        filterChainDefinitionMap.put("/ps/prototype/sync_map", "anon");
        filterChainDefinitionMap.put("/products/sync_map", "anon");
        filterChainDefinitionMap.put("/materials/sync_map", "anon");
        filterChainDefinitionMap.put("/merchantStore/temu/webhook/orderMall/expired", "anon");
        filterChainDefinitionMap.put("/merchantStore/temu/webhook/productMall/expired", "anon");
        filterChainDefinitionMap.put("/merchantStore/temu/webhook/orderUpdate", "anon");
        filterChainDefinitionMap.put("/merchant/shein/webhook/*", "anon");
        filterChainDefinitionMap.put("/merchant/shein/webhook", "anon");
        filterChainDefinitionMap.put("/merchantStore/walmart/webhook/authExpired", "anon");
        filterChainDefinitionMap.put("/merchantStore/walmart/webhook/orderUpdate", "anon");
        filterChainDefinitionMap.put("/materials/test", "anon");
        filterChainDefinitionMap.put("/ps/factories/sync_map", "anon");
        filterChainDefinitionMap.put("/ps/factories/test", "anon");
        filterChainDefinitionMap.put("/products/supply_sync_map", "anon");
        filterChainDefinitionMap.put("/products/test", "anon");
        filterChainDefinitionMap.put("/products/test2", "anon");
        filterChainDefinitionMap.put("/products/test3", "anon");
        filterChainDefinitionMap.put("/merchants/products/suppliedAndMerchantAuthorized", "anon");
        filterChainDefinitionMap.put("/material/theme/page", "anon");
        filterChainDefinitionMap.put("/themePrototype/officialPage", "anon");
        filterChainDefinitionMap.put("/ps/prototype/psd_sync_map", "anon");
        filterChainDefinitionMap.put("/ps/prototype/layer_sync_map", "anon");
        filterChainDefinitionMap.put("/ps/prototype/test", "anon");
        filterChainDefinitionMap.put("/ps/image/cut", "anon");
        filterChainDefinitionMap.put("/order_import_record_orders/order_previews", "anon");
        filterChainDefinitionMap.put("/ps/image/cut_reload", "anon");
        filterChainDefinitionMap.put("/ps/task/redirect", "anon");
        filterChainDefinitionMap.put("/ps/task/preview", "anon");
        filterChainDefinitionMap.put("/office-materials/download/*", "anon");
        filterChainDefinitionMap.put("/ps/image/*/download", "anon");
        filterChainDefinitionMap.put("/verification_code", "anon");
        filterChainDefinitionMap.put("/verifyCode", "anon");
        filterChainDefinitionMap.put("/geetest/startCaptchaServlet", "anon");
        filterChainDefinitionMap.put("/initCaptcha", "anon");
        filterChainDefinitionMap.put("/bind", "anon");
        filterChainDefinitionMap.put("/products", "anon");
        filterChainDefinitionMap.put("/products/*", "anon");
        filterChainDefinitionMap.put("/categories/search", "anon");
        filterChainDefinitionMap.put("/materials/sync2es", "anon");
        filterChainDefinitionMap.put("/categories", "anon");
        filterChainDefinitionMap.put("/category/tree/**", "anon");
        filterChainDefinitionMap.put("/category/treeWomenDress/**", "anon");
        filterChainDefinitionMap.put("/gateway/**", "anon");
        filterChainDefinitionMap.put("/design/export/**", "anon");
        filterChainDefinitionMap.put("/register", "anon");
        filterChainDefinitionMap.put("/registerMerchantInTenant/createMerchants", "anon");
        filterChainDefinitionMap.put("/registerMerchantInTenant/pod/auth/merchantVerifyCode", "anon");
        filterChainDefinitionMap.put("/designerRegister", "anon");
        filterChainDefinitionMap.put("/login", "anon");
        filterChainDefinitionMap.put("/template_tokens/login", "anon");
        filterChainDefinitionMap.put("/configs", "anon");
        filterChainDefinitionMap.put("/payments/*/finish", "anon");
        filterChainDefinitionMap.put("/ziguang_images/*/material", "anon");
        filterChainDefinitionMap.put("/ps/taskChild/callBack*", "anon");
        filterChainDefinitionMap.put("/ps/orders/sync", "anon");
        filterChainDefinitionMap.put("/ps/orders/sync_test", "anon");
        filterChainDefinitionMap.put("/ps/ptag/sync", "anon");
        filterChainDefinitionMap.put("/swagger-ui.html", "anon");
        filterChainDefinitionMap.put("/swagger*/**", "anon");
        filterChainDefinitionMap.put("/v2/**", "anon");
        filterChainDefinitionMap.put("/webjars/**", "anon");
        filterChainDefinitionMap.put("/crmUsers/contact", "anon");
        filterChainDefinitionMap.put("/ps/tag/findTagAll", "anon");
        filterChainDefinitionMap.put("/ps/prototype/sync", "anon");
        filterChainDefinitionMap.put("/ps/merchants/sync", "anon");
        filterChainDefinitionMap.put("/ps/merchants/sync_recharge", "anon");
        filterChainDefinitionMap.put("/ps/merchants/sync_add_limit", "anon");
        filterChainDefinitionMap.put("/ps/prototype/delete_sync", "anon");
        filterChainDefinitionMap.put("/ps/material/sync_batch", "anon");
        filterChainDefinitionMap.put("/ps/endproducts/site_statistics", "anon");
        filterChainDefinitionMap.put("/swagger-resources/**", "anon");
        filterChainDefinitionMap.put("/logout", "logout");
        filterChainDefinitionMap.put("/ps/merchants/status", "anon");
        filterChainDefinitionMap.put("/ps/endproducts/get_by_sku", "anon");
        filterChainDefinitionMap.put("/configs/rotation_chart", "anon");
        filterChainDefinitionMap.put("/configs/**", "anon");
        filterChainDefinitionMap.put("/designerMerchant/landingDesignInfo", "anon");
        filterChainDefinitionMap.put("/products/*/cycle", "anon");
        filterChainDefinitionMap.put("/nationalities", "anon");
        filterChainDefinitionMap.put("/merchants/*/countryExpressInfo/productId/**", "anon");
        filterChainDefinitionMap.put("/announcements/**", "anon");
        filterChainDefinitionMap.put("/products/*/recommend", "anon");
        filterChainDefinitionMap.put("/sys_dict", "anon");
        filterChainDefinitionMap.put("/ps/test/testfeign", "anon");
        filterChainDefinitionMap.put("/platform_goods", "anon");
        filterChainDefinitionMap.put("/merchants/platformGoods", "anon");
        filterChainDefinitionMap.put("/getAmazonIamge", "anon");
        filterChainDefinitionMap.put("/ali_image", "anon");
        filterChainDefinitionMap.put("/ps/test/syncAllImportOrder", "anon");
        filterChainDefinitionMap.put("/ps/image/demo/upload", "anon");
        filterChainDefinitionMap.put("/ping/**", "anon");
        filterChainDefinitionMap.put("/lakala/notify/paid", "anon");
        filterChainDefinitionMap.put("/shopify/webhook/*", "anon");
        filterChainDefinitionMap.put("/shopify/auth/oauth2", "anon");
        filterChainDefinitionMap.put("/shopify/auth/test", "anon");
        filterChainDefinitionMap.put("/onlineOrder/alibaba/*", "anon");
        filterChainDefinitionMap.put("/onlineOrder/**", "anon");
        filterChainDefinitionMap.put("/etsy/oauth/v3/callback", "anon");
        filterChainDefinitionMap.put("/merchants/etsy/v3/oauth/callback", "anon");
        filterChainDefinitionMap.put("/shoplazza/auth/*", "anon");
        filterChainDefinitionMap.put("/dhgate/oauth2/**", "anon");
        filterChainDefinitionMap.put("/amazon/auth/callback", "anon");
        filterChainDefinitionMap.put("/amazon/oauth/oauthLogin", "anon");
        filterChainDefinitionMap.put("/amazon/auth/beginAuth", "anon");
        filterChainDefinitionMap.put("/shoplazza/webhook/*", "anon");
        filterChainDefinitionMap.put("/customer/shopify", "anon");
        filterChainDefinitionMap.put("/customer/shopify/aaa", "anon");
        filterChainDefinitionMap.put("/customer/shopify/connect_sds", "anon");
        filterChainDefinitionMap.put("/platform/login", "anon");
        filterChainDefinitionMap.put("/order_import_record_orders/sync_mysql_to_es", "anon");
        filterChainDefinitionMap.put("/sitecountries/rate", "anon");
        filterChainDefinitionMap.put("/**/logisticProducts/*", "anon");
        filterChainDefinitionMap.put("/**/merchants/initTemplate/**", "anon");
        filterChainDefinitionMap.put("/women/clothing/topic/**", "anon");
        filterChainDefinitionMap.put("/agentMerchants/banner", "anon");
        filterChainDefinitionMap.put("/agentMerchants/website/config", "anon");
        filterChainDefinitionMap.put("/agentMerchants/agentProductParentTypes", "anon");
        filterChainDefinitionMap.put("/agentMerchants/recommendProduct", "anon");
        filterChainDefinitionMap.put("/material/homePage/**", "anon");
        filterChainDefinitionMap.put("/themeOfficialGroup/homePage/officialPage", "anon");
        filterChainDefinitionMap.put("/merchants/products/profit", "anon");
        filterChainDefinitionMap.put("/products/homePage/specialZones", "anon");
        filterChainDefinitionMap.put("/merchant/texture/list", "anon");
        filterChainDefinitionMap.put("/merchant/productAttribute/group", "anon");
        filterChainDefinitionMap.put("/products/homePage/randomByCategoryIds", "anon");
        filterChainDefinitionMap.put("/agentMerchants", "anon");
        filterChainDefinitionMap.put("/products/listOfMerchantProduct", "anon");
        filterChainDefinitionMap.put("/gatewaySign/*", "anon");
        filterChainDefinitionMap.put("/outLogin", "anon");
        filterChainDefinitionMap.put("/merchants/officialDesignProducts/recommend", "anon");
        filterChainDefinitionMap.put("/rocketmq/**", "anon");
        filterChainDefinitionMap.put("/platformProducts/**", "anon");
        filterChainDefinitionMap.put("/merchants/ecommerceAdmin/**", "anon");
        filterChainDefinitionMap.put("/oldData/**", "anon");
        filterChainDefinitionMap.put("/contents/recommendation/**", "anon");
        filterChainDefinitionMap.put("/merchants/*/specialZones", "anon");
        filterChainDefinitionMap.put("/merchants/platformGoods/setMeal/list", "anon");
        filterChainDefinitionMap.put("/merchants/platformGoods/setMeal/*", "anon");
        filterChainDefinitionMap.put("/merchants/cartonConfiguration", "anon");
        filterChainDefinitionMap.put("/products/specialZones/*", "anon");
        filterChainDefinitionMap.put("/activityCalendar/list", "anon");
        filterChainDefinitionMap.put("/activityCalendar/countryCodes", "anon");
        filterChainDefinitionMap.put("/merchantStorePlatformCodes/all", "anon");
        filterChainDefinitionMap.put("/products/page", "anon");
        filterChainDefinitionMap.put("/merchant/product/price/upMemberLevel", "anon");
        filterChainDefinitionMap.put("/blog/**", "anon");
        filterChainDefinitionMap.put("/lakalaLedger/callback/**", "anon");
        filterChainDefinitionMap.put("/lakalaFile/**", "anon");
        filterChainDefinitionMap.put("/admin/order/es/**", "anon");
        filterChainDefinitionMap.put("/fonts/files", "anon");
        filterChainDefinitionMap.put("/fonts/noLogin/**", "anon");
        filterChainDefinitionMap.put("/imgToAli/**", "anon");
        filterChainDefinitionMap.put("/aliexpressCatogories/init/**", "anon");
        filterChainDefinitionMap.put("/merchantOldData/**", "anon");
        filterChainDefinitionMap.put("/oldDatas/**", "anon");
        filterChainDefinitionMap.put("/weChats/**", "anon");
        filterChainDefinitionMap.put("/aliFaceVerify/callback", "anon");
        filterChainDefinitionMap.put("/merchantAuth/**", "anon");
        filterChainDefinitionMap.put("/ps/endproducts/demoDesign/**", "anon");
        filterChainDefinitionMap.put("/merchant/partners/list", "anon");
        filterChainDefinitionMap.put("/merchant_amazon_templates/oldAmazonTemplateContentInit", "anon");
        filterChainDefinitionMap.put("/merchantOldData/syncRepresentativeToNewTable", "anon");
        filterChainDefinitionMap.put("/merchantStores/findMerchantStoreId", "anon");

        filterChainDefinitionMap.put("/**", "nologin");

        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        Map<String, javax.servlet.Filter> filters = Maps.newHashMap();
        filters.put("nologin", accessTokenFilter());
        shiroFilterFactoryBean.setFilters(filters);
        return shiroFilterFactoryBean;
    }

    /**
     * 配置自定义的 Realm
     *
     * @return
     */
    @Bean("realms")
    public List<Realm> getRealms() {
        List<Realm> realms = Lists.newArrayList();

//        // 配置自定义 UserRealm
//        // 由于UserRealm里使用了自动注入，所以这里需要注入Realm而不是new新建
//        userRealm.setAuthenticationTokenClass(UserAuthenticationToken.class);
//        userRealm.setCredentialsMatcher(new PasswordCredentialsMatcher());//使用自定义的密码匹配器
        if (userRealm == null) {
            logger.info("user realm null");
        } else {
            logger.info("user realm object");

        }
        realms.add(userRealm);
        return realms;
    }

    @Bean
    public Realm getRealm() {
        return userRealm;
    }

    public ShiroDbRealm getUserRealm() {
        return userRealm;
    }

    public void setUserRealm(ShiroDbRealm userRealm) {
        this.userRealm = userRealm;
    }

    @Bean
    public RedisSessionDAO getRedisSessionDao(RedisManager redisManager) {
        RedisSessionDAO redisSessionDAO = new RedisSessionDAO();
        redisSessionDAO.setRedisManager(redisManager);
        return redisSessionDAO;
    }

    @Bean
    public RedisCacheManager getRedisCacheManager(RedisManager redisManager) {
        RedisCacheManager redisCacheManager = new RedisCacheManager();
        redisCacheManager.setRedisManager(redisManager);
        redisCacheManager.setPrincipalIdFieldName("id");
        return redisCacheManager;
    }

    @Bean("redisManager")
    public RedisManager getRedisManager(@Value("${spring.redis.host}") String redisHost
            , @Value("${spring.redis.port}") String redisPort
            , @Value("${spring.redis.password:#{null}}") String redisPassword) {
        logger.info("init redis manager" + redisHost + ":" + redisPort);
        RedisManager redisManager = new RedisManager();
        redisManager.setHost(redisHost + ":" + redisPort);
        if (StrUtil.isNotBlank(redisPassword)){
            redisManager.setPassword(redisPassword);
        }
        this.redisManager = redisManager;
        return redisManager;
    }

}
