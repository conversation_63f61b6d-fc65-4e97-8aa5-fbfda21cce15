package com.ps.ps.config;

import com.ps.ps.shiro.ShiroUser;
import com.ps.support.ISecurityUtils;
import com.ps.util.AggFeignHelper;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
public class FeignClientInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        ShiroUser currUser = null;
        try {
            currUser = ISecurityUtils.getSameCurrUser();
        } catch (Throwable ignored) {
        }
        if (currUser == null) {
            AggFeignHelper.headerHandler(requestTemplate, SdsPlatformEnum.MERCHANT.code);
        } else {
            AggFeignHelper.headerHandler(requestTemplate, SdsPlatformEnum.MERCHANT.code
                    , currUser.getId(), currUser.getMerchantId(), currUser.getTenantId(), null, currUser.getUsername());
        }
    }
}
