package com.ps.ps.shiro;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SecurityManagerType {
    @Value("${shiro.security_manager}")
    private String securityManager;
    public static String LDAP_SECURITY_MANAGER = "ldapSecurityManager";
    public static String SECURITY_MANAGER = "securityManager";

    public String getSecurityManager() {
        return securityManager;
    }

    public void setSecurityManager(String securityManager) {
        this.securityManager = securityManager;
    }

    public Boolean isLdap() {
        return securityManager.equals(LDAP_SECURITY_MANAGER);
    }
    public Boolean isNotLdap() {
        return !isLdap();
    }
}
