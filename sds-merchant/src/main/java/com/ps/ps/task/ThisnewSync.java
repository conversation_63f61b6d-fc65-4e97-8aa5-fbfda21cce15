package com.ps.ps.task;

import com.ps.ps.service.MaterialService;
import com.ps.ps.service.ProductService;
import com.ps.ps.service.PrototypeService;
import com.ps.ps.service.SiteCountryService;
import com.ziguang.base.annotion.ScheduleAspectAnnotion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * 2019/8/28 0028
 */
@Component
public class ThisnewSync {

    private static org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(ThisnewSync.class);


    @Autowired
    private PrototypeService prototypeService;
    @Autowired
    private ProductService productService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private SiteCountryService siteCountryService;

    private static final Long SYNC_TIME = 86400000L;

    @Scheduled(cron = "0 0 */4 * * ? ", zone = "GMT+0800")
    @ScheduleAspectAnnotion(lock = "PrototypeSync")
    public void prototypeSync() {
        logger.info("PrototypeSync begin ");
        prototypeService.syncFromOrigin(getDate());
        logger.info("PrototypeSync end");
    }

    public Date getDate() {
        Long time = System.currentTimeMillis();
        time = time - SYNC_TIME;
        return new Date(time);
    }

    @Scheduled(cron = "0 30 */4 * * ? ", zone = "GMT+0800")
    @ScheduleAspectAnnotion(lock = "ProductSync")
    public void productSync() {
        logger.info("ProductSync begin ");
        productService.syncFromOrigin(getDate());
        logger.info("ProductSync end");
    }

    @Scheduled(cron = "0 45 */4 * * ? ", zone = "GMT+0800")
    @ScheduleAspectAnnotion(lock = "materialSync")
    public void materialSync() {
        logger.info("materialSync begin ");
        materialService.syncFromOrigin(getDate());
        logger.info("materialSync end");
    }

}
