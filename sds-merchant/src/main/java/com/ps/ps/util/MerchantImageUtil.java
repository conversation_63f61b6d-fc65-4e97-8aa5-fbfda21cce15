package com.ps.ps.util;

import com.sdsdiy.common.base.exception.BusinessException;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2023/6/15
 */
@Log4j2
public class MerchantImageUtil {

    public static void zipImgUrl(Collection<String> urlList, String zipName, HttpServletResponse response) {

        try {
            zipName = URLEncoder.encode(zipName, "UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + zipName);

            ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
            for (String imageUrl : urlList) {
                URL url = new URL(imageUrl);
                zos.putNextEntry(new ZipEntry(getFileName(imageUrl)));
                InputStream fis = url.openConnection().getInputStream();
                byte[] buffer = new byte[1024];
                int r;
                while ((r = fis.read(buffer)) != -1) {
                    zos.write(buffer, 0, r);
                }
                fis.close();
            }
            zos.flush();
            zos.close();
        } catch (IOException e) {
            log.error("图片打包失败:", e);
            throw new BusinessException("图片打包失败:" + e.getMessage());
        }
    }

    public static String getFileName(String url) {
        if (!StringUtils.hasText(url)) {
            return url;
        }
        int index = url.lastIndexOf("/");
        if (index >= 0) {
            url = url.substring(index + 1);
        }
        index = url.lastIndexOf("?");
        if (index >= 0) {
            url = url.substring(0, index);
        }
        return url;
    }
}
