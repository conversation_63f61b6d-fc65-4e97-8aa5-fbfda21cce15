package com.ps.ps.util;

import com.ps.support.ISecurityUtils;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.user.UserTagConst;
import com.sdsdiy.statapi.dto.mq.UserOptDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/***
 * mq生产者
 * @Description
 * <AUTHOR>
 * @Date 2020/6/28、18:33
 */
@Component
@Slf4j
public class MqEventProducer {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void sendUserOptEvent(UserOptType userOptType,int num) {
        UserOptDto userOptDto = new UserOptDto();
        userOptDto.setMerchantId(ISecurityUtils.getMerchantId());
        userOptDto.setMerchantUserId(ISecurityUtils.getCurrUserId());
        userOptDto.setUserOptType(userOptType.value());
        userOptDto.setMsgId(generateMsgId());
        userOptDto.setMaterialNum(num);
        userOptDto.setTimsStamp(System.currentTimeMillis());
        //mqTemplate.sendMessage(EventConstant.EVENT_USER_OPT, userOptDto);
        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_USER, UserTagConst.EVENT_USER_OPT, userOptDto);
        log.info("发送用户操作事件:userOptDto {} {}", userOptDto,userOptType.toString());
    }

    public void sendUserOptEvent(Long merchantId,Long userId,UserOptType userOptType,int num) {
        UserOptDto userOptDto = new UserOptDto();
        userOptDto.setMerchantId(merchantId);
        userOptDto.setMerchantUserId(userId);
        userOptDto.setUserOptType(userOptType.value());
        userOptDto.setMsgId(generateMsgId());
        userOptDto.setMaterialNum(num);
        userOptDto.setTimsStamp(System.currentTimeMillis());

        //mqTemplate.sendMessage(EventConstant.EVENT_USER_OPT, userOptDto);
        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_USER, UserTagConst.EVENT_USER_OPT, userOptDto);
        log.info("发送用户操作事件:userOptDto {} {}", userOptDto,userOptType.toString());
    }

    public void sendUserOptEvent(UserOptType userOptType) {
         sendUserOptEvent(userOptType,1);
    }

    /**
     * 消息Id生成
     * @return
     */
    public String generateMsgId(){
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        return uuid;
    }


}
