package com.ps.ps.util;

/**
 * <AUTHOR>
 */

public enum UserOptType {

    /**
     *
     */
    OPT_ADD_MATERIAL(1),
    /**
     *
     */
    OPT_EXTRACT_KEYWORD(2),
    OPT_OUTCUT_IMG(3),
    OPT_STYLIZED(4),
    OPT_ENLARGE(5);


    private int value = 0;

    UserOptType(int value) {
        this.value = value;
    }

    public static UserOptType valueOf(int value) {
        switch (value) {
            case 1:
                return OPT_ADD_MATERIAL;
            case 2:
                return OPT_EXTRACT_KEYWORD;
            case 3:
                return OPT_OUTCUT_IMG;
            case 4:
                return OPT_STYLIZED;
            case 5:
                return OPT_ENLARGE;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }


    @Override
    public String toString(){
        switch (value) {
            case 1:
                return "添加素材";
            case 2:
                return "提取关键字";
            case 3:
                return "抠图";
            case 4:
                return "风格化(滤镜)";
            case 5:
                return "放大";
            default:
                return "???";
        }
    }


}
