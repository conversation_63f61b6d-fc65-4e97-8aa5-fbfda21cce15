package com.ps.ps.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.util.List;

/**
 * @Author: zmy
 * @Date: 2020/6/4 21:13
 * @Description: 违禁词检测，不区分大小写
 */
public class Trie {

    /**
     * trie树的根节点
     */
    private Node root;
    /**
     * trie树中拥有多少分枝（多少个敏感词）
     */
    private int size;

    public Trie() {
        this.root = new Node();
        this.size = 0;
    }

    public Trie(List<String> wordList) {
        this.root = new Node();
        this.size = 0;
        if (CollectionUtils.isNotEmpty(wordList)) {
            // 插入敏感词
            wordList.stream().forEach(word -> this.addBranchesInTrie(" "+word+" "));
        }
    }

    /**
     * @Description: 返回trie树中分枝树（敏感词树）
     */
    public int getSize() {
        return size;
    }

    /**
     * @param word 添加的敏感词
     * @Description: 向trie树中添加分枝/敏感词
     */
    public void addBranchesInTrie(String word) {
        // 设置当前节点为根节点
        Node cur = root;
        char[] words = word.toCharArray();

        for (char c : words) {
            // 判断当前节点的子节点中是否存在字符c
            if (!cur.children.containsKey(Character.toLowerCase(c))) {
                // 如果不存在则将其添加进行子节点中
                cur.children.put(Character.toLowerCase(c), new Node());
            }
            // 当前节点进行变换，变换为新插入到节点 c
            cur = cur.children.get(Character.toLowerCase(c));
        }
        // 分枝添加完成后，将分枝中的最后一个节点设置为叶子节点
        if (!cur.isWord) {
            cur.isWord = true;
            // 分枝数（敏感词数）加1
            size++;
        }
    }

    /**
     * @param word
     * @return 大于0：包含，等于0，不包含
     * @Description: 判断trie树中是否存在某分枝/敏感词，oldCount为了处理：暴9力，这种违禁词被截断的情况
     */
    public int contains(String word) {
        if(StringUtils.isBlank(word)){
            return 0;
        }
        StringBuffer sb=new StringBuffer();
        sb.append(" ").append(word).append(" ");
        //检测出的违禁词个数
        int count = 0;
        //检测到的单词个数，比如：暴9力，就是3个
        int oldCount = 0;
        Node cur = root;
        char[] words = sb.toString().toCharArray();
        for (char c : words) {
            if (!cur.children.containsKey(Character.toLowerCase(c))) {
                if (oldCount > 0) {
                    cur = root;
                    //处理：暴9力，这种违禁词被截断的情况
                    oldCount = 0;
                    if(cur.children.containsKey(Character.toLowerCase(c))){
                        oldCount++;
                        cur = cur.children.get(Character.toLowerCase(c));
                    }
                }
                continue;
            }
            if (!cur.isWord) {
                oldCount++;
                cur = cur.children.get(Character.toLowerCase(c));
            }
            if (cur.isWord) {
                count++;
                // 查找一个敏感词并替换后，需要重新从根节点进行遍历，所以当前节点指向root
                cur = root;
                oldCount = 0;
            }
        }
        return count;
    }

}
