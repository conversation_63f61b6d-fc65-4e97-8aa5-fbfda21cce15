package com.ps.ps.util;

import java.util.HashMap;

/**
 * @Author: zmy
 * @Date: 2020/6/4 21:12
 * @Description: 节点
 */
public class Node {

    /**节点是否为叶子节点的标志；true：叶子节点，false：非叶子节点（用于子节点的节点）*/
    public boolean isWord;
    /**当前节点拥有的孩子节点，使用hashmap进行存储，在查找子节点时的时间复杂度为O(1)*/
    public HashMap<Character, Node> children;

    public Node(boolean isWord) {
        this.isWord = isWord;
        this.children = new HashMap<>();
    }

    public Node() {
        this(false);
    }
}
