package com.ps.ps.controller.Gateway;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.ps.ps.feign.MerchantSysUserFeign;
import com.ps.support.utils.OutPlatformUtil;
import com.ps.param.GatewayMerchantSysUserRespDto;
import com.sdsdiy.userapi.constant.MerchantSysUserConstant;
import com.sdsdiy.userapi.dto.base.MerchantSysUserRespDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RequestMapping(value = "/gateway/users")
@Controller
public class GatewayUserController {
    @Resource
    MerchantSysUserFeign merchantSysUserFeign;

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET)
    public List<GatewayMerchantSysUserRespDto> list() throws Exception {
        List<MerchantSysUserRespDto> sysUserRespDtoList = merchantSysUserFeign.getByMerchantId(OutPlatformUtil.getMerchantId(), "merchantId");
        List<GatewayMerchantSysUserRespDto> gatewayMerchantSysUserRespDtos = Lists.newArrayList();
        for (MerchantSysUserRespDto merchantSysUserRespDto : sysUserRespDtoList) {
            if(MerchantSysUserConstant.USER_TYPE_ROOT.equalsIgnoreCase(merchantSysUserRespDto.getUserType())){
                continue;
            }
            gatewayMerchantSysUserRespDtos.add(BeanUtil.toBean(merchantSysUserRespDto, GatewayMerchantSysUserRespDto.class));

        }
        return gatewayMerchantSysUserRespDtos;
    }

}
