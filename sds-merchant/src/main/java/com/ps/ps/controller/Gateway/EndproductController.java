package com.ps.ps.controller.Gateway;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.exception.BusinessException;
import com.ps.ps.service.*;
import com.ps.support.ISecurityUtils;
import com.ps.support.ListResponse;
import com.ps.support.MapListUtil;
import com.ps.support.utils.HttpUtil;
import com.ps.support.utils.OutPlatformUtil;
import com.ps.system.service.UserService;
import com.ziguang.base.dto.DesignProductApiRequest;
import com.ziguang.base.dto.DesignProductDTO;
import com.ziguang.base.dto.DesignProductLayerDTO;
import com.ziguang.base.dto.gateway.GatewayEndProductHuajuProductDto;
import com.ziguang.base.dto.gateway.GatewayEndproductDetailDto;
import com.ziguang.base.dto.gateway.GatewayEndproductDto;
import com.ziguang.base.dto.gateway.GatewayLayerImage;
import com.ziguang.base.model.Material;
import com.ziguang.base.model.TaskChildFinished;
import com.ziguang.base.model.User;
import com.ziguang.base.support.StringUtils;
import com.ziguang.base.vo.DesignProductVo;
import com.ziguang.base.vo.ProductIdsEndproductVo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

@RequestMapping(value = "/gateway/endproducts")
@RestController("GatewayEndproductController")
public class EndproductController {
    @Autowired
    private EndProductService endProductService;
    @Autowired
    private GatewayEndProductService gatewayEndProductService;
    @Autowired
    private FactoryOrderService factoryOrderService;
    @Autowired
    private ProductService productService;
    @Autowired
    private TaskChildLayerService taskChildLayerService;
    @Autowired
    private S3ServiceV2 s3ServiceV2;


    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET)
    public ListResponse<GatewayEndproductDto> source(@RequestParam(value = "page", defaultValue = "1") int page,
                               @RequestParam(value = "size", defaultValue = "10") int size,
                               @RequestParam(value = "search", defaultValue = "") String search,
                               @RequestParam(value = "categoryId",required = false)Long categoryId,
                               @RequestParam(value = "designProductType", required = false) String designProductType,
                               @RequestParam(value = "productName", required = false) String productName,
                               @RequestParam(value = "userId",required = false)Long userId,
                               @RequestParam(value = "taskId",required = false)Long taskId,
                               @RequestParam(value = "gmt_created_begin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date createdDateBegin,
                               @RequestParam(value = "gmt_created_end", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date createdDateEnd,
                               @RequestParam(value = "sort_col", defaultValue = "id") String sortCol,
                               @RequestParam(value = "sort_type", defaultValue = "desc") String sortType,
                               HttpServletRequest httpServletRequest
    ) {
        String type = TaskChildFinished.TYPE_NONE + "," + TaskChildFinished.TYPE_PARENT + "," + TaskChildFinished.TYPE_SIMPLE;
        DesignProductApiRequest designProductApiRequest = new DesignProductApiRequest();
        designProductApiRequest.setPage(page);
        designProductApiRequest.setSize(size);
        designProductApiRequest.setCategoryId(categoryId);
        designProductApiRequest.setSearch(search);
        designProductApiRequest.setTaskIds(taskId == null ? null : taskId.toString());
        designProductApiRequest.setSortCol(sortCol);
        designProductApiRequest.setSortType(sortType);
        designProductApiRequest.setCreatedDateBegin(createdDateBegin);
        designProductApiRequest.setCreatedDateEnd(createdDateEnd);
        designProductApiRequest.setSearchUserIds(userId == null ? null : userId.toString());
        designProductApiRequest.setType(type);
        designProductApiRequest.setMerchantId(OutPlatformUtil.getMerchantId());
        designProductApiRequest.setDesignProductType(designProductType);
        if (com.ps.support.utils.StringUtils.isNotBlank(productName)) {
            List<Long> parentIds = productService.matchByProductParentName(productName);
            if (CollectionUtils.isEmpty(parentIds)) {
                return new ListResponse(0, Lists.newArrayList());
            }
            String productIds = com.ps.support.utils.StringUtils.listConvertToString(parentIds);
            designProductApiRequest.setProductIds(productIds);
        }
        ListResponse  listResponse= designProductService.endProductOutFormat(designProductApiRequest);
        List<GatewayEndproductDto> list = gatewayEndProductService.gatewayConvertToEndproductDto(listResponse.getItems());
        gatewayEndProductService.gatewayCategory(list);
        endProductService.buildPrice(OutPlatformUtil.getMerchantId(),list);
        listResponse.setItems(list);

        return listResponse;
    }


    @Autowired
    private CommonDesignProductService designProductService;
    @ResponseBody
    @RequestMapping(value = "{id}", method = RequestMethod.GET)
    public GatewayEndproductDto getById(@PathVariable("id") String id) {
        TaskChildFinished taskChildFinished ;
        if(StringUtils.isNumber(id)) {
            taskChildFinished = designProductService.formatDetail(Long.valueOf(id));
        } else {
            taskChildFinished = designProductService.findByKeyId(OutPlatformUtil.getMerchantId(), id, true);
        }
        if (taskChildFinished == null) {
            return null;
        }
        GatewayEndproductDto res = gatewayEndProductService.gatewayConvertToEndproductDto(taskChildFinished);
        if(taskChildFinished.getProduct() != null) {
            res.setCategories(CategoryService.categoriesToDto(taskChildFinished.getProduct().getCategories()));
        }
        List<String> compoundIds = Lists.newArrayList(taskChildFinished.getCompoundId());
        List<GatewayLayerImage> layerImages = Lists.newArrayList();
        //获取图层信息
        List<DesignProductLayerDTO> layers = designProductService.getDesignProductLayerDTOS(OutPlatformUtil.getMerchantId(), compoundIds);
        List<Material> materials = factoryOrderService.getMaterials(layers);
        for (Material material : materials) {
            GatewayLayerImage layerImage = new GatewayLayerImage();
            layerImage.setName(material.getName());
            layerImage.setImageUrl(material.getImgUrl());
            layerImage.setMaterialId(material.getId());
            layerImages.add(layerImage);
        }
        res.setLayerImages(layerImages);
        endProductService.buildPrice(OutPlatformUtil.getMerchantId(), Lists.newArrayList(res));
        return res;

    }
    @ResponseBody
    @RequestMapping(value = "{id}/huajuDetail", method = RequestMethod.GET)
    public GatewayEndproductDetailDto detail(@PathVariable("id") Long id) {
        GatewayEndproductDetailDto res = endProductService.gatewayConvertToEndproductDtoDetail(designProductService.getDesignProductWithVariant(OutPlatformUtil.getMerchantId(),id,true));
        if(res != null) {
            GatewayEndProductHuajuProductDto productDto = endProductService.getHuajuProduct(res ,OutPlatformUtil.getMerchantId(),res.getProductParentId());
            res.setGoods(productDto);
            res.setProductName(productDto.getGoodsTitle());
        }
        return res;

    }



    @ResponseBody
    @RequestMapping(value = "get_by_end_product", method = RequestMethod.GET)
    public Collection<ProductIdsEndproductVo> getByEndProduct(@RequestParam(value = "end_product_id") String endproductId, @RequestParam(value = "product_ids") String productIds) throws Exception {
        User user = UserService.outDefaultUser(OutPlatformUtil.getMerchantId());
        return endProductService.getOrSave(OutPlatformUtil.getMerchantId(), user.getId(), OutPlatformUtil.getTenantId(), user, endproductId, StringUtils.stringToLongList(productIds));
    }
    @ResponseBody
    @RequestMapping(value = "/get_by_key", method = RequestMethod.GET)
    public TaskChildFinished getByKey(@RequestParam("key") String key, HttpServletRequest httpServletRequest) {
        String host = HttpUtil.getHost(httpServletRequest);
        TaskChildFinished endProduct = endProductService.findByKey(OutPlatformUtil.getMerchantId(), key);
        return endProduct;
    }
    @ResponseBody
    @RequestMapping(value = "variant_detail", method = RequestMethod.GET)
    public DesignProductVo variantDetail(@RequestParam("id") Long id, HttpServletRequest httpServletRequest) {
        DesignProductVo designProductVo = designProductService.findDetail(id, OutPlatformUtil.getMerchantId());
        designProductService.setSourceFlag(OutPlatformUtil.getMerchantId(), Lists.newArrayList(designProductVo));
        List<DesignProductVo> designProductVos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(designProductVo.getMaterialVariant())) {
            for (DesignProductVo vo : designProductVo.getMaterialVariant()) {
                if (CollectionUtils.isNotEmpty(vo.getVariant())) {
                    designProductVos.addAll(vo.getVariant());
                } else {
                    designProductVos.add(vo);
                }
            }
            designProductVo.setVariant(designProductVos);
        }
        return designProductVo;

    }
    @ResponseBody
    @RequestMapping(value = "layerImageDetail", method = RequestMethod.GET)
    public List<String> layerImageDetail(@RequestParam("keyId") String keyId) {
        DesignProductDTO designProductDTO = endProductService.getByKeyId(OutPlatformUtil.getMerchantId(), keyId);
        List<String> contents = Lists.newArrayList();
        for (DesignProductLayerDTO designProductLayerDTO : designProductDTO.getDesignProductLayerDTOS()) {
            String fileCode = taskChildLayerService.resetSrcFileCode(OutPlatformUtil.getMerchantId(), designProductLayerDTO.getId());
            String content = taskChildLayerService.manuscriptFileCode(fileCode);
            contents.add(s3ServiceV2.getDownloadUrlByFilePath(taskChildLayerService.fillFilePath(content)));
        }
        return contents;

    }
    @ResponseBody
    @RequestMapping(value = "variant_material_detail", method = RequestMethod.GET)
    public GatewayEndproductDto variantMaterialDetail(@RequestParam("id") Long id, HttpServletRequest httpServletRequest) {
        DesignProductVo designProductVo = designProductService.findDetail(id, OutPlatformUtil.getMerchantId());
        if (designProductVo == null) {
            return null;
        }
        GatewayEndproductDto res = endProductService.gatewayConvertToEndproductDto(designProductVo);
        res.setCategories(CategoryService.categoriesToDto(designProductVo.getCategories()));
        Set<String> compoundIds = Sets.newHashSet();
        List<GatewayEndproductDto> variants = Lists.newArrayList();
        List<DesignProductLayerDTO> layers = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(designProductVo.getMaterialVariant())) {
            for (DesignProductVo vo : designProductVo.getMaterialVariant()) {
                if (CollectionUtils.isNotEmpty(vo.getVariant())) {
                    for (DesignProductVo productVo : vo.getVariant()) {
                        compoundIds.add(productVo.getCompoundId());
                        variants.add(endProductService.gatewayConvertToEndproductDto(productVo));
                        layers.addAll(productVo.getDesignProductLayerDTOS());
                    }
                } else {
                    variants.add(endProductService.gatewayConvertToEndproductDto(vo));
                    compoundIds.add(vo.getCompoundId());
                    layers.addAll(vo.getDesignProductLayerDTOS());
                }
            }
            res.setVariant(variants);
        }


        //获取图层信息
//        List<DesignProductLayerDTO> layers = designProductService.getDesignProductLayerDTOS(OutPlatformUtil.getMerchantId(), Lists.newArrayList(compoundIds));
        MapListUtil<String,Material> materialMapListUtil = factoryOrderService.getCompoundMaterials(layers);
        List<GatewayEndproductDto> endproductDtos = Lists.newArrayList(variants);
        endproductDtos.add(res);

        for (GatewayEndproductDto endproductDto : endproductDtos) {
            List<GatewayLayerImage> layerImages = Lists.newArrayList();
            for (Material material : materialMapListUtil.getList(endproductDto.getCompoundId())) {
                GatewayLayerImage layerImage = new GatewayLayerImage();
                layerImage.setName(material.getName());
                layerImage.setImageUrl(material.getImgUrl());
                layerImage.setMaterialId(material.getId());
                layerImages.add(layerImage);
            }
            endproductDto.setLayerImages(layerImages);
        }

        endProductService.buildPrice(OutPlatformUtil.getMerchantId(), endproductDtos);
        if(designProductVo.getProduct() != null){
            res.setCategoryId(designProductVo.getProduct().getCategoryId());
        }

        return res;

    }


}
