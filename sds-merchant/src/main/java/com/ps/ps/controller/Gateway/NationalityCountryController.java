package com.ps.ps.controller.Gateway;

import com.ps.ps.service.NationalityService;
import com.ps.support.ListResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
@Controller
@RequestMapping(value = "/gateway/nationalities")
public class NationalityCountryController {
    @Autowired
    private NationalityService nationalityService;
    @RequestMapping(value = "",method = RequestMethod.GET)
    @ResponseBody
    public ListResponse list(){
        ListResponse listResponse = new ListResponse();
        listResponse.setItems(nationalityService.select());
        return listResponse;

    }
}
