package com.ps.ps.controller.inner;

import com.ziguang.base.vo.WebSocketUserVo;
import com.ps.ps.shiro.ShiroUser;
import com.ps.support.ISecurityUtils;
import com.ziguang.base.model.User;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * <AUTHOR>
 * 2019/7/26 0026
 */
@RequestMapping(value="/verify_token")
@RestController
public class WebSocketTokenAuthController {

    /**
     * 校验token并获取用户
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object tokenAuth(){
        ShiroUser user = ISecurityUtils.getCurrUser();
        return new WebSocketUserVo(user.getId(), Arrays.asList(String.valueOf(user.getMerchantId())));
    }

}
