package com.ps.ps.controller.Gateway;

import com.beust.jcommander.internal.Lists;
import com.ps.ps.feign.PsdContentFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/21
 */
@Controller("PsdContentController2")
@RequestMapping("/gateway/cut/filecode/content")
@Api("psd相关")
public class GatewayPsdContentController {
    @Resource
    private PsdContentFeign psdContentFeign;

    @RequestMapping(value = "",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("获得content")
    public Map<String,Object> psdContent(@RequestParam String ids){
        String[] idStr = ids.split(",");
        List<String> idList = Lists.newArrayList();
        for (String s : idStr) {
            s = s + ".psd";
            idList.add(s);
        }
        return psdContentFeign.getPsdContentByCode(idList);
    }
}
