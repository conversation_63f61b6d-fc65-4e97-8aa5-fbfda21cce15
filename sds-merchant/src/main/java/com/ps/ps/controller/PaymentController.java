/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.ps.ps.controller;

import com.ps.ps.alipay.AliPayService;
import com.ps.ps.service.PaymentService;
import com.ps.ps.service.payment.AlipaymentSuccessService;
import com.ps.ps.service.payment.MerchantRechargeService;
import com.sdsdiy.orderapi.constant.EnumPaymentMethod;
import com.sdsdiy.orderapi.constant.EnumPaymentStatus;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.ziguang.base.model.Payment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Controller
@RequestMapping("/payments")
public class PaymentController {
    @Autowired
    private PaymentService paymentService;

    @Autowired
    AliPayService aliPayService;
    @Autowired
    MerchantRechargeService merchantRechargeService;
    @Resource
    AlipaymentSuccessService alipaymentSuccessService;

    @RequestMapping(value = "/{id}",method = RequestMethod.GET)
    @ResponseBody
    public Payment get(@PathVariable("id") Long id){
        return paymentService.uppainCheck(id);
    }


    @RequestMapping(value = "/check",method = RequestMethod.GET)
    @ResponseBody
    public PaymentDto check(@RequestParam("id") Long id,@RequestParam("eventType")String eventType){
        return alipaymentSuccessService.check(id,eventType);
    }



    @RequestMapping(value = "/recharge/payment/{id}",method = RequestMethod.GET)
    @ResponseBody
    public PaymentDto payed(@PathVariable("id") Long id){
        return merchantRechargeService.payed(id);
    }
    @RequestMapping(value = "/{id}/refund",method = RequestMethod.GET)
    @ResponseBody
    public void refund(@PathVariable("id") Long id){
        Payment payment = paymentService.getById(id);
        if(payment == null){
            return ;
        }
        if(payment.getMethod().equalsIgnoreCase(EnumPaymentMethod.ALIPAY.getValue()) && payment.getStatus().equals(EnumPaymentStatus.WAIT.getValue())){
            aliPayService.refund(payment.getNo(),"支付失败退款",payment.getTotalAmount(),"支付失败退款");
            paymentService.updateStatus(payment.getId(), EnumPaymentStatus.REFUND.getValue());
        }
    }
    @RequestMapping(value = "/{id}/finish",method = RequestMethod.POST)
    @ResponseBody
    public Payment finish(@PathVariable("id") Long id){
        return paymentService.finish(id);
    }
}

