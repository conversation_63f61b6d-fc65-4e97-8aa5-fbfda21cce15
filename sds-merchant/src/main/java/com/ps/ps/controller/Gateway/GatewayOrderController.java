 /*
  * Powered By [rapid-framework]
  * Web Site: http://www.rapid-framework.org.cn
  * Google Code: http://code.google.com/p/rapid-framework/
  * Since 2008 - 2019
  */


 package com.ps.ps.controller.Gateway;

 import cn.hutool.core.bean.BeanUtil;
 import cn.hutool.core.util.StrUtil;
 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSONObject;
 import com.google.common.collect.Lists;
 import com.google.common.collect.Maps;
 import com.google.common.collect.Sets;
 import com.ps.aggregation.manager.MerchantAreaAddressManager;
 import com.ps.base.entity.SearchBean;
 import com.ps.dto.*;
 import com.ps.exception.BusinessException;
 import com.ps.ps.feign.AdminPrepaidCarriageNoFeign;
 import com.ps.ps.feign.AdminPrepaidFeign;
 import com.ps.ps.feign.OrderItemNoFeign;
 import com.ps.ps.feign.address.AddressFeign;
 import com.ps.ps.feign.logistics.LogisticsExpensesFeign;
 import com.ps.ps.feign.order.OrderCarriageFeign;
 import com.ps.ps.feign.order.OrderItemImportProducFeign;
 import com.ps.ps.feign.order.PrepaidBoxItemFeign;
 import com.ps.ps.feign.order.PrepaidShippedFeign;
 import com.ps.ps.service.*;
 import com.ps.ps.service.cache.ProductCacheService;
 import com.ps.ps.service.linstener.OrderEventService;
 import com.ps.support.Assert;
 import com.ps.support.ISecurityUtils;
 import com.ps.support.redis.LockBatchUtil;
 import com.ps.support.utils.ConvertUtil;
 import com.ps.support.utils.OutPlatformUtil;
 import com.ps.system.service.MerchantUserService;
 import com.ps.system.service.OfficeService;
 import com.ps.tool.cbt.common.util.StringUtil;
 import com.sdsdiy.common.base.constant.BasePoConstant;
 import com.sdsdiy.common.base.entity.dto.*;
 import com.sdsdiy.common.base.helper.IdsSearchHelper;
 import com.sdsdiy.common.base.helper.NumberUtils;
 import com.sdsdiy.core.mq.core.RocketMQTemplate;
 import com.sdsdiy.logisticsapi.dto.LogisticsFreightProductReqDto;
 import com.sdsdiy.logisticsapi.dto.LogisticsFreightReqDto;
 import com.sdsdiy.logisticsapi.dto.LogisticsFreightRespDto;
 import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
 import com.sdsdiy.logisticsdata.dto.track.OrderCarriageTrackInfosDTO;
 import com.sdsdiy.orderapi.constant.OrderShelveConstant;
 import com.sdsdiy.orderapi.dto.AdminPrepaidOrderNoLogisticsRespDto;
 import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
 import com.sdsdiy.orderapi.dto.PrepaidOrderNoLogisticsRespDto;
 import com.sdsdiy.orderapi.dto.order.OrderCarriageRespDto;
 import com.sdsdiy.orderapi.dto.ordercarriage.CustomUpdateOrderCarriageUploadLabelParam;
 import com.sdsdiy.orderapi.dto.ordercarriage.UpdateOrderCarriageUploadLabelParam;
 import com.sdsdiy.orderdata.constant.order.OrderLockConstant;
 import com.sdsdiy.orderdata.constant.order.SupplyChainTypeEnum;
 import com.sdsdiy.orderdata.dto.AddressReqDto;
 import com.sdsdiy.orderdata.dto.ImportProductDto;
 import com.sdsdiy.orderdata.dto.order.no.OrderItemNoSaveReqDTO;
 import com.sdsdiy.orderdata.dto.order.no.OrderItemNoSaveRespDTO;
 import com.ziguang.base.dto.*;
 import com.ziguang.base.dto.gateway.*;
 import com.ziguang.base.model.*;
 import com.ziguang.base.model.OrderItem;
 import com.ziguang.base.model.geteway.AddOrderDto;
 import com.ziguang.base.support.*;
 import com.ziguang.base.support.contant.CommonStatus;
 import com.ziguang.base.support.contant.OrderStatus;
 import com.ziguang.base.support.contant.PaymentMethod;
 import com.ziguang.base.vo.AfterServiceSubmitVo;
 import io.seata.spring.annotation.GlobalTransactional;
 import io.swagger.annotations.ApiOperation;
 import lombok.RequiredArgsConstructor;
 import org.apache.commons.collections4.CollectionUtils;
 import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
 import org.springframework.beans.BeanUtils;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.format.annotation.DateTimeFormat;
 import org.springframework.stereotype.Controller;
 import org.springframework.web.bind.annotation.*;

 import javax.annotation.Resource;
 import java.util.Date;
 import java.util.List;
 import java.util.Map;
 import java.util.Set;
 import java.util.concurrent.TimeUnit;
 import java.util.function.Function;
 import java.util.stream.Collectors;

 import static com.sdsdiy.common.base.constant.BasePoConstant.YES;

 @Controller
 @RequestMapping(value = "/gateway/orders")
 @RestController("GatewayOrderController")
 @RequiredArgsConstructor
 public class GatewayOrderController {
     private final MerchantAreaAddressManager merchantAreaAddressManager;
     private Logger logger = LogManager.getLogger(GatewayOrderController.class);
     @Autowired
     OrderItemImportProducFeign orderItemImportProducFeign;

     @Resource
     CustomLogisticOrderService customLogisticOrderService;
     @Resource
     ImageUploadService imageUploadService;

     @Resource
     OrderUpdateLogisticsService orderUpdateLogisticsService;
     @Autowired
     AfterServiceManage afterServiceManage;
     @Autowired
     private OrderService orderService;
     @Autowired
     private OrderPaymentService orderPaymentService;
     @Autowired
     private PrepaidBoxItemFeign prepaidBoxItemFeign;
     @Autowired
     private OrderCarriageFeign orderCarriageFeign;
     @Resource
     private LogisticsExpensesFeign logisticsExpensesFeign;

     @Resource
     private OfficeService officeService;
     @Resource
     private PaymentService paymentService;

     @Resource
     private LogisticsService logisticsService;
     @Autowired
     OrderImportRecordOrderService orderImportRecordOrderService;
     @Resource
     private MerchantUserService merchantUserService;


     @Resource
     private AddressFeign addressFeign;
     @Resource
     private AddressService addressService;
     @Resource
     private OrderEventService orderEventService;
     @Resource
     private AdminPrepaidCarriageNoFeign adminPrepaidCarriageNoFeign;
     @Resource
     private AdminPrepaidFeign adminPrepaidFeign;
     @Resource
     private OrderItemNoFeign orderItemNoFeign;
     @Resource
     private PrepaidShippedFeign prepaidShippedFeign;
     @Autowired
     private RocketMQTemplate rocketMQTemplate;

     @RequestMapping(value = "", method = RequestMethod.GET)
     @ResponseBody
     public SearchBean list(
             @RequestParam(value = "product_name", required = false) String productName
             , @RequestParam(value = "receiver", required = false) String receiver
             , @RequestParam(value = "seller_sku", required = false) String sellerSku
             , @RequestParam(value = "carriage_number", required = false) String carriageNumber
             , @RequestParam(value = "order_no", required = false) String orderNo
             , @RequestParam(value = "logistics_id", required = false) Long logisticsId
             , @RequestParam(value = "status", required = false) Integer status
             , @RequestParam(value = "production_type", required = false) Integer productionType
             , @RequestParam(value = "gmt_created_begin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date createdDateBegin
             , @RequestParam(value = "gmt_created_end", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date createdDateEnd
             , @RequestParam(value = "out_customer_id", required = false) String outCustomerId
             , @RequestParam(value = "merchant_store_platform_code", required = false) String merchantStorePlatformCode
             , @RequestParam(value = "merchant_store_code", required = false) Integer merchantStoreCode
             , @RequestParam(value = "site", required = false) String site
             , @RequestParam(value = "merchant_store_status ", required = false) Integer merchantStoreStatus
             , @RequestParam(value = "page", defaultValue = "1") Integer page
             , @RequestParam(value = "size", defaultValue = "10") Integer size
             , @RequestParam(value = "is_advance", required = false) Integer isAdvance
     ) {

         return this.orderService.formatList(null, null, createdDateBegin, createdDateEnd, productionType,
                 OutPlatformUtil.getMerchantId(), receiver, sellerSku, productName, carriageNumber,
                 orderNo, logisticsId, status, null,
                 null, outCustomerId, page, size, merchantStorePlatformCode, merchantStoreCode, site, merchantStoreStatus,
                 null, null, null, null, null, null, null);
     }


     @Resource
     LockService lockService;
     //15FEN
     private static final long LOCK_TIME = 60000L;

     //等待10ms
     private static final long LOCK_WAIT_TIME = 10L;

     @RequestMapping(value = "", method = RequestMethod.POST)
     @ResponseBody
     public Object save(@RequestBody OrderGatewayDto addOrder) {
         String key = StringUtils.getMd5(JsonUtil.toJson(addOrder));
         if (!this.lockService.tryLock(key, LOCK_WAIT_TIME, LOCK_TIME)) {
             Assert.wrong("相同的参数提交太频繁,请隔一分钟后重试");
         }
         this.merchantAreaAddressManager.formatUsProvinceCode(addOrder.getAddress());
         this.orderService.buildOrderAddress(addOrder);
         return this.orderService.outplatformSave(OutPlatformUtil.getMerchantId(), addOrder);
     }

     @RequestMapping(value = "/add/notAssociated", method = RequestMethod.POST)
     @ResponseBody
     public Object saveNotAssociatedOrder(@RequestBody OrderGatewayDto addOrder) {
         String key = StringUtils.getMd5(JsonUtil.toJson(addOrder));
         if (!this.lockService.tryLock(key, LOCK_WAIT_TIME, LOCK_TIME)) {
             Assert.wrong("相同的参数提交太频繁,请隔一分钟后重试");
         }
         this.merchantAreaAddressManager.formatUsProvinceCode(addOrder.getAddress());
         this.orderService.buildOrderAddress(addOrder);
         return this.orderService.saveNotAssociatedOrder(OutPlatformUtil.getMerchantId(), addOrder);
     }

     @RequestMapping(value = "/add/saveAssociationNotDesigned", method = RequestMethod.POST)
     @ResponseBody
     public Object saveAssociationNotDesignedOrder(@RequestBody OrderGatewayDto addOrder) {
         String key = StringUtils.getMd5(JsonUtil.toJson(addOrder));
         if (!this.lockService.tryLock(key, LOCK_WAIT_TIME, LOCK_TIME)) {
             Assert.wrong("相同的参数提交太频繁,请隔一分钟后重试");
         }
         this.merchantAreaAddressManager.formatUsProvinceCode(addOrder.getAddress());
         this.orderService.buildOrderAddress(addOrder);
         return this.orderService.saveAssociationNotDesignedOrder(OutPlatformUtil.getMerchantId(), addOrder);
     }


     @RequestMapping(value = "/add", method = RequestMethod.POST)
     @ResponseBody
     public Object add(@RequestBody OrderGatewayDto addOrder) {
         String key = StringUtils.getMd5(JsonUtil.toJson(addOrder));
         if (!this.lockService.tryLock(key, LOCK_WAIT_TIME, LOCK_TIME)) {
             Assert.wrong("相同的参数提交太频繁,请隔一分钟后重试");
         }
         if (addOrder.getBoolPaymanet() == null) {
             addOrder.setBoolPaymanet(1);
         }
         this.merchantAreaAddressManager.formatUsProvinceCode(addOrder.getAddress());
         this.orderService.buildOrderAddress(addOrder);
         return this.orderService.outplatformEndProducSave(OutPlatformUtil.getMerchantId(), addOrder);
     }

     /**
      * 因为有用户提出需求需要创建未付款关联已设计订单，又不想影响原有流程所以增加这个刚发
      */
     @RequestMapping(value = "/add/noPay", method = RequestMethod.POST)
     @ResponseBody
     public Object addNoPay(@RequestBody OrderGatewayDto addOrder) {
         String key = StringUtils.getMd5(JsonUtil.toJson(addOrder));
         if (!this.lockService.tryLock(key, LOCK_WAIT_TIME, LOCK_TIME)) {
             Assert.wrong("相同的参数提交太频繁,请隔一分钟后重试");
         }
         addOrder.setBoolPaymanet(0);
         this.merchantAreaAddressManager.formatUsProvinceCode(addOrder.getAddress());
         this.orderService.buildOrderAddress(addOrder);
         return this.orderService.outplatformEndProducSave(OutPlatformUtil.getMerchantId(), addOrder);
     }

     //v6.2.3获取订单相关信息
     @RequestMapping(value = "/{no}", method = RequestMethod.GET)
     @ResponseBody
     public Object get(@PathVariable("no") String no) {
         Order order = this.orderService.findByNo(no);
         GatewayOrder orderResp = new GatewayOrder();
         if (order != null && order.getMerchantId().equals(OutPlatformUtil.getMerchantId())) {
             orderResp = this.formatGatewayOrder(this.getOrderResp(order));

         } else {
             Long id = Long.valueOf(no);
             order = this.orderService.findById(id);
             orderResp = this.formatGatewayOrder(this.getOrderResp(order));
         }


         return orderResp;
     }

     private Order getOrderResp(Order order) {
         Long id = null;
         this.logger.info("a" + OutPlatformUtil.getMerchantId());
         if (order != null && order.getMerchantId().equals(OutPlatformUtil.getMerchantId())) {
             id = order.getId();
         } else {
             Assert.wrong("订单不存在");
         }
         return this.getOrderResp(id);
     }

     private Order getOrderResp(Long id) {
         Order orderResp = this.orderService.format(OutPlatformUtil.getMerchantId(), id);
         OrderCarriageRespDto orderCarriage = this.orderCarriageFeign.findOneByParcel(id, null, "carriageNo");

         if (orderCarriage != null) {
             orderResp.setCarriageNo(orderCarriage.getCarriageNo());
             orderResp.setCarriageStatus(orderCarriage.getCarriageStatus());
         }
         return orderResp;
     }

     //
     @RequestMapping(value = "detail", method = RequestMethod.GET)
     @ResponseBody
     public OrderOutResponseDto getDetail(@RequestParam(value = "no", required = false) String no, @RequestParam(value = "id", required = false) Long id, @RequestParam(value = "outOrderNo", required = false) String outOrderNo) {
         Order order = null;
         if (id != null && 0 < id) {
             order = this.orderService.findById(id, OutPlatformUtil.getMerchantId());
         }
         if (order == null && StringUtils.isNotBlank(no)) {
             order = this.orderService.findByNo(no, OutPlatformUtil.getMerchantId());
         }

         if (order == null && StringUtils.isNotBlank(outOrderNo)) {
             List<Order> orders = this.orderService.findByOutOrderNo(outOrderNo);
             for (Order o : orders) {
                 if (o.getMerchantId().equals(OutPlatformUtil.getMerchantId()) && OrderStatus.statusRepeat(o.getStatus())) {
                     order = o;
                 }
             }
         }

         if (order == null) {
             return null;
         }
         List<Order> orders = Lists.newArrayList(order);
         this.orderService.formatLogistics(orders);
         OrderCarriageRespDto one = this.orderCarriageFeign.findOneByParcel(order.getId(), null, "id");
         OrderOutResponseDto responseDto = new OrderOutResponseDto();
         responseDto.convertByOrder(order);
         responseDto.setCarriageNo(one.getCarriageNo());
         responseDto.setCarriageStatus(one.getCarriageStatus());
         return responseDto;
     }

     @RequestMapping(value = "/get_by_out_no", method = RequestMethod.GET)
     @ResponseBody
     public Object getByOutNo(@RequestParam("no") String no) {
         List<Order> orders = this.orderService.findByOutOrderNo(no);
         if (CollectionUtils.isEmpty(orders)) {
             Assert.wrong("订单不存在");
         }
         Order order = orders.get(0);
         List<Long> orderItemIds = Lists.newArrayList();
         GatewayOrder orderResp = this.formatGatewayOrder(this.getOrderResp(order));
         for (GatewayOrderItem item : orderResp.getItems()) {
             orderItemIds.add(item.getId());
         }
         IdsSearchHelper idsSearchHelper = new IdsSearchHelper();
         idsSearchHelper.setIds(orderItemIds);
         Map<Long, ImportProductDto> importProductDtoMap = this.orderItemImportProducFeign.findMapByIds(idsSearchHelper);
         for (GatewayOrderItem item : orderResp.getItems()) {
             ImportProductDto importProductDto = importProductDtoMap.get(item.getId());
             if (importProductDto != null) {
                 item.setOutProductId(importProductDto.getImportSku());
             }
         }
         return orderResp;
     }

     private GatewayOrder formatGatewayOrder(Order order) {
         GatewayOrder gatewayOrder = new GatewayOrder();
         if (null == order) {
             return gatewayOrder;
         }
         gatewayOrder.setId(order.getId());
         if (order.getMerchant() != null) {
             GatewayMerchant gatewayMerchant = new GatewayMerchant();
             gatewayMerchant.setId(order.getMerchant().getId());
             gatewayMerchant.setName(order.getMerchant().getName());
             gatewayMerchant.setMerchantNo(order.getMerchant().getMerchantNo());
             gatewayOrder.setMerchant(gatewayMerchant);
         }
         if (order.getAddress() != null) {
             Address address = order.getAddress();
             GatewayAddress gatewayAddress = new GatewayAddress();
             BeanUtils.copyProperties(address, gatewayAddress);
             gatewayOrder.setAddress(gatewayAddress);
         }
         List<GatewayOrderItem> gatewayOrderItems = Lists.newArrayList();
         for (OrderItem orderItem : order.getItems()) {
             GatewayOrderItem gatewayOrderItem = new GatewayOrderItem();
             BeanUtils.copyProperties(orderItem, gatewayOrderItem);
             if (orderItem.getProduct() != null) {
                 GatewayProduct gatewayProduct = new GatewayProduct();
                 BeanUtils.copyProperties(orderItem.getProduct(), gatewayProduct);
                 gatewayOrderItem.setProduct(gatewayProduct);
             }
             gatewayOrderItems.add(gatewayOrderItem);
         }
         gatewayOrder.setItems(gatewayOrderItems);
         gatewayOrder.setSite(order.getSite());
         gatewayOrder.setFinishTime(order.getFinishTime());
         gatewayOrder.setWeight(order.getWeight());
         gatewayOrder.setNo(order.getNo());
         gatewayOrder.setOutOrderNo(order.getOutOrderNo());
         gatewayOrder.setStatus(order.getStatus());
         gatewayOrder.setOutCurrencyCode(order.getOutCurrencyCode());
         gatewayOrder.setDesignStatus(order.getDesignStatus());
         gatewayOrder.setRemark(order.getRemark());
         gatewayOrder.setCancelRemark(order.getCancelRemark());
         gatewayOrder.setCustomerId(order.getCustomerId());
         gatewayOrder.setMerchantId(order.getMerchantId());
         gatewayOrder.setProductAmount(order.getProductAmount());
         gatewayOrder.setServiceAmount(order.getServiceAmount());
         gatewayOrder.setCarriageAmount(order.getCarriageAmount());
         gatewayOrder.setCarriageAmountOldField(order.getCarriageAmount());
         gatewayOrder.setCarriageNo(order.getCarriageNo());
         gatewayOrder.setIssuingBayId(order.getIssuingBayId());
         gatewayOrder.setIssuingBayAreaId(order.getIssuingBayAreaId());
         gatewayOrder.setRefundAmount(order.getRefundAmount());
         gatewayOrder.setRefundProductAmount(order.getRefundProductAmount());
         gatewayOrder.setRefundCarriageAmount(order.getRefundCarriageAmount());
         gatewayOrder.setRefundServiceAmount(order.getRefundServiceAmount());
         gatewayOrder.setCarriageServiceCharge(order.getCarriageServiceCharge());
         gatewayOrder.setCarriageName(order.getCarriageName());
         gatewayOrder.setExchangeRate(order.getExchangeRate());
         gatewayOrder.setTotalAmount(order.getTotalAmount());
         gatewayOrder.setProductNum(order.getProductNum());
         gatewayOrder.setLogisticsId(order.getLogisticsId());
         gatewayOrder.setPaymentId(order.getPaymentId());
         gatewayOrder.setCreatedTime(order.getCreatedTime());
         gatewayOrder.setUpdatedTime(order.getUpdatedTime());
         gatewayOrder.setProductionType(order.getProductionType());
         gatewayOrder.setOrigin(order.getOrigin());
         gatewayOrder.setCarriagePayStaus(order.getCarriagePayStaus());
         gatewayOrder.setOriginType(order.getOriginType());
         gatewayOrder.setCountryExpressInfoId(order.getCountryExpressInfoId());
         gatewayOrder.setOriginId(order.getOriginId());
         gatewayOrder.setMerchantStorePlatformCode(order.getMerchantStorePlatformCode());
         gatewayOrder.setMerchantStoreId(order.getMerchantStoreId());
         gatewayOrder.setExpressType(order.getExpressType());
         gatewayOrder.setTrackStatus(order.getTrackStatus());
         gatewayOrder.setPrepaidNo(order.getPrepaidNo());
         gatewayOrder.setBeAfterServiceOrder(order.getBeAfterServiceOrder());
         gatewayOrder.setTransactionCode(order.getTransactionCode());

         return gatewayOrder;
     }

     @Autowired
     CommonDesignProductService designProductService;
     @Autowired
     CountryExpressInfoService expressInfoService;
     @Autowired
     ProductCacheService productCacheService;
     @Autowired
     EndProductService endProductService;
     @Resource
     private CountryExpressInfoService countryExpressInfoService;

     @RequestMapping(value = "/logisticsInfo", method = RequestMethod.POST)
     @ResponseBody
     public List<OrderOutLogisticsReferResponseDto> logisticsInfo(@RequestBody OrderOutLogisticsReferRequestDto orderOutLogisticsReferRequestDto) {
         LogisticsFreightReqDto req = orderOutLogisticsReferRequestDto.getAddress();

         req.setLogisticsType(LogisticsService.getTypeNormal());

         Assert.validateTrue(CollectionUtils.isEmpty(orderOutLogisticsReferRequestDto.getProducts()), "产品信息有误!");
         List<LogisticsFreightProductReqDto> productList = Lists.newArrayList();
         Long merchantId = OutPlatformUtil.getMerchantId();
         List<String> keyIds = orderOutLogisticsReferRequestDto.getProducts().stream().map(OrderOutLogisticsReferProductRequestDto::getKeyId).collect(Collectors.toList());
         List<DesignProductDTO> designProductDTOS = this.designProductService.findListByKeyIds(merchantId, keyIds);
         Map<String, DesignProductDTO> map = Maps.newHashMap();
         for (DesignProductDTO designProductDTO : designProductDTOS) {
             map.put(designProductDTO.getKeyId(), designProductDTO);
         }
         com.sdsdiy.productapi.dto.ProductIssuingBayDto productIssuingBayDto = null;
         for (OrderOutLogisticsReferProductRequestDto orderOutLogisticsReferProductRequestDto : orderOutLogisticsReferRequestDto.getProducts()) {
             String keyId = orderOutLogisticsReferProductRequestDto.getKeyId();
             DesignProductDTO designProductDTO = map.get(keyId);

             if (designProductDTO == null) {
                 Assert.wrong(keyId + "没有对应的成品信息");
             }
             Product product = this.productCacheService.findById(designProductDTO.getProductId());
             if (productIssuingBayDto == null) {
                 productIssuingBayDto = this.endProductService.getProductIssuingBayInfoNoBay(merchantId
                         , designProductDTO.getProductId(), SupplyChainTypeEnum.ONE_PIECE.name()
                         , ConvertUtil.dtoConvert(orderOutLogisticsReferRequestDto.getAddress(), BaseAddressDTO.class));
             }
             Assert.validateTrue(product == null, "不存在该产品!");
             LogisticsFreightProductReqDto productReqDto = new LogisticsFreightProductReqDto();
             productReqDto.setNum(orderOutLogisticsReferProductRequestDto.getNum());
             productReqDto.setId(product.getId());
             productReqDto.setWeight(product.getWeight());
             productReqDto.setBoxHeight(product.getBoxHeight());
             productReqDto.setBoxLength(product.getBoxLength());
             productReqDto.setBoxWidth(product.getBoxWidth());
             productList.add(productReqDto);
         }
         //发货仓
         req.setIssuingBayAreaId(productIssuingBayDto.getIssuingAreaId());
         req.setTenantId(OutPlatformUtil.getTenantId());
         req.setProductList(productList);
         this.countryExpressInfoService.formatGoodsTotalPrice(req);
         List<LogisticsFreightRespDto> refers = this.logisticsExpensesFeign.findRefer(req);
         List<OrderOutLogisticsReferResponseDto> res = Lists.newArrayList();
         for (LogisticsFreightRespDto refer : refers) {
             OrderOutLogisticsReferResponseDto data = new OrderOutLogisticsReferResponseDto();
             data.setLogisticsId(refer.getLogisticsId());
             data.setLogisticsName(refer.getLogistics().getName());
             data.setAmount(refer.getFreight());
             res.add(data);
         }
         return res;
     }


     @Autowired
     private FactoryOrderService factoryOrderService;
     //v6.0 不用改 获取生产信息说明
/*    @RequestMapping(value = "{no}/facotory_orders", method = RequestMethod.GET)
    @ResponseBody
    public Object getFactoryOrders(@PathVariable("no") String no) {
        Response response = new Response();

        Order order = orderService.findByNo(no);
        if (null == order){
            response.setResponseCode(ResponseCode.NO_ORDER);
            response.setData(Lists.newArrayList());
            return response;
        }

        Long id = order.getId();
*//*        Long id = null;
        logger.info(" merchantId {}",OutPlatformUtil.getMerchantId());
        if(order != null && order.getMerchantId().equals(OutPlatformUtil.getMerchantId())){
            id = order.getId();
        }else {
            id = Long.valueOf(no);
        }
        Order data = orderService.findById(id);
        List<FactoryOrder> factoryOrders = factoryOrderService.findByMerchantOrderNo(data.getNo());*//*
        List<FactoryOrder> factoryOrders = factoryOrderService.findByMerchantOrderNo(order.getNo());
        List<FactoryOrderDto> list = Lists.newArrayList();
        List<OrderItem> orderItems = orderItemService.findByOrderId(id);
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        Map<Long,OrderItemImportProduct> orderItemImportProductMap = orderImportRecordOrderService.findItemImportProductMap(orderItemIds);

        for (FactoryOrder factoryOrder : factoryOrders) {
            FactoryOrderDto factoryOrderDto = new FactoryOrderDto();
            factoryOrderDto.setNo(factoryOrder.getNo());
            factoryOrderDto.setImageUrls(factoryOrder.getImgUrls());
            for (OrderItem orderItem : orderItems) {
                if(orderItem.getId().equals(factoryOrder.getOrderItemId())){
                    factoryOrderDto.setOutProductId(orderItem.getOutProductId());
                    OrderItemImportProduct orderItemImportProduct = orderItemImportProductMap.get(orderItem.getId());
                    if(StringUtils.isBlank(orderItem.getOutOrderItemId()) && orderItemImportProduct != null){
                        factoryOrderDto.setOutProductId(orderItemImportProduct.getImportAsin());
                    }
                    break;
                }
            }
            list.add(factoryOrderDto);

        }
        if(CollectionUtils.isNotEmpty(list)){
            response.setData(list);
        }else {
            response.setResponseCode(ResponseCode.ERROR);
        }
        return response;

    }*/


     //v6.0 不用改  6.2.3 查询生产信息说明
     @RequestMapping(value = "{no}/facotory_orders", method = RequestMethod.GET)
     @ResponseBody
     public Object getFactoryOrders(@PathVariable("no") String no) {
         Order order = this.orderService.findByNo(no);
         Long id = null;
         if (order != null && order.getMerchantId().equals(OutPlatformUtil.getMerchantId())) {
             id = order.getId();
         } else {
             id = Long.valueOf(no);
         }
         Order data = this.orderService.findById(id);
         List<FactoryOrder> factoryOrders = this.factoryOrderService.findByMerchantOrderNo(data.getNo());
         List<FactoryOrderDto> list = Lists.newArrayList();
         List<OrderItem> orderItems = this.orderItemService.findByOrderId(id);
         List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
         Map<Long, OrderItemImportProduct> orderItemImportProductMap = this.orderImportRecordOrderService.findItemImportProductMap(orderItemIds);

         Response response = new Response();
         for (FactoryOrder factoryOrder : factoryOrders) {
             FactoryOrderDto factoryOrderDto = new FactoryOrderDto();
             factoryOrderDto.setNo(factoryOrder.getNo());
             factoryOrderDto.setImageUrls(factoryOrder.getImgUrls());
             for (OrderItem orderItem : orderItems) {
                 if (orderItem.getId().equals(factoryOrder.getOrderItemId())) {
                     factoryOrderDto.setOutProductId(orderItem.getOutProductId());
                     OrderItemImportProduct orderItemImportProduct = orderItemImportProductMap.get(orderItem.getId());
                     if (StringUtils.isBlank(orderItem.getOutOrderItemId()) && orderItemImportProduct != null) {
                         factoryOrderDto.setOutProductId(orderItemImportProduct.getImportAsin());
                     }
                     break;
                 }
             }
             list.add(factoryOrderDto);

         }
         if (CollectionUtils.isNotEmpty(list)) {
             response.setData(list);
         } else {
             response.setResponseCode(ResponseCode.ERROR);
         }
         return response;

     }

     //v6.1.4 修改地址物流
     @RequestMapping(value = "/{id}/putLogistics", method = RequestMethod.POST)
     @ResponseBody
     public ResDTO<String> batchUpdatLogisticsPayment(@PathVariable("id") Long id, @RequestBody GatewayPutLogisticsReqDto param) {
         ResDTO<String> resDTO = new ResDTO();
         //3.9.5 免密支付 zmy
         Order order = this.orderService.findById(id);
         if (null == order) {
             Assert.wrong("没有找到对应订单");
         }
         Assert.validateNotEmpty(this.prepaidBoxItemFeign.getByOrderNos(new BaseListDto<>(order.getNo()))
                 , "该订单已寄付装箱，不可修改物流，如需变更，请联系客服");
/*        if (StrUtil.isEmpty(param.getTransactionCode())){
            Assert.wrong("transactionCode 不可为空");
        }
        if ( !order.getTransactionCode().equalsIgnoreCase(param.getTransactionCode())) {
            Assert.wrong("订单信息发生变动，请重新进入");
        }*/

         merchantAreaAddressManager.formatUsProvinceCode(param.getAddress());
         if (order.getStatus() == com.sdsdiy.orderdata.enums.OrderStatus.NONE.getStatus()) {
             this.orderService.updateOrderAddress(id, param, order);
         } else {
             List<Order> orders = Lists.newArrayList();
             Order orderNew = new Order();
             orderNew.setId(order.getId());
             orderNew.setTransactionCode(order.getTransactionCode());
             orders.add(orderNew);
             BatchPutLogisticsReqDto batchPutLogisticsReqDto = new BatchPutLogisticsReqDto();
             batchPutLogisticsReqDto.setLogisticsId(param.getLogisticsId());
             batchPutLogisticsReqDto.setOrders(orders);
             batchPutLogisticsReqDto.setPaymentMethod(BALANCE_PAY);
             AddressReqDto address = null;
             if (param.getAddress() != null) {
                 address = new AddressReqDto();
                 BeanUtil.copyProperties(param.getAddress(), address);
             }
             batchPutLogisticsReqDto.setAddress(address);
             Boolean noPayPermission = false;
             //            User user = merchantUserService.findMerchantBoss(OutPlatformUtil.getMerchantId());
             Long userId = NumberUtils.greaterZero(OutPlatformUtil.getUserId()) ? OutPlatformUtil.getUserId() : 0L;
             this.customLogisticOrderService.addOrUpdate(id, param.getLogisticsId(), param.getOrderLogistics());
             this.orderUpdateLogisticsService.batchUpdatLogisticsPayment(OutPlatformUtil.getMerchantId(), userId, batchPutLogisticsReqDto, noPayPermission);
             //重新申请运单号
//             OrderApplyCarriageNoParam carriageNoParam = new OrderApplyCarriageNoParam();
//             carriageNoParam.setOrderId(order.getId());
//             carriageNoParam.setOperationMode(CarriageNoRecodeConstant.OPERATION_MODE_SELF);
//             carriageNoParam.setIsAsync(true);
//             carriageNoParam.setIsAnyNode(false);
////             this.orderEventService.sendMessage(carriageNoParam, OrderProgressConstant.GENERATE_WAYBILL_NUMBER);
//             rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.GENERATE_WAYBILL_NUMBER, carriageNoParam);

         }
         resDTO.setData(CommonStatus.ONLINE.getDesc());
         return resDTO;

     }


     //v6.2.2 修改地址
/*    @RequestMapping(value = "/{id}/putAddress", method = RequestMethod.POST)
    @ResponseBody
    public void putAddress(@PathVariable("id") Long id, @RequestBody AddressCommonDto addressCommonDto) {
        //3.9.5 免密支付 zmy
        Order order = orderService.findById(id);
        if (null == order){
            Assert.wrong("没有找到对应订单");
        }
        if (order.getStatus() == com.sdsdiy.orderapi.constant.OrderStatus.NONE.getStatus() || order.getStatus() == com.sdsdiy.orderapi.constant.OrderStatus.UNPAIN.getStatus()){
            //待编辑 待付款直接改
            addressCommonDto.setMerchantId(OutPlatformUtil.getMerchantId());
            Long addressId = addressFeign.getOrSaveAddress(addressCommonDto);
            order.setAddressId(addressId);
            orderService.upOrderAddress(id,addressId);
        }else{


        }


    }*/


     //v6.1.4 shanbin_sun 获取订单可用物流列表
     @RequestMapping(value = "/{no}/logistics/availablelist", method = RequestMethod.GET)
     @ResponseBody
     public GatewayLogisticsAvailablelist logisticsAvailablelist(@PathVariable("no") String no) {
         Order order = this.orderService.findByNo(no);
         GatewayLogisticsAvailablelist orderReferDtoNew = new GatewayLogisticsAvailablelist();
         if (null == order) {
             throw new BusinessException(ResponseCode.NO_ORDER);
         }

         OrderReferDto orderReferDto = this.orderService.refer(order.getId(), OutPlatformUtil.getMerchantId(), false);
         List<GatewayCountryExpressInfoDto> list = Lists.newArrayList();
         for (CountryExpressInfoNew countryExpressInfoNew : orderReferDto.getItems()) {
             GatewayCountryExpressInfoDto gatewayCountryExpressInfoDto = new GatewayCountryExpressInfoDto();
             gatewayCountryExpressInfoDto.setId(countryExpressInfoNew.getId());
             gatewayCountryExpressInfoDto.setName(countryExpressInfoNew.getName());
             gatewayCountryExpressInfoDto.setRemark(countryExpressInfoNew.getRemark());
             Logistics logistics = countryExpressInfoNew.getLogistics();
             if (null != logistics) {
                 GatewayLogisticsDto gatewayLogisticsDto = new GatewayLogisticsDto();
                 gatewayLogisticsDto.setId(logistics.getId());
                 gatewayLogisticsDto.setType(logistics.getType());
                 gatewayLogisticsDto.setName(logistics.getName());
                 gatewayLogisticsDto.setRemark(logistics.getRemark());
                 gatewayCountryExpressInfoDto.setLogistics(gatewayLogisticsDto);
             }
             list.add(gatewayCountryExpressInfoDto);
         }


         orderReferDtoNew.setItems(list);
         orderReferDtoNew.setItems(list);

         return orderReferDtoNew;
     }

     public static final String BALANCE_PAY = "BALANCE_PAY";

     //v6.1.4  shanbin_sun 订单支付
     @RequestMapping(value = "payment", method = RequestMethod.POST)
     @ResponseBody
     @GlobalTransactional
     public void payment(@RequestBody IdsParam param) {
         LockUtil lockUtil = this.paymentService.paymentLock(OutPlatformUtil.getMerchantId());
         User user = this.merchantUserService.findMerchantBoss(OutPlatformUtil.getMerchantId());
         Long merchantId = OutPlatformUtil.getMerchantId();
         orderPaymentService.payment(merchantId,user,param.getIds());

         lockUtil.unlock();
     }


     //v6.1.4 shanbin_sun 获取订单物流信息
     @RequestMapping(value = "/{no}/orderCarriage", method = RequestMethod.GET)
     @ResponseBody
     public GatewayOrderCarriageRespDto logistics(@PathVariable("no") String no) {
         Order order = this.orderService.findByNo(no);
         if (order == null) {
             order = this.orderService.oneByOutOrderNo(no);
         }
         if (null == order) {
             throw new BusinessException(ResponseCode.NO_ORDER);
         }
         OrderCarriageRespDto one = this.orderCarriageFeign.findOneByParcel(order.getId(), null, "id,transferCarriageNo");

         GatewayOrderCarriageRespDto gatewayOrderCarriageRespDto = new GatewayOrderCarriageRespDto();
         BeanUtil.copyProperties(one, gatewayOrderCarriageRespDto);
         Set<Long> logisticsIds = Sets.newHashSet();
         Long orderLogisticsId = 0L;
         if (NumberUtils.greaterZero(gatewayOrderCarriageRespDto.getLogisticsId())) {
             orderLogisticsId = gatewayOrderCarriageRespDto.getLogisticsId();
         } else if (NumberUtils.greaterZero(order.getLogisticsId())) {
             orderLogisticsId = order.getLogisticsId();
         }
         if (NumberUtils.greaterZero(orderLogisticsId)) {
             logisticsIds.add(orderLogisticsId);
         }


         gatewayOrderCarriageRespDto.setOrderStatus(order.getStatus());

         List<OrderCarriageRespDto> orderCarriageResp = this.orderService.orderCarriageList(Lists.newArrayList(order.getId()));

         List<GatewayOrderLogisticsListRespDto> logisticsList = Lists.newArrayList();

         for (OrderCarriageRespDto orderCarriageRespDto : orderCarriageResp) {
             GatewayOrderLogisticsListRespDto gatewayOrderLogisticsListRespDto = new GatewayOrderLogisticsListRespDto();
             gatewayOrderLogisticsListRespDto.setCarriageNo(orderCarriageRespDto.getCarriageNo());
             gatewayOrderLogisticsListRespDto.setLogisticsId(orderCarriageRespDto.getLogisticsId());
             gatewayOrderLogisticsListRespDto.setCarrierName(orderCarriageRespDto.getCarriageName());
             logisticsList.add(gatewayOrderLogisticsListRespDto);
             logisticsIds.add(orderCarriageRespDto.getLogisticsId());
         }

         List<Logistics> logistics = this.logisticsService.findByIds(logisticsIds);
         Map<Long, Logistics> logisticsMap = logistics.stream().collect(Collectors.toMap(Logistics::getId, Function.identity()));
         for (GatewayOrderLogisticsListRespDto gatewayOrderLogisticsListRespDto : logisticsList) {
             Logistics logi = logisticsMap.get(gatewayOrderLogisticsListRespDto.getLogisticsId());
             if (logi != null) {
                 gatewayOrderLogisticsListRespDto.setCarrierName(logi.getName());
             }
         }

         Logistics orderLogistics = logisticsMap.get(orderLogisticsId);
         if (orderLogistics != null) {
             gatewayOrderCarriageRespDto.setCarrierName(orderLogistics.getName());
             gatewayOrderCarriageRespDto.setLogisticsCodeId(orderLogistics.getCodeId());
             gatewayOrderCarriageRespDto.setStandardName(orderLogistics.getStandardName());
             gatewayOrderCarriageRespDto.setMethodName(orderLogistics.getMethodName());
             gatewayOrderCarriageRespDto.setIsOther(orderLogistics.getIsOther());
         }
         if (LogisticsCodeIdEnum.CONSIGNMENT.getCodeId().equalsIgnoreCase(gatewayOrderCarriageRespDto.getLogisticsCodeId())) {
             PrepaidOrderNoLogisticsRespDto logisticsInfoByOrderNo = this.prepaidShippedFeign.getLogisticsInfoByOrderNo(order.getNo());
             if (logisticsInfoByOrderNo != null) {
                 List<AdminPrepaidOrderNoLogisticsRespDto> batchInfos = logisticsInfoByOrderNo.getBatchInfos();
                 List<String> carriageNos = batchInfos.stream().flatMap(i -> i.getCarriageNos().stream()).distinct().collect(Collectors.toList());
                 List<String> carriageNames = batchInfos.stream().map(i -> i.getLogisticsServiceProvider()).distinct().collect(Collectors.toList());
                 gatewayOrderCarriageRespDto.setCarrierName(StringUtils.listToString(carriageNames));
                 gatewayOrderCarriageRespDto.setCarriageNo(StringUtils.listToString(carriageNos));
                 logisticsList = Lists.newArrayList();
                 for (AdminPrepaidOrderNoLogisticsRespDto batchInfo : batchInfos) {
                     for (String carriageNo : batchInfo.getCarriageNos()) {
                         GatewayOrderLogisticsListRespDto gatewayOrderLogisticsListRespDto = new GatewayOrderLogisticsListRespDto();
                         gatewayOrderLogisticsListRespDto.setCarriageNo(carriageNo);
                         gatewayOrderLogisticsListRespDto.setCarrierName(batchInfo.getLogisticsServiceProvider());
                         logisticsList.add(gatewayOrderLogisticsListRespDto);
                     }

                 }

             }
         }
         gatewayOrderCarriageRespDto.setLogisticsList(logisticsList);

         return gatewayOrderCarriageRespDto;
     }

     //v6.1.4 shanbin_sun 获取订单物流信息
     @RequestMapping(value = "/orderCarriages", method = RequestMethod.GET)
     @ResponseBody
     public List<GatewayOrderCarriageRespDto> getOrderCarriages(@RequestParam("nos") String nos) {
         List<Order> orders = this.getPermissionOrders(nos);
         List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
         List<OrderCarriageRespDto> orderCarriageRespDtos = this.orderService.orderCarriageList(orderIds);


         Set<Long> logisticsIds = Sets.newHashSet();
         MapListUtil<Long, OrderCarriageRespDto> orderCarriageRespDtoMapListUtil = MapListUtil.instance();

         for (OrderCarriageRespDto orderCarriageRespDto : orderCarriageRespDtos) {
             if (NumberUtils.greaterZero(orderCarriageRespDto.getLogisticsId())) {
                 logisticsIds.add(orderCarriageRespDto.getLogisticsId());
             }
             orderCarriageRespDtoMapListUtil.addValue(orderCarriageRespDto.getOrderId(), orderCarriageRespDto);

         }


         List<Logistics> logistics = this.logisticsService.findByIds(logisticsIds);
         Map<Long, Logistics> logisticsMap = logistics.stream().collect(Collectors.toMap(Logistics::getId, Function.identity()));
         List<GatewayOrderCarriageRespDto> respDtos = Lists.newArrayList();


         for (Order order : orders) {
             List<GatewayOrderLogisticsListRespDto> logisticsList = Lists.newArrayList();

             GatewayOrderCarriageRespDto gatewayOrderCarriageRespDto = new GatewayOrderCarriageRespDto();
             gatewayOrderCarriageRespDto.setOrderStatus(order.getStatus());
             gatewayOrderCarriageRespDto.setOrderId(order.getId());
             gatewayOrderCarriageRespDto.setOrderNo(order.getNo());
             gatewayOrderCarriageRespDto.setLogisticsCodeId(order.getLogisticsCodeId());
             for (OrderCarriageRespDto orderCarriageRespDto : orderCarriageRespDtos) {
                 if (!orderCarriageRespDto.getOrderId().equals(order.getId())) {
                     continue;
                 }
                 GatewayOrderLogisticsListRespDto gatewayOrderLogisticsListRespDto = new GatewayOrderLogisticsListRespDto();
                 BeanUtil.copyProperties(orderCarriageRespDto, gatewayOrderLogisticsListRespDto);

                 Logistics orderLogistics = logisticsMap.get(orderCarriageRespDto.getLogisticsId());
                 if (orderLogistics != null) {
                     gatewayOrderLogisticsListRespDto.setCarrierName(orderLogistics.getName());
                     gatewayOrderLogisticsListRespDto.setLogisticsCodeId(orderLogistics.getCodeId());
                     gatewayOrderLogisticsListRespDto.setStandardName(orderLogistics.getStandardName());
                     gatewayOrderLogisticsListRespDto.setMethodName(orderLogistics.getMethodName());
                     gatewayOrderLogisticsListRespDto.setIsOther(orderLogistics.getIsOther());
                 }
                 logisticsList.add(gatewayOrderLogisticsListRespDto);
             }

             if (LogisticsCodeIdEnum.CONSIGNMENT.getCodeId().equalsIgnoreCase(gatewayOrderCarriageRespDto.getLogisticsCodeId())) {
                 PrepaidOrderNoLogisticsRespDto logisticsInfoByOrderNo = this.prepaidShippedFeign.getLogisticsInfoByOrderNo(order.getNo());
                 if (logisticsInfoByOrderNo != null) {
                     List<AdminPrepaidOrderNoLogisticsRespDto> batchInfos = logisticsInfoByOrderNo.getBatchInfos();
                     logisticsList = Lists.newArrayList();
                     for (AdminPrepaidOrderNoLogisticsRespDto batchInfo : batchInfos) {
                         for (String carriageNo : batchInfo.getCarriageNos()) {
                             GatewayOrderLogisticsListRespDto gatewayOrderLogisticsListRespDto = new GatewayOrderLogisticsListRespDto();
                             gatewayOrderLogisticsListRespDto.setCarriageNo(carriageNo);
                             gatewayOrderLogisticsListRespDto.setCarrierName(batchInfo.getLogisticsServiceProvider());
                             logisticsList.add(gatewayOrderLogisticsListRespDto);
                         }

                     }

                 }
             }
             gatewayOrderCarriageRespDto.setLogisticsList(logisticsList);
             respDtos.add(gatewayOrderCarriageRespDto);
         }


         return respDtos;
     }


     private final static String SHELVE_TYPE_AUTO = "AUTO";
     private final static String SHELVE_TYPE_HAND = "HAND";

     //v6.1.4 shanbin_sun 订单搁置
     @RequestMapping(value = "/{id}/shelve", method = RequestMethod.PUT)
     @ResponseBody
     public void shelve(@PathVariable("id") Long id, @RequestBody JSONObject jsonObject) {

         Order order = this.orderService.findById(id);
         if (null == order) {
             throw new BusinessException(ResponseCode.NO_ORDER);
         }
         String type = jsonObject.getString("type");
         com.ps.support.Assert.validateNull(type, "type必传");
         if (!SHELVE_TYPE_AUTO.equals(type) && !SHELVE_TYPE_HAND.equals(type)) {
             throw new BusinessException("type 值不合法");
         }


         User user = this.merchantUserService.findMerchantBoss(OutPlatformUtil.getMerchantId());

         this.orderService.shelve(user.getId(), user.getUsername(), id, type);

     }

     //订单释放
     @RequestMapping(value = "{id}/shelveRealse", method = RequestMethod.PUT)
     @ResponseBody
     public void shelveRealse(@PathVariable("id") Long id) {
         User user = this.merchantUserService.findMerchantBoss(OutPlatformUtil.getMerchantId());

         this.orderService.shelveRealse(id, user, OrderShelveConstant.manual);
     }


     @RequestMapping(value = "{no}/track_info", method = RequestMethod.GET)
     @ResponseBody
     public Object trackInfo(@PathVariable("no") String no) {
         Order order = this.orderService.findByNo(no);
         Assert.validateBool(order.getMerchantId().equals(OutPlatformUtil.getMerchantId()), "没有找到对应的订单");
         return this.logisticsService.queryExpressInfo(order.getId(), null, 0);
     }


     @RequestMapping(value = "trackInfo", method = RequestMethod.GET)
     @ResponseBody
     public Object orderTrackInfo(@RequestParam("no") String no) {
         return this.logisticsService.queryExpressInfoByOutOrderNo(OutPlatformUtil.getMerchantId(), no);
     }

     @RequestMapping(value = "trackInfos", method = RequestMethod.GET)
     @ResponseBody
     public List<GatewwayBatchOrderCarriageDTO> orderTrackInfos(@RequestParam("nos") String nos) {
         List<Order> orders = getPermissionOrders(nos);
         List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
         List<OrderCarriageTrackInfosDTO> orderCarriageTrackInfosDTOS = this.logisticsService.queryExpressInfos(orderIds);
         List<GatewwayBatchOrderCarriageDTO> responses = Lists.newArrayList();
         for (Order order : orders) {
             List<GatewwayBatchOrderCarriageTrackInfosDTO> carriageTrackInfoDTOS = Lists.newArrayList();
             GatewwayBatchOrderCarriageDTO response = new GatewwayBatchOrderCarriageDTO();
             for (OrderCarriageTrackInfosDTO orderCarriageTrackInfosDTO : orderCarriageTrackInfosDTOS) {
                 if (orderCarriageTrackInfosDTO.getOrderId().equals(order.getId())) {
                     GatewwayBatchOrderCarriageTrackInfosDTO gatewwayBatchOrderCarriageTrackInfosDTO = new GatewwayBatchOrderCarriageTrackInfosDTO();
//                     List<CarriageTrackInfoDTO> carriageTrackInfoDTOList = Lists.newArrayList();
//                     carriageTrackInfoDTOList.addAll();
//                     response.setStatus(orderCarriageTrackInfosDTO.getStatus());
                     gatewwayBatchOrderCarriageTrackInfosDTO.setCarriageTrackInfos(orderCarriageTrackInfosDTO.getCarriageTrackInfoDTOS());
                     gatewwayBatchOrderCarriageTrackInfosDTO.setStatus(orderCarriageTrackInfosDTO.getStatus());
                     gatewwayBatchOrderCarriageTrackInfosDTO.setCarrigeNo(orderCarriageTrackInfosDTO.getCarriageNo());
                     carriageTrackInfoDTOS.add(gatewwayBatchOrderCarriageTrackInfosDTO);
                 }
             }
             response.setOrderNo(order.getNo());
             response.setCarriageInfos(carriageTrackInfoDTOS);
             responses.add(response);

         }
         return responses;
     }

     public List<Order> getPermissionOrders(String nos) {
         List<String> noList = StringUtils.stringToStringList(nos);
         Assert.validateTrue(noList.size() > 20, "最多同时查询20个订单");
         List<Order> orders = this.orderService.findByNos(noList);
         return orders.stream().filter(o -> o.getMerchantId().equals(OutPlatformUtil.getMerchantId())).collect(Collectors.toList());
     }

     @Autowired
     private OrderItemService orderItemService;

//    @RequestMapping(value = "{no}/address_payment", method = RequestMethod.PUT)
//    @ResponseBody
//    @GlobalTransactional
//    public void updateAddressPayment(@PathVariable("no") String no, @RequestBody JSONObject jsonObject) {
//        Long logisticsId = jsonObject.getLong("logistics_id");
//        Address address = jsonObject.getObject("address",Address.class);
//        orderService.updateAddressPayment(OutPlatformUtil.getMerchantId(),no,logisticsId,address);
//    }


     public boolean checkOrderPayment(List<Long> orderIds) {
         Boolean isViewSelf = ISecurityUtils.checkPermission("order:view:all");
         if (isViewSelf) {
             List<Long> ids = this.officeService.getOfficUser(ISecurityUtils.getCurrUserId(), ISecurityUtils.getMerchantId(), "order:view:all");
             //如果有权限
             if (ids.size() == 0) {
                 throw new com.ps.exception.BusinessException("您没有操作该订单权限");
             } else {
                 List<Order> orders = this.orderService.findByIds(orderIds);
                 List<Long> customsIds = orders.stream().map(Order::getCustomerId).collect(Collectors.toList());
                 if (customsIds.size() > 0) {
                     for (Long id : customsIds) {
                         if (!ids.contains(id)) {
//                         TODO   throw new BusinessException("订单中存在其他人下的单您没有权限操作！");
                         }
                     }
                 } else {
                     throw new com.ps.exception.BusinessException("您没有操作该订单权限");
                 }
             }
         } else {
             throw new com.ps.exception.BusinessException("您没有操作该订单权限");
         }
         return true;
     }

     @RequestMapping(value = "cancel", method = RequestMethod.PUT)
     @ResponseBody
     public void batchCancel(@RequestBody IdsSearchHelper idsSearchHelper) {
         List<Long> idList = idsSearchHelper.getIds();
         User user = this.merchantUserService.findMerchantBoss(OutPlatformUtil.getMerchantId());
         this.orderService.batchCancel(idList, user, Lists.newArrayList(user.getId()));
     }

     @RequestMapping(value = "/{id}/cancel", method = RequestMethod.POST)
     @ResponseBody
     @ApiOperation("取消提交")
     public void afterServiceSubmit(@PathVariable("id") Long id, @RequestBody AfterServiceSubmitVo vo) {
         LockBatchUtil.tryLockWithUnlock(OrderLockConstant.ORDER_ID_Z_SET_LOCK_KEY, TimeUnit.MINUTES.toSeconds(30)
                 , id, "订单状态已变更", () -> {
                     Order order = this.orderService.findById(id);
                     Assert.validateNull(order, "订单不存在");
                     Assert.validateBool(order.getMerchantId().equals(OutPlatformUtil.getMerchantId()), "订单不存在");
                     vo.setTransactionalCode(order.getTransactionCode());
                     if (StringUtil.isBlank(vo.getCause())) {
                         vo.setCause("第三方用户取消");
                     }
                     vo.setType(AfterServiceAuditService.TYPE_AFTER_SERVICE_REFUND);
                     vo.setServiceMoney(null);
                     vo.setMaterialServiceMoney(null);
                     vo.setCurrentStatus(order.getStatus());
                     Long userId = NumberUtils.greaterZero(OutPlatformUtil.getUserId()) ? OutPlatformUtil.getUserId() : 0L;
                     User user = this.merchantUserService.findById(userId);
                     if (user == null || !user.getMerchantId().equals(OutPlatformUtil.getMerchantId())) {
                         user = this.merchantUserService.findMerchantBoss(OutPlatformUtil.getMerchantId());
                     }
                     if (this.afterServiceManage.cancelStatus().contains(vo.getCurrentStatus())) {
                         //取消订单
                         this.afterServiceManage.merchantCancelOrder(id, vo, user);
                     } else {
                         Assert.wrong("此接口只支持状态" + JSONUtil.toJsonStr(this.afterServiceManage.cancelStatus()));
                     }
                 });
     }

     @RequestMapping(value = "preGenCustomOrderItemNo", method = RequestMethod.GET)
     @ResponseBody
     public List<OrderItemNoSaveRespDTO> preGenCustomOrderItemNo(@RequestParam Integer num) {
         OrderItemNoSaveReqDTO orderItemNoSaveReqDTO = OrderItemNoSaveReqDTO.builder().merchantId(OutPlatformUtil.getMerchantId()).num(num).build();
         return this.orderItemNoFeign.save(orderItemNoSaveReqDTO);
     }

     @ResponseBody
     @RequestMapping(value = "/orderCarriageLabel", method = RequestMethod.PUT)
     @ApiOperation("sprint_3.2_批量更新运单号信息")
     public ResDTO<String> updateOrderCarriageLabel(@RequestBody CustomUpdateOrderCarriageUploadLabelParam param) {
         BaseListValidDto<UpdateOrderCarriageUploadLabelParam> list = new BaseListValidDto<>();
         UpdateOrderCarriageUploadLabelParam updateOrderCarriageUploadLabelParam = new UpdateOrderCarriageUploadLabelParam();

         updateOrderCarriageUploadLabelParam.setOrderNo(param.getOrderNo());
         updateOrderCarriageUploadLabelParam.setCarriageNo(param.getCarriageNo());
         ImageUpload imageUpload = imageUploadService.saveLogisticsWaybillPdf(param.getFileUrl(), 0L);
         updateOrderCarriageUploadLabelParam.setFileCode(imageUpload.getFileCode());

         list.setList(Lists.newArrayList(updateOrderCarriageUploadLabelParam));
         this.orderCarriageFeign.updateOrderCarriageLabel(OutPlatformUtil.getMerchantId(), 0L, list);
         ResDTO<String> resDTO = new ResDTO<>();
         resDTO.setData(BasePoConstant.SUCCESS_SIMPLE_STRING);
         return resDTO;
     }
 }

