package com.ps.ps.controller;


import com.ziguang.base.model.Address;
import com.ps.ps.service.AddressService;
import com.ps.ps.task.ImageTask;
import com.ps.support.ISecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping(value="/address")
@Controller
public class AddressController {

   @Autowired
   private AddressService addressService;
   @Autowired
   private ImageTask imageTask;

   /**
    * 查询收货人
    */
   @RequestMapping(value = "",method = RequestMethod.GET)
   @ResponseBody
   public List<Address> queryAddresses(
           @RequestParam(value = "page", defaultValue = "1") Integer page
           , @RequestParam(value = "size", defaultValue = "10") Integer size
           , @RequestParam(required = false) String name
           , @RequestParam(required = false) String phone) {
      return addressService.queryAddresses(page,size, ISecurityUtils.getMerchantId(),name, phone);
   }

   /**
    * 查询收货人
    */
   @RequestMapping(value = "test",method = RequestMethod.GET)
   @ResponseBody
   public List<Address> test(
           @RequestParam(value = "page", defaultValue = "1") Integer page
           , @RequestParam(value = "size", defaultValue = "10") Integer size
           , @RequestParam(required = false) String name
           , @RequestParam(required = false) String phone) {
       imageTask.excute();
       return null;
   }

}
