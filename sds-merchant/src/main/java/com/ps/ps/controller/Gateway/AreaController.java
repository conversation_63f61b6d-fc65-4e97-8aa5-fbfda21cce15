package com.ps.ps.controller.Gateway;


import com.ziguang.base.model.ConfigAddress;
import com.ps.ps.service.CategoryService;
import com.ps.ps.service.CommonService;
import com.ps.support.ListResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value="/gateway/areas")
@RestController("GatewayAreaController")
public class AreaController {
    @Autowired
    private CategoryService categoryService;
    @Autowired
    CommonService commonService;

    @ResponseBody
    @RequestMapping(value = "/{area_code}/items",method = RequestMethod.GET)
    public ListResponse getProvince(@PathVariable("area_code") String areaCode){
        ListResponse listResponse = new ListResponse();
        List<ConfigAddress> list = commonService.getAddress(areaCode);
        listResponse.setItems(list);
        return listResponse;
    }

}
