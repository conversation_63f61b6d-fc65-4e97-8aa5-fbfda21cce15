package com.ps.ps.controller.onlineorder;

import cn.hutool.core.util.ObjectUtil;
import com.ps.ps.feign.platformorder.OnlineOrderFeign;
import com.ps.ps.service.MerchantStoreService;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.orderapi.param.AutoOnlineOrderCreateParam;
import com.ziguang.base.model.MerchantStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.Date;

@Controller
@RequestMapping(value = "/onlineOrder")
@RequiredArgsConstructor
@Slf4j
public class OnlineOrderController {
    
    private final OnlineOrderFeign onlineOrderFeign;
    private final MerchantStoreService merchantStoreService;

    @PostMapping(value = "/admin/orderUpdate")
    @ResponseBody
    public void orderUpdateAdmin(@RequestBody AutoOnlineOrderCreateParam param) {
        onlineOrderFeign.adminAutoImport(param);
    }


    @PostMapping(value = "/orderUpdate")
    @ResponseBody
    public void alibabaOrderUpdate(@RequestBody AutoOnlineOrderCreateParam param) {
        onlineOrderFeign.adminAutoImport(param);
    }
    
    /**
     * 1688订单自动导入
     *
     * @param orderId
     * @param memberId
     */
    @RequestMapping(value = "/alibaba/orderUpdate", method = RequestMethod.GET)
    @ResponseBody
    public void alibabaOrderUpdate(
        @RequestParam(value = "orderId") String orderId,
        @RequestParam(value = "memberId") String memberId
    ) {
        log.info("receive alibaba online order sync id={}", orderId);
        AutoOnlineOrderCreateParam param = new AutoOnlineOrderCreateParam();

        param.setOrderId(orderId);
        param.setPlatformCode(MerchantStorePlatformEnum.ALIBABA.getCode());
        param.setSellerId(memberId);

        onlineOrderFeign.autoImport(param);
    }

    /**
     * 1688店铺失效通知
     *
     * @param memberId
     */
    @RequestMapping("/alibaba/authExpired")
    public void alibabaAuthExpired(
        @RequestParam(value = "memberId") String memberId
    ) {
        MerchantStore store = merchantStoreService.getValidStoreByPlatformAndSellerId(MerchantStorePlatformEnum.ALIBABA, memberId);
        if (ObjectUtil.isNull(store)) {
            return;
        }
        if (store.getAmzAuthorizedStatus() != MerchantStoreService.AMZ_AUTHORIZED_STATUS_SUCCESS) {
            return;
        }

        store.setAmzAuthorizedStatus(MerchantStoreService.AMZ_AUTHORIZED_STATUS_OVERDUE);
        store.setUpdateTime(Date.from(Instant.now()));
        store.setAuthExpireTime(System.currentTimeMillis());
        merchantStoreService.update(store);
    }
}
