package com.ps.ps.controller.product;

import com.ps.ps.feign.product.ProductInquiryPreviewFeign;
import com.ps.support.ISecurityUtils;
import com.sdsdiy.productapi.dto.product.ProductInquiryPreviewReqDto;
import com.sdsdiy.productapi.dto.product.ProductInquiryPreviewRespDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api("产品询价预览")
@RestController
@RequestMapping("/productInquiry/preview")
public class ProductInquiryPreviewController {

    @Resource
    private ProductInquiryPreviewFeign productInquiryPreviewFeign;


    @RequiresPermissions("special:price:application")
    @ApiOperation("批量添加询价清单")
    @PostMapping("/batch")
    public List<ProductInquiryPreviewRespDto> batchPreview(@RequestBody ProductInquiryPreviewReqDto productInquiryPreviewReqDto) {
        productInquiryPreviewReqDto.setMerchantId(ISecurityUtils.getMerchantId());
        return productInquiryPreviewFeign.batchPreview(productInquiryPreviewReqDto);
    }

    @RequiresPermissions("special:price:application")
    @ApiOperation("询价单预览")
    @PostMapping("/one")
    public ProductInquiryPreviewRespDto onePreview(@RequestBody ProductInquiryPreviewReqDto productInquiryPreviewReqDto) {
        productInquiryPreviewReqDto.setMerchantId(ISecurityUtils.getMerchantId());
        return productInquiryPreviewFeign.onePreview(productInquiryPreviewReqDto);
    }

}
