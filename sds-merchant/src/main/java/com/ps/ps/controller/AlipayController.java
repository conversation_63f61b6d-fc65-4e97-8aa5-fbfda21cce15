package com.ps.ps.controller;


import com.ps.ps.service.PaymentService;
import com.ps.ps.service.payment.AlipaySuccessService;
import com.ziguang.base.model.Payment;
import io.seata.spring.annotation.GlobalTransactional;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RequestMapping(value="/alipay")
@Controller
@Deprecated
public class AlipayController {
   org.slf4j.Logger logger = LoggerFactory.getLogger(AlipayController.class);
   @Autowired
   @Lazy
   private PaymentService paymentService;

   @Autowired
   AlipaySuccessService alipaySuccessService;

   /**
    * 查询收货人
    */
   @RequestMapping(value = "return",method = RequestMethod.POST)
   @ResponseBody
   @GlobalTransactional
   public String alipayReturn(HttpServletRequest request) {
      String tradeStatus = request.getParameter("trade_status");
      logger.info("alipay return info" + tradeStatus);
      if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
         String orderNO = request.getParameter("out_trade_no");
         logger.info("alipay return out_trade_no" + orderNO);
         Payment payment = paymentService.finish(orderNO);
         if (payment.getStatus().equals(PaymentService.STATUS_PAIN)) {
            logger.info("alipay return success" + orderNO);
            return "success";
         }
      }
      logger.info("alipay return failed");
      return "";
   }
   /**
    * 支付宝成功通知的接口
    */
   @RequestMapping(value = "success",method = RequestMethod.POST)
   @ResponseBody
   public String alipaySuccess(HttpServletRequest request){
      String tradeStatus = request.getParameter("trade_status");
      logger.info("alipay return success" + tradeStatus);
      if("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)){
         String orderNO = request.getParameter("out_trade_no");
         logger.info("alipay return success out_trade_no" + orderNO);
         boolean payRes = alipaySuccessService.checkSuccess(orderNO);
         if(payRes){
            logger.info("alipay return success" + orderNO);
            return "success";
         }
      }
      logger.info("alipay return failed" );
      return "";
   }
}
