package com.ps.ps.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ps.ps.feign.website.ActivityConfigFeign;
import com.ps.ps.service.AdminConfigService;
import com.ziguang.base.dto.RotationChartDto;
import com.ziguang.base.model.AdminConfigs;
import com.ziguang.base.model.ConfigAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/configs")
public class AdminConfig {

    @Autowired
    private AdminConfigService adminConfigService;

    @RequestMapping(value = "", method = RequestMethod.GET)
    public ConfigAdmin getAdminConfig() {
        return adminConfigService.getConfigOperate("operate_code");
    }

    @RequestMapping(value = "/rotation_chart", method = RequestMethod.GET)
    public List<RotationChartDto> getRotationChart() {
        AdminConfigs adminConfigs = adminConfigService.getCodeValue("rotation_chart");
        JSONArray jsonArray = JSONUtil.parseArray(adminConfigs.getValue());
        List<RotationChartDto> list = jsonArray.toList(RotationChartDto.class);
        return list;
    }

    @RequestMapping(value = "/homeBanner", method = RequestMethod.GET)
    public JSONArray getBanner() {
        AdminConfigs adminConfigs = adminConfigService.getCodeValue("homeBanner");
        return JSONUtil.parseArray(adminConfigs.getValue());
    }

    @RequestMapping(value = "/{code}", method = RequestMethod.GET)
    public JSONObject getByCode(@PathVariable("code") String code) {
        AdminConfigs adminConfigs = adminConfigService.getCodeValue(code);
        try {
            return JSONObject.parseObject(adminConfigs.getValue());
        } catch (Exception e) {
            return null;
        }
    }

    @Autowired
    private ActivityConfigFeign activityConfigFeign;

    @GetMapping("/homePage/activity")
    public Map<String, Object> getHomePageActivity(@RequestParam(required = false, defaultValue = "") String type) {
        return activityConfigFeign.cache(type);
    }
}
