package com.ps.ps.controller.Gateway;


import com.google.common.collect.Lists;
import com.ps.ps.service.*;
import com.ps.support.ItemsResponse;
import com.ps.support.utils.OutPlatformUtil;
import com.ziguang.base.dto.DesignProductDTO;
import com.ziguang.base.dto.DesignProductOutResponseDto;
import com.ziguang.base.support.contant.MaterialGroupType;
import com.ziguang.base.support.es.EsSearchResHelper;
import com.ziguang.sdsdesignproduct.dto.SearchRequestParamDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/gateway/designProducts")
@RestController("GatewayNDesignProductController")
public class NDesignProductController {

    @Autowired
    CommonDesignProductService designProductService;

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public ItemsResponse<DesignProductOutResponseDto> list(
            @RequestParam(value = "page", defaultValue = "1") Integer page
            , @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize
            , @RequestParam(value = "taskId", required = false) Long taskId
            , @RequestParam(value = "keyId", required = false) String keyId
            , @RequestParam(value = "gmtFinishTimeBegin", required = false) String gmtFinishTimeBegin
            , @RequestParam(value = "gmtFinishTimeEnd", required = false) String gmtFinishTimeEnd
    ) {

        SearchRequestParamDto paramDto  =new SearchRequestParamDto();
        paramDto.setMerchantId(OutPlatformUtil.getMerchantId());
        paramDto.setGmtFinishTimeBegin(gmtFinishTimeBegin);
        paramDto.setGmtFinishTimeEnd(gmtFinishTimeEnd);
        paramDto.setPage(page);
        paramDto.setPageSize(pageSize);
        paramDto.setKeyId(keyId);
        paramDto.setTaskId(taskId);
        EsSearchResHelper<DesignProductDTO> esSearchResHelper = designProductService.getRecommendDesignProductMaterialChildDTOS(paramDto);

        designProductService.formatProduct(esSearchResHelper.getItems());
        List<DesignProductOutResponseDto> res = Lists.newArrayList();
        for (DesignProductDTO designProductDTO : esSearchResHelper.getItems()) {
            DesignProductOutResponseDto designProductOutResponseDto = new DesignProductOutResponseDto();
            designProductOutResponseDto.convertByDesignProduct(designProductDTO);
            if(designProductDTO.getVariant() != null) {
                for (DesignProductDTO productDTO : designProductDTO.getVariant()) {
                    if (productDTO.getMaterialGroupType() != MaterialGroupType.PARENT.getStatus()) {
                        DesignProductOutResponseDto child = new DesignProductOutResponseDto();
                        child.convertByDesignProduct(productDTO);
                        designProductOutResponseDto.addVariant(child);
                    }
                }
            }
            res.add(designProductOutResponseDto);
        }
        ItemsResponse<DesignProductOutResponseDto> itemsResponse = new ItemsResponse();
        itemsResponse.setItems(res);
        itemsResponse.setTotalCount(esSearchResHelper.getTotalCount().intValue());

        return itemsResponse;
    }



}
