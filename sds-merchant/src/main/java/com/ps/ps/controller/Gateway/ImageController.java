package com.ps.ps.controller.Gateway;


import com.alibaba.fastjson.JSONObject;
import com.ps.amazon.s3.S3FileEnum;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.support.utils.ConvertUtil;
import com.ziguang.base.model.ImageUpload;
import com.ps.ps.service.ImageUploadService;
import com.ziguang.base.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.util.Iterator;

@RequestMapping(value="/gateway/images")
@RestController("GatewayImageController")
public class ImageController {
    @Autowired
    private ImageUploadService imageUploadService;


    @Autowired
    private S3ServiceV2 s3ServiceV2;

    @RequestMapping(value = "", method = RequestMethod.POST)
    public @ResponseBody
    ImageUpload upload(MultipartHttpServletRequest request) throws IllegalStateException, IOException {
        Iterator<String> itr = request.getFileNames();
        MultipartFile mf = request.getFile(itr.next());
        ImageUpload imageUpload = imageUploadService.save(mf, 0L);
        return imageUpload;
    }
    @RequestMapping(value = "url", method = RequestMethod.POST)
    public @ResponseBody
    ImageUpload upload(@RequestBody JSONObject jsonObject) throws IllegalStateException, IOException {
        return s3ServiceV2.getInfoByUrl(jsonObject.getString("url"));
    }
    @RequestMapping(value = "byBase64", method = RequestMethod.POST)
    public @ResponseBody
    ImageUpload uploadByBase64(@RequestBody JSONObject jsonObject) throws IllegalStateException, IOException {
        return s3ServiceV2.saveByBase64(jsonObject.getString("data"));
    }

//
//    @RequestMapping(value = "add_by_url", method = RequestMethod.POST)
//    public @ResponseBody
//    ImageUpload upload(MultipartHttpServletRequest request) throws IllegalStateException, IOException {
//        Iterator<String> itr = request.getFileNames();
//        MultipartFile mf = request.getFile(itr.next());
//        ImageUpload imageUpload = imageUploadService.save(mf, 0L);
//        return imageUpload;
//    }
}
