package com.ps.ps.controller;

import com.ziguang.base.model.Attribute;
import com.ziguang.base.model.Config;
import com.ziguang.base.dto.ConfigDto;
import com.ps.ps.service.ConfigService;
import com.ps.support.ISecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@RequestMapping("ps/configs")
@Controller
public class ConfigController {
    @Autowired
    ConfigService configService;


    @RequestMapping(value = "page", method = RequestMethod.GET)
    public String page() {
        return "ps/Config";
    }
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @ResponseBody
    public ConfigDto update(@RequestBody ConfigDto configDto) {
        if(configDto.getAutoToFinish() != null){
            configService.update(ISecurityUtils.getCurrUserId(), Config.CODE_AUTO_TO_FINISH,configDto.getAutoToFinish().toString());
        }
        if(configDto.getAutoToSimple() != null){
            configService.update(ISecurityUtils.getCurrUserId(), Config.CODE_AUTO_TO_SIMPLE,configDto.getAutoToSimple().toString());
        }
        return configDto;
    }
    @RequestMapping(value = "", method = RequestMethod.GET)
    @ResponseBody
    public ConfigDto get() {
        ConfigDto configDto = new ConfigDto();
        configDto.setAutoToFinish(configService.getAutoToFinish(ISecurityUtils.getCurrUserId()));
        configDto.setAutoToSimple(configService.getAutoToSimple(ISecurityUtils.getCurrUserId()));
        return configDto;
    }

}
