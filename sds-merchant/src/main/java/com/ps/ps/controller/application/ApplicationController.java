package com.ps.ps.controller.application;

import com.ps.ps.feign.application.ApplicationFeign;
import com.ps.support.ISecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Auther: wangtao
 * @Date: 2021/11/1
 */
@RestController
@RequestMapping("/application")
@Deprecated
public class ApplicationController {

    @Resource
    private ApplicationFeign applicationFeign;

    @GetMapping({"/tenant/me/level"})
    String[] getLevelApplication(@RequestParam(value = "levelType",required = false) String levelType){
        Long tenantId = ISecurityUtils.getTenantId();
        return applicationFeign.getLevelApplication(tenantId, levelType);
    }


}
