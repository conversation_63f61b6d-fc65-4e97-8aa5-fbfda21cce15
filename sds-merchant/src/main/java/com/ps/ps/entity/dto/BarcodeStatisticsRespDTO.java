package com.ps.ps.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/14
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BarcodeStatisticsRespDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    private Integer type;
    @JsonProperty(value = "gmt_last_import")
    private String gmtLastImport;
    private Long lastImportTime;
    private Long userId;
    private Integer status;
    private Integer stock;
    @JsonProperty("generation_type")
    private Integer generationType;
    @ApiModelProperty(value = "库存限制")
    private Integer stockLimit;
}
