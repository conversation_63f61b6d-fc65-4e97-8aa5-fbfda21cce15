
INSERT IGNORE INTO `merchant_product_parent` (
  `product_id`,
  `merchant_id`,
  `type`
)
SELECT
  p.`parent_id`,
  mp.`merchant_id`,
  2
FROM
  `merchant_product` mp,
  product p
WHERE mp.`product_id` = p.`id`
  AND TYPE > 0
GROUP BY p.`parent_id`,
  mp.`merchant_id` ;
  
  
  
INSERT IGNORE INTO `merchant_product_parent` (
  `product_id`,
  `merchant_id`,
  `type`
)
SELECT
  p.`parent_id`,
  mp.`merchant_id`,
  1
FROM
  `merchant_product` mp,
  product p
WHERE mp.`product_id` = p.`id`
  AND TYPE = 0
GROUP BY p.`parent_id`,
  mp.`merchant_id` ;



UPDATE `merchant_product` mp,product p SET mp.`product_parent_id` = p.`parent_id` WHERE mp.`product_id` = p.`id`