ALTER TABLE `sds_design`.design_product_1 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_2 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_3 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_4 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_5 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_6 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_7 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_8 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_9 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_10 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_11 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_12 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_13 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_14 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_15 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_16 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_17 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_18 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_19 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_20 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_21 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_22 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_23 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_24 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_25 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_26 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_27 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_28 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_29 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_30 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_31 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_32 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_33 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_34 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_35 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_36 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_37 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_38 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_39 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_40 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_41 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_42 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_43 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_44 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_45 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_46 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_47 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_48 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_49 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_50 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_51 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_52 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_53 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_54 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_55 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_56 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_57 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_58 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_59 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_60 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_61 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_62 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_63 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_64 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_65 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_66 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_67 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_68 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_69 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_70 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_71 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_72 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_73 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_74 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_75 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_76 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_77 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_78 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_79 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_80 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_81 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_82 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_83 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_84 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_85 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_86 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_87 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_88 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_89 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_90 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_91 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_92 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_93 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_94 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_95 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_96 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_97 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;
ALTER TABLE `sds_design`.design_product_98 ADD COLUMN `material_key` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '素材key' AFTER `attributes_json`, ADD COLUMN `material_group_size` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材尺寸' AFTER `size`, ADD COLUMN `material_group_pid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '按图片分组的父id' AFTER `material_key`, ADD COLUMN `material_group_type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '类型1,普通成品 2素材组父成品' AFTER `material_group_pid`, ADD COLUMN `material_color` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '素材组对应的颜色' AFTER `material_group_type`, AUTO_INCREMENT=11053349316708;

;


ALTER TABLE `order_import_record_item`
  ADD COLUMN `hicustom_sku` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '指纹对应的sku' AFTER `key_id`,
  ADD COLUMN `import_asin_parent` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '亚马逊导入时母体的asin' AFTER `hicustom_sku`;
ALTER TABLE `order_import_record_item`
  ADD  INDEX `idx_hicustom_sku` (`hicustom_sku`),
  ADD  INDEX `idx_asin_parent` (`import_asin_parent`);
ALTER TABLE `order_import_record_order_fba_item`
  ADD COLUMN `hicustom_sku` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '指纹sku' AFTER `update_time`;


-- ===========================zjl start ==============================
drop index Index_1 on product_label;

drop table if exists product_label;

/*==============================================================*/
/* Table: product_label                                         */
/*==============================================================*/
create table product_label
(
   id                   bigint(20) not null auto_increment,
   merchant_id          bigint(20) not null,
   user_id              bigint(20) not null,
   name                 varchar(255) not null,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table product_label comment '产品标签';

/*==============================================================*/
/* Index: Index_1                                               */
/*==============================================================*/
create index Index_1 on product_label
(
   merchant_id,
   user_id
);

-- ===========================zjl end ==============================



  UPDATE warehouse SET is_PUT=0 WHERE	 STATUS=1;

  DROP TABLE `warehouse_item`;


ALTER TABLE `product`
  ADD COLUMN `on_sale_status` TINYINT(3) UNSIGNED DEFAULT 2 NOT NULL COMMENT '促销状态1开启 2关闭' AFTER `box_height`,
  ADD COLUMN `on_sale_begin` TIMESTAMP DEFAULT '2010-01-01' NOT NULL COMMENT '促销开始时间' AFTER `on_sale_status`,
  ADD COLUMN `on_sale_end` TIMESTAMP DEFAULT '2010-01-01' NOT NULL COMMENT '促销结束时间' AFTER `on_sale_begin`,
  ADD COLUMN `on_sale_price` DECIMAL(20,2) UNSIGNED NOT NULL COMMENT '促销价' AFTER `on_sale_end`;



ALTER TABLE `product`
  ADD COLUMN `on_sale_begin_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '促销开始时间' AFTER `on_sale_status`,
  ADD COLUMN `on_sale_end_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '促销结束时间' AFTER `on_sale_begin`;
ALTER TABLE `product`
  DROP COLUMN `on_sale_begin`,
  DROP COLUMN `on_sale_end`;
