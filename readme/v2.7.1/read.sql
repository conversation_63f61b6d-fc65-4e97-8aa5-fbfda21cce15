
ALTER TABLE `nationality`
  ADD COLUMN `show_country_detail` TINYINT(4) UNSIGNED DEFAULT 1 NOT NULL COMMENT '1不显示2显示具体的省市区' AFTER `sort`;

  UPDATE `nationality` SET `show_country_detail` = '2' WHERE `id` = '37';


UPDATE product SET STATUS = 2 WHERE parent_id  = 0;
UPDATE product SET `status`= 1 WHERE id IN (SELECT t.id FROM (SELECT DISTINCT(parent_id) AS id FROM product WHERE `status` = 1) AS t);


CREATE TABLE `logistic_product`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `logistic_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `product_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `create_time` BIGINT(100) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=MYISAM CHARSET=utf8mb4;
ALTER TABLE `logistic_product`
  CHANGE `create_time` `create_time` BIGINT(100) UNSIGNED DEFAULT 0 NOT NULL,
  ADD COLUMN `product_parent_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `create_time`;

  ALTER TABLE `logistic_product`
  ADD COLUMN `status` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '1关联2取消关联' AFTER `product_parent_id`;



  CREATE TABLE `country_express_info_area`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `country_express_info_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `code` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '省市区的code',
  `parent_code` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '父亲code',
  `status` TINYINT(4) UNSIGNED NOT NULL DEFAULT 2 COMMENT '1开启2没有',
  `create_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=MYISAM CHARSET=utf8mb4;
ALTER TABLE `sds_saas_dev`.`country_express_info_area`
  ADD COLUMN `logistic_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `create_time`;
 UPDATE `config_area` SET `id` = '710000' WHERE `id` = '120000';
 UPDATE `config_area` SET `id` = '720000' WHERE `id` = '130000';
UPDATE `config_area` SET `id` = '730000' WHERE `id` = '140000';
INSERT INTO `config_address` (area_code,`name`,parent_id,area_type) VALUE ("720000","台湾",0,1),("730000","香港特别行政区",0,1),("740000","澳门特别行政区",0,1);
INSERT INTO `config_address` (area_code,`name`,parent_id,area_type) VALUE ("740010","澳门特别行政区",10005,2),("740020","澳门半岛",10005,2),("740030","凼仔",10005,2),("740040","路凼城",10005,2);
INSERT INTO `config_address` (area_code,`name`,parent_id,area_type) VALUE ("730010","香港岛",10004,2),("730020","九龙",10004,2),("730030","新界",10004,2);
INSERT INTO `config_address` (area_code,`name`,parent_id,area_type) VALUE
("720010","彰化县",10003,2)
,("720020","嘉义县",10003,2)
,("720030","新竹县",10003,2)
,("720040","花莲县",10003,2)
,("720050","宜兰县",10003,2)
,("720060","高雄",10003,2)
,("720070","基隆市",10003,2)
,("720080","金门县",10003,2)
,("720090","连江县",10003,2)
,("720100","苗栗县",10003,2)
,("720110","南投县",10003,2)
,("720120","澎湖县",10003,2)
,("720130","屏东县",10003,2)
,("720140","台东县",10003,2)
,("720150","台中市",10003,2)
,("720160","台南市",10003,2)
,("720170","台北市",10003,2)
,("720180","桃园市",10003,2)
,("720190","云林县",10003,2)
,("720200","新北市",10003,2)
,("720210","新竹市",10003,2);
/*[下午 16:14:25][30 ms]*/ UPDATE `config_address` SET `area_code` = '130601' WHERE `id` = '5502';
ALTER TABLE `sds_saas_test`.`carriage_no_recode`
  ADD COLUMN `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL AFTER `price_difference`;

-- zjl sql start
ALTER TABLE `order_import_record_item` ADD COLUMN `province_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '省ID' AFTER `province`, ADD COLUMN `city_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '市ID' AFTER `city`;
ALTER TABLE `address` ADD COLUMN `province_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '省ID' AFTER `province`, ADD COLUMN `city_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '市ID' AFTER `city`;
ALTER TABLE `carriage_no_recode` ADD COLUMN `original_price` DOUBLE(16,2) NULL AFTER `logistics_id`, ADD COLUMN `current_price` DOUBLE(16,2) NULL AFTER `original_price`, ADD COLUMN `price_difference` DOUBLE(16,2) NULL AFTER `current_price`;


drop index uniq_word on sensitive_word;

drop table if exists sensitive_word;

/*==============================================================*/
/* Table: sensitive_word                                        */
/*==============================================================*/
create table sensitive_word
(
   id                   bigint(20) not null auto_increment,
   word                 varchar(128) not null,
   is_delete            tinyint(1) not null default 0,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table sensitive_word comment '敏感词';

/*==============================================================*/
/* Index: uniq_word                                             */
/*==============================================================*/
create unique index uniq_word on sensitive_word
(
   word
);

-- zjl sql end