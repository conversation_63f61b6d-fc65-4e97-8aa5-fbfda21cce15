

INSERT INTO logistic` (`id`, `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`, `aliexpress_code`, `warehouse_carrier_service`, `refund_percent`, `refund_reset_num`, `standard_name`) VALUES (NULL, '云途日本专线挂号（带电）', '4', '0', '', '1', '3', 'PK0014', '', '0', '2', '1', '', '', '0', '0', '');
ALTER TABLE `refund_record` ADD COLUMN `after_service_audit_id` BIGINT(20) DEFAULT 0 NOT NULL AFTER `voucher_img_urls`;