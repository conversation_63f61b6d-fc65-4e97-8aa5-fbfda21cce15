ALTER TABLE `prototype`
  ADD COLUMN `production_manuscript_status` CHAR(6) DEFAULT 'NONE' NOT NULL COMMENT '生产稿件状态 NONE 未设置 ,SET 已设置' AFTER `del_flag`,
  ADD COLUMN `production_manuscript_type` CHAR(6) DEFAULT 'UNIT' NOT NULL COMMENT ' 生产稿件类型 UNIT 使用同一个生产稿件 ,APART 使用各自的生产稿件' AFTER `production_manuscript_status`,
  ADD COLUMN `lens_id` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '滤镜id' AFTER `production_manuscript_type`;


  CREATE TABLE `prototype_lens`(
  `id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '滤镜code',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '滤镜名称',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `remark` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
)
COMMENT='模板滤镜';


CREATE TABLE `prototype_production_manuscript`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `prototype_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '原型id',
  `file_code` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '文件code',
  `file_url` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '文件url',
  `thumbnail_url` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '缩略图',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `index_prototype_id` (`prototype_id`)
);

ALTER TABLE `prototype_production_manuscript`
  ADD COLUMN `product_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '产品id' AFTER `prototype_id`;

ALTER TABLE `prototype_layer`
  ADD COLUMN `lens_id` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '滤镜id' AFTER `print_width`;

-----郑响----
ALTER TABLE `order` ADD COLUMN `refund_status` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '0不显示 1退款中 2退款成功' AFTER `usable_free_gold`;
ALTER TABLE `task_child_layer`
  ADD COLUMN `lens_code` CHAR(8) DEFAULT '' NOT NULL COMMENT '滤镜' AFTER `resize_mode`;
ALTER TABLE `sds_saas_dev`.`order` ADD COLUMN `refund_status` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '0不显示 1退款中 2退款成功' AFTER `usable_free_gold`;
ALTER TABLE `task_child_layer`
  ADD COLUMN `lens_code` CHAR(16) DEFAULT '' NOT NULL COMMENT '滤镜' AFTER `resize_mode`;


  UPDATE `order` o,`refund_record` rr SET o.`refund_status` = rr.`status` WHERE o.`no` = rr.`no`;



  ALTER TABLE `prototype_production_manuscript`
  CHANGE `file_code` `file_code` VARCHAR(128) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '文件id',
  ADD COLUMN `psd_code` VARCHAR(128) DEFAULT '' NOT NULL COMMENT 'psd_code' AFTER `created_time`;



CREATE TABLE `order_item_production_manuscript`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_item_id` BIGINT(20) UNSIGNED NOT NULL,
  `type` TINYINT(3) UNSIGNED NOT NULL,
  `img_url` VARCHAR(512) NOT NULL,
  `text_content` VARCHAR(128) NOT NULL,
  `created_time` BIGINT(20) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`)
);

ALTER TABLE `order_item_production_manuscript`
  CHANGE `order_item_id` `order_item_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL,
  CHANGE `type` `type` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL,
  CHANGE `img_url` `img_url` VARCHAR(512) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL,
  CHANGE `text_content` `text_content` VARCHAR(128) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL,
  CHANGE `created_time` `created_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL;


CREATE TABLE `order_item_production_manuscript_task`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_item_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单item',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` CHAR(8) NOT NULL DEFAULT 'NONE' COMMENT '成功 SUCCESS',
  `failed_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  PRIMARY KEY (`id`)
);




INSERT INTO `order_item_production_manuscript_task` (`order_item_id`)
SELECT
  id
FROM
  `order_item`
WHERE `status` IN (2, 3, 4)


ALTER TABLE `task_child_layer`
  ADD COLUMN `material_json` VARCHAR(1024) DEFAULT '' NOT NULL COMMENT '用到的所有素材id' AFTER `lens_code`;

ALTER TABLE `order_item_production_manuscript`
  ADD COLUMN `layer_name` VARCHAR (128) DEFAULT '' NOT NULL COMMENT '图层名称' AFTER `type`,
  ADD COLUMN `layer_id` BIGINT (20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '图层id' AFTER `layer_name`,
  ADD COLUMN `origin` VARCHAR (32) DEFAULT '' NOT NULL COMMENT '图片来源 ，图层' AFTER `layer_id`

ALTER TABLE `psd`
  ADD COLUMN `md5` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '文件md5' AFTER `photo_service_file_url`,
  ADD  INDEX `index_md5` (`md5`);
