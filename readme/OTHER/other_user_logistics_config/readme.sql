drop index l_id on logistics_config;

drop table if exists logistics_config;

/*==============================================================*/
/* Table: logistics_config                                      */
/*==============================================================*/
create table logistics_config
(
   id                   bigint(20) not null auto_increment,
   logistic_id          bigint(20) not null,
   config_info          text comment '物流配置',
   enable_config        tinyint(1) not null default 0 comment '启用配置',
   shipper_info         text comment '发货人信息',
   enable_shipper        tinyint(1) not null default 0 comment '启用发货人',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table logistics_config comment '物流配置(针对发货人与物流密钥等)';

/*==============================================================*/
/* Index: l_id                                                  */
/*==============================================================*/
create unique index l_id on logistics_config
(
   logistic_id
);

ALTER TABLE `order` ADD COLUMN `xf_sync_status` TINYINT(1) DEFAULT 2 NOT NULL COMMENT '1 同步中 2 同步完成' AFTER `xf_extend_no`;