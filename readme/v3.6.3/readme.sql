ALTER TABLE `order_import_record_order`
  ADD COLUMN `currency_code` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '货币code' AFTER `address_md5`;


ALTER TABLE `order`
  ADD COLUMN `out_total_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '第三方订单号总金额' AFTER `issuing_bay_id`,
  ADD COLUMN `out_logistics_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '第三方订单物流金额' AFTER `out_total_amount`,
  ADD COLUMN `out_product_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '第三方订单产品金额' AFTER `out_logistics_amount`;
ALTER TABLE `order`
  ADD COLUMN `out_currency_code` VARCHAR(16) DEFAULT '' NOT NULL AFTER `out_product_amount`;
ALTER TABLE `order`
  CHANGE `out_currency_code` `out_currency_code` VARCHAR(16) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '币种',
  ADD COLUMN `exchange_rate` DECIMAL(20,6) UNSIGNED DEFAULT 0 NOT NULL COMMENT '汇率' AFTER `out_currency_code`;
ALTER TABLE `order`
  ADD COLUMN `commit_rate` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '费率' AFTER `exchange_rate`;





UPDATE
  `order` o,
  order_import_record_order io
SET
  o.`out_total_amount` = io.`amount`,
  o.`out_logistics_amount` = io.`logistics_amount`,
  o.out_currency_code = io.`currency_code`
WHERE o.`order_import_record_order_id` = io.`id`;
