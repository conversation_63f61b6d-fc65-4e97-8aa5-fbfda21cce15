------zx-----
ALTER TABLE `refund_record`
  ADD COLUMN `type` VARCHAR(50) DEFAULT 'CANCEL'  NOT NULL   COMMENT 'CANCEL 取消 AFTER_SALE 售后 CHANGE_ADDRESS运费变动' AFTER `add_consume`,
  ADD COLUMN `verify_date` BIGINT(20) DEFAULT 0  NOT NULL   COMMENT '加入时间' AFTER `type`,
  ADD COLUMN `verify_images` TEXT NULL   COMMENT '退款凭证图片' AFTER `verify_date`;
ALTER TABLE `order`
  ADD COLUMN `product_names` TEXT  NULL   COMMENT '产品名称' AFTER `product_num`;

ALTER TABLE `factory_order`
  ADD COLUMN `cancal_status` INT(1) DEFAULT 0  NOT NULL   COMMENT '取消订单' AFTER `add_warning_date`;

COLUMN `outConfirmDate` INT(20) DEFAULT 2075354810000  NOT NULL AFTER `out_harvest_date`;




  ALTER TABLE `sds_saas_dev`.`early_warning_type`
  ADD COLUMN `is_parent` INT(1) DEFAULT 0  NOT NULL AFTER `is_delete`;

  ALTER TABLE `factory_order`
  CHANGE `out_date` `out_date` BIGINT(50) DEFAULT 2075354810000  NOT NULL   COMMENT '最近超期时间';




  UPDATE refund_record SET voucher_img_urls=CONCAT('[','"',remittance_image,'"',']') WHERE remittance_image IS NOT NULL OR remittance_image !=''
  UPDATE refund_record SET voucher_img_urls='' WHERE voucher_img_urls='[""]'

ALTER TABLE `notification`
  CHANGE `type` `type` TINYINT(1) NOT NULL   COMMENT '1体现，2线下充值，3服务 5退款 6售后[退款] 7售后[重发] 8退运费',
  CHANGE `status` `status` TINYINT(1) NOT NULL   COMMENT '1被驳回（提现） 2通过（提现）  3平台主动开通会员套餐 4平台主动提高功能额度 5平台主动变更会员套餐 6年会员套餐只剩最后一个月 7 年会员套餐只剩24小时 8 月会员套餐只剩最后7天 9被驳回 10  通过 11 财务已打款  12 财务已打款 通过',
  ADD COLUMN `image_url` VARCHAR(255) DEFAULT ''  NOT NULL   COMMENT '汇款凭证' AFTER `relate_id`;

 ALTER TABLE `notification`
  CHANGE `status` `status` TINYINT(1) NOT NULL   COMMENT '1被驳回（提现） 2通过（提现）  3平台主动开通会员套餐 4平台主动提高功能额度 5平台主动变更会员套餐 6年会员套餐只剩最后一个月 7 年会员套餐只剩24小时 8 月会员套餐只剩最后7天 9被驳回 10  通过 11 财务已打款  15 财务已打款 13被驳回[退款] 14 通过[退款]';

ALTER TABLE `early_warning_type`
  CHANGE `is_parent` `parent_id` INT(1) DEFAULT 0  NOT NULL;


 ALTER TABLE `factory_order`
  CHANGE  'out_date' 'out_date' BIGINT(50) NOT NULL DEFAULT 2075354810000 commit  '最近超期时间' AFTER  'out_confirm_date',
 add column  'cancal_status' int(1) not null DEFAULT  0 commit  '取消订单 1取消中 2已取消' after 'add_warning_date',
  ADD COLUMN `out_urge_date` BIGINT(20) DEFAULT 2075354810000  NOT NULL AFTER `cancal_status`;


ALTER TABLE `refund_record`
  ADD COLUMN `type` VARCHAR(50) DEFAULT 'CANCEL'  NOT NULL   COMMENT 'CANCEL 取消 AFTER_SALE 售后 CHANGE_ADDRESS运费变动' AFTER `add_consume`,
  ADD COLUMN `verify_date` BIGINT(20) DEFAULT 0  NOT NULL   COMMENT '加入时间' AFTER `type`,
  ADD COLUMN `verify_images` TEXT NULL   COMMENT '退款凭证图片' AFTER `verify_date`;

ALTER TABLE `order`
  ADD COLUMN `out_payment_date` BIGINT(50) DEFAULT 2075516809000  NOT NULL AFTER `product_name`,
  ADD COLUMN `out_express_date` BIGINT(50) DEFAULT 2075516809000  NOT NULL AFTER `out_payment_date`,
  ADD COLUMN `out_harvest_date` BIGINT(50) DEFAULT 2075516809000  NOT NULL AFTER `out_express_date`,
  ADD COLUMN `out_confirm_date` BIGINT(50) DEFAULT 2075516809000  NOT NULL AFTER `out_harvest_date`,
  ADD COLUMN `out_cycle_date` BIGINT(50) DEFAULT 2075516809000  NOT NULL AFTER `out_confirm_date`,
  ADD COLUMN `out_urge_date` BIGINT(50) DEFAULT 2075516809000  NOT NULL AFTER `out_cycle_date`;

  UPDATE `factory_order` SET out_date=UNIX_TIMESTAMP(out_cycle_date)*1000;

ALTER TABLE `early_warning_type`
  ADD COLUMN `parent_id` int(1)   NOT NULL default 0  AFTER `is_delete`;
  ADD COLUMN `remark` VARCHAR(255) DEFAULT ''  NOT NULL   COMMENT '描述' AFTER `parent_id`;

UPDATE `early_warning_type` SET remark=NAME  WHERE id NOT IN (2,3,4,5,6,7,8);

UPDATE `order` o  SET out_date =  (SELECT IFNULL(MIN(f.`out_date`),2075611541000) AS min_out_urge_date FROM  factory_order f  WHERE  f.`status` IN (1,2,3,4,5));


UPDATE `order` o  LEFT JOIN (SELECT o.id AS id ,IFNULL(MIN(fo.out_date),2075354810000) AS out_date FROM `order` o LEFT  JOIN  factory_order fo ON o.no=fo.merchant_order_no    GROUP BY  o.no  ) AS a   ON o.id=a.id SET o.out_date=a.out_date;


UPDATE `order` SET  out_date='2075354810000' WHERE STATUS NOT IN (1,2,3);

UPDATE order_item oi LEFT JOIN factory_order fo ON oi.id=fo.order_item_id SET oi.factory_order_status=98 WHERE fo.status =98;

UPDATE order_item oi LEFT JOIN factory_order fo ON oi.id=fo.order_item_id SET oi.factory_order_status=6 WHERE fo.status =6;

UPDATE factory_order SET out_date =UNIX_TIMESTAMP(out_cycle_date)*1000 WHERE STATUS IN (1,2,3,4,5);