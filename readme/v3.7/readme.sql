ALTER TABLE `order`
  ADD COLUMN `origin_type` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '来源类型,CART购物车 IMPORT,FBA' AFTER `private_status`,
  ADD COLUMN `service_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '服务费' AFTER `carriage_free_gold`,
  ADD COLUMN `refund_service_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '退款服务费' AFTER `service_amount`,
  ADD COLUMN `carriage_pay_staus` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '运费结算状态' AFTER `refund_service_amount`;


ALTER TABLE `order_item`
  ADD COLUMN `service_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '服务费' AFTER `be_resend_for_lose`;

  CREATE TABLE `fba_order_item_history`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `item_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'item_id',
  `order_original_price` DECIMAL(20,2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '原价',
  `order_price` DECIMAL(20,2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前价格',
  `factory_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '工厂id',
  `product_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品id',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`)
);



CREATE TABLE `fba_order_task`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `status` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态1已创建 2已完成',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_status_time` (`status`, `created_time`)
);

ALTER TABLE `fba_order_task`
  DROP INDEX `idx_order_id`,
  ADD  UNIQUE INDEX `idx_order_id` (`order_id`);



ALTER TABLE `fba_order_item_history`
  ADD COLUMN `num` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '数量' AFTER `item_id`;


ALTER TABLE `fba_order_item_history`
  ADD COLUMN `supply_price` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '供应价' AFTER `num`,
  ADD COLUMN `supply_max_day` INT(0) UNSIGNED DEFAULT 0 NOT NULL COMMENT '最高天数' AFTER `supply_price`;


ALTER TABLE `order_item`
  ADD COLUMN `order_import_fba_item_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '导入的fba_item_id' AFTER `import_item_id`;


ALTER TABLE `factory_order`
  ADD COLUMN `origin_type` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '订单来源' AFTER `is_manuscript_over`,
  ADD COLUMN `fnsku` VARCHAR(64) DEFAULT '' NOT NULL COMMENT 'fnsku' AFTER `origin_type`;



ALTER TABLE `after_service_audit`
  ADD COLUMN `request_refund_service_money` DECIMAL(20,2) UNSIGNED NOT NULL COMMENT '申请退款服务费金额' AFTER `merchant_no`,
  ADD COLUMN `real_refund_service_money` DECIMAL(20,2) UNSIGNED NOT NULL COMMENT '实际退款服务费金额' AFTER `request_refund_carriage_money`;
ALTER TABLE AS service_money`refund_record`
  ADD COLUMN `service_price` DECIMAL(20,2) UNSIGNED NOT NULL COMMENT '服务费' AFTER `carriage_price`;


ALTER TABLE AS service_money`order` DROP INDEX `index_merchant`, ADD KEY `index_merchant` (`merchant_id`, `order_import_record_order_fba_id`);

-- =============zjl s========================================================================
drop table if exists order_fba;

drop table if exists order_fba_box;

drop table if exists order_fba_box_item;

drop table if exists order_import_record_order_fba;

drop table if exists order_import_record_order_fba_item;

/*==============================================================*/
/* Table: order_fba                                             */
/*==============================================================*/
create table order_fba
(
   id                   bigint(20) not null auto_increment,
   order_id             bigint(20) not null,
   payment_id           bigint(20) not null default 0 comment '运费支付ID',
   labels_json          text comment '装箱标签json数组',
   pickup_code          varchar(20) not null default '' comment '取货码',
   pickup_code_url      text comment '取件条码',
   pickup_time          bigint(20) not null default 0 comment '核销时间',
   pickup_people        varchar(20) not null default '' comment '取件人',
   operator             varchar(20) not null default '' comment '操作员',
   user_id              bigint(20) not null comment '操作员ID',
   status               int(2) not null default 0 comment '11 待装箱 12 待支付运费 13 待上传装箱标签 14  已上传装箱标签 15 待发货 16 待自提 17 已完成',
   carriage_money       double(16,2) not null default 0 comment '物流金额',
   product_label_printed tinyint(1) not null default 0 comment '产品标签是否(boolean)打印',
   box_label_printed    tinyint(1) not null default 0 comment '箱子标签是否(boolean)打印',
   take_label_printed   tinyint(1) not null default 0 comment '提货标签是否(boolean)打印',
   producted_status     int(2) not null default 1 comment '1 生产中 2 已生产',
   encasement_time      bigint(20) not null default 0 comment '装箱时间',
   upload_label_time    bigint(20) not null default 0 comment '上传箱标时间',
   pay_carriage_time    bigint(20) not null default 0 comment '支付运费时间',
   producted_time       bigint(20) not null default 0 comment '生成完成时间',
   shipped_time         bigint(20) not null default 0 comment '发货时间',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table order_fba comment 'FBA订单信息';

/*==============================================================*/
/* Table: order_fba_box                                         */
/*==============================================================*/
create table order_fba_box
(
   id                   bigint(20) not null auto_increment,
   order_id             bigint(20) not null,
   order_fba_id         bigint(20) not null,
   serial_num           int not null comment '纸箱编号',
   total_num            int not null comment '总数',
   weight               double(14,3) not null comment '重量(kg)',
   box_length           double(14,1) not null comment '箱长',
   box_width            double(14,1) not null comment '箱宽',
   box_height           double(14,1) not null comment '箱高',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table order_fba_box comment 'fba订单箱';

/*==============================================================*/
/* Table: order_fba_box_item                                    */
/*==============================================================*/
create table order_fba_box_item
(
   id                   bigint(20) not null auto_increment,
   order_fba_box_id     bigint(20) not null,
   order_item_id        bigint(20) not null,
   label_fnsku          varchar(50) not null,
   num                  int not null comment '数量',
   product_name         varchar(256) not null default '' comment '产品名称',
   produt_color_block   varchar(256) not null default '' comment '产品颜色',
   product_size         varchar(128) not null default '' comment '产品规格',
   texture_name         varchar(64) not null default '' comment '材质',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table order_fba_box_item comment 'fba箱内商品';

/*==============================================================*/
/* Table: order_import_record_order_fba                         */
/*==============================================================*/
create table order_import_record_order_fba
(
   id                   bigint(20) not null auto_increment,
   merchant_id          bigint(20) not null,
   user_id              bigint(20) not null comment '操作者',
   merchant_store_platform_code varchar(25) not null default '',
   merchant_store_id    bigint(20) not null,
   seller_id            varchar(32) not null default '' comment 'seller id',
   site                 varchar(25) not null default '' comment '归属站点',
   out_order_no         varchar(50) not null comment '订单号',
   receiver             varchar(128) not null default '' comment '收件人姓名',
   country              varchar(50) not null default '' comment '国家code如US',
   postcode             varchar(20) not null default '' comment '邮编',
   mobile_phone         varchar(128) not null default '' comment '移动电话',
   province             varchar(128) not null default '' comment '省',
   province_code        varchar(20) not null default '',
   city                 varchar(50) not null default '' comment '市',
   city_code            varchar(20) not null default '',
   detail               text not null comment '详细地址',
   plan_id              varchar(50) not null default '' comment '计划ID',
   repetition           tinyint(1) not null default 0 comment '订单号是否已下过订单',
   is_delete            tinyint(1) not null default 0,
   delete_time          bigint(20) not null default 0,
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   update_time          timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table order_import_record_order_fba comment 'fba订单导入记录';

/*==============================================================*/
/* Table: order_import_record_order_fba_item                    */
/*==============================================================*/
create table order_import_record_order_fba_item
(
   id                   bigint(20) not null auto_increment,
   order_import_record_order_fba_id bigint(20) not null,
   out_order_no         varchar(50) not null default '' comment '订单号',
   num                  int(11) not null comment '产品数',
   import_sku           varchar(1024) not null comment '导入sku',
   sku                  varchar(255) not null default '' comment '匹配sku(旧成品关联字段兼容性保留)',
   repeat_no            int(11) not null default 0 comment '订单号是否已下过订单',
   end_product_id       bigint(20) not null default 0 comment '成品ID',
   is_delete            tinyint(1) not null default 0,
   import_product_name  varchar(255) not null default '' comment '导入产品名',
   import_color         varchar(1024) not null default '' comment '导入颜色',
   import_size          varchar(1024) not null default '' comment '导入规格',
   import_imgs          text comment '导入图片',
   img_load_status      tinyint(4) not null default 0 comment '导入图片下载状态：0 未下载 1下载中 2 下载完成 3 下载失败',
   item_order_no        varchar(50) not null default '' comment 'amz_item订单号',
   import_asin          varchar(255) not null default '',
   label_fnsku          varchar(50) not null default '' comment '标签-FNSKU',
   label_fnsku_url      text comment '标签-FNSKU条形码',
   label_status         varchar(50) not null default '' comment '标签-状况',
   product_name         varchar(255) not null default '' comment '关联成品（产品）名称',
   key_id               varchar(16) not null default '' comment '关联成品KEYID',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table order_import_record_order_fba_item comment 'fba导入记录item';
ALTER TABLE `order` ADD COLUMN `order_import_record_order_fba_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT 'fba导入订单ID' AFTER `order_import_record_order_id`;
ALTER TABLE `logistic` ADD COLUMN `type` TINYINT(1) DEFAULT 2 NOT NULL COMMENT '1 fba物流 2 普通物流' AFTER `be_common`;

INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `menu_type`) VALUES (NULL, '201', '0,1,201,', 'FBA订单列表', '5', '/fba-order', NULL, '', '1', '', '2019-02-22 07:57:09', NULL, '0', '8');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `menu_type`) VALUES (NULL, '201', '0,1,201,', 'FBA待自提', '6', '/fba-pick', NULL, '', '1', '', '2019-02-22 07:57:09', NULL, '0', '8');
-- =============zjl e==========================================================


ALTER TABLE `refund_record`
  ADD COLUMN `service_price` DECIMAL(20,2) UNSIGNED NOT NULL COMMENT '退款服务费' AFTER `carriage_price`;


  ALTER TABLE `order_item`
  ADD COLUMN `fba_update` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT 'fba变更' AFTER `production_cycle_max`;

ALTER TABLE `order_item`
  ADD COLUMN `origin_price` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '原始价格' AFTER `end_product_id`;
ALTER TABLE `order`
  ADD COLUMN `latest_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '超时时间' AFTER `cancel_time`;
