-- =========================zjl start
ALTER TABLE `merchant_store` ADD COLUMN `bind_logistic_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '绑定物流ID' AFTER `send_fail_handle`;
ALTER TABLE `order` ADD COLUMN `carriage_service_charge` DECIMAL(20,2) DEFAULT 0 NOT NULL COMMENT '物流手续费' AFTER `refund_carriage_amount`;

ALTER TABLE `after_service_audit` ADD COLUMN `order_carriage_amount` DOUBLE(12,2) DEFAULT 0 NOT NULL COMMENT '订单总运费' AFTER `evidence_imgs`, ADD COLUMN `order_new_carriage_amount` DOUBLE(12,2) DEFAULT 0 NOT NULL COMMENT '订单新运费' AFTER `order_carriage_amount`;
ALTER TABLE `merchant_bill` ADD COLUMN `order_alteration_record_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '订单变更记录ID' AFTER `usable_free_gold`;
-- =========================zjl end