---------郑响--------
CREATE TABLE `warehouse`(
  `id` INT(20) NOT NULL AUTO_INCREMENT,
  `order_id` INT(20) NOT NULL DEFAULT 0,
  `status` INT(1) NOT NULL DEFAULT 0 COMMENT '1空仓 2已分配',
  `is_put` INT(1) NOT NULL DEFAULT 1 COMMENT '1未全部入库 2已全部入库',
  `num` INT(20) NOT NULL DEFAULT 0 COMMENT '订单量',
  `created_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`)
);

ALTER TABLE `factory_order`
  CHANGE `put_in_storage_status` `put_in_storage_status` INT(1) DEFAULT 1  NOT NULL   COMMENT '1未入库 2已入库 3不入库';


CREATE TABLE `warehouse_item`(
  `id` INT(20) NOT NULL AUTO_INCREMENT,
  `factory_order_id` INT(20) NOT NULL,
  `status` INT(1) NOT NULL DEFAULT 1 COMMENT '1未入库 2已入库',
  `option_user` INT(20) NOT NULL,
  `created_time` INT(20) NOT NULL,
  `put_in_time` INT(20) NOT NULL COMMENT '入库时间',
  `warehouse_id` INT(20) NOT NULL,
  PRIMARY KEY (`id`)
);
ALTER TABLE `factory_order`
  ADD COLUMN `put_in_storage_status` INT(1) DEFAULT 1  NOT NULL   COMMENT '1未入库 2已入库' AFTER `out_urge_date`,
  ADD COLUMN `finished_time` INT(20) DEFAULT 0  NOT NULL   COMMENT '质检完成时间' AFTER `put_in_storage_status`,
  ADD COLUMN `finished_user` INT(20) DEFAULT 0  NULL   COMMENT '操作者' AFTER `finished_time`,
  ADD COLUMN `is_allocation` INT(1) DEFAULT 1  NULL   COMMENT '0 不分配仓库 1分配仓库' AFTER `finished_user`;


ALTER TABLE `factory_order_operate_record`
  ADD  INDEX `idx_operate_platform_user_id_created_time` (`operate`, `platform`, `user_id`, `created_time`);

  ALTER TABLE `order`
  ADD COLUMN `option_user` INT(20) DEFAULT 0  NOT NULL   COMMENT '操作用户' AFTER `xf_extend_no`;

  ALTER TABLE `factory_order`
  CHANGE `put_in_storage_status` `put_in_storage_status` INT(1) DEFAULT 1  NOT NULL   COMMENT '1未入库 2已入库 3不入库';

  ALTER TABLE `factory_order`
  CHANGE `finished_time` `finished_time` BIGINT(20) DEFAULT 0  NOT NULL   COMMENT '质检完成时间';








