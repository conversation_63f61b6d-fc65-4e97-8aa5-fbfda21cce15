-- ------------------------- zjl begin --------------------------

/*[下午 16:25:20][275 ms]*/ ALTER TABLE `order_sync_history` ADD COLUMN `relative_order_no` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '关联订单号' AFTER `no`;
/*[下午 17:11:59][304 ms]*/ ALTER TABLE `order_sync_history` ADD COLUMN `pdf` TEXT NULL COMMENT '面单' AFTER `relative_order_no`;
/*[下午 17:23:18][247 ms]*/ ALTER TABLE `order_sync_history` ADD COLUMN `relative_out_order_no` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '外部关联订单号' AFTER `no`;
/*[上午 10:29:04][17 ms]*/ INSERT INTO `service_provider` (`name`) VALUES ('讯蜂物流');
/*[上午 10:30:24][23 ms]*/ INSERT INTO `logistic` (`name`, `service_provider_id`) VALUES ('虚拟海外仓（美国）', '8');

/*[上午 10:53:39][0 ms]*/ INSERT INTO `config_area` (`id`, `name`) VALUES ('1', '讯蜂美国');
/*[上午 10:59:10][2 ms]*/ UPDATE `nationality` SET `show_country_detail` = '2' WHERE `id` = '183';

/*[上午 11:03:00][1135 ms]*/ ALTER TABLE `config_address` ADD COLUMN `config_area_id` BIGINT(20) DEFAULT 0 NOT NULL AFTER `flag`;
/*[上午 11:08:13][73 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '1', '1区', '1Q', '1区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:08:36][17 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '2', '2区', '2Q', '2区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:09:00][8 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '3', '3区', '3Q', '3区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:09:12][10 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '4', '4区', '4Q', '4区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:09:21][9 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '5', '5区', '5Q', '5区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:09:32][21 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '6', '6区', '6Q', '6区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:09:41][17 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '7', '7区', '7Q', '7区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:09:53][19 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '8', '8区', '8Q', '8区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 11:10:02][18 ms]*/ INSERT INTO `config_address` (`id`, `area_code`, `name`, `first_letter`, `full_name`, `parent_id`, `area_type`, `registrar_ID`, `registrar_Name`, `create_time`, `update_time`, `flag`, `config_area_id`) VALUES (NULL, '9', '9区', '9Q', '9区', '0', '1', NULL, NULL, '2018-07-20 15:41:12', NULL, '1', '1');
/*[上午 10:24:44][24 ms]*/ UPDATE `config_address` SET `area_code` = '510801' WHERE `id` = '7694';
/*[上午 10:25:40][260 ms]*/ ALTER TABLE `config_address` ADD UNIQUE INDEX `uniq_code` (`area_code`);

/*[下午 14:13:56][414 ms]*/ ALTER TABLE `country_express_info_new` ADD COLUMN `area_type` TINYINT(2) DEFAULT 1 NOT NULL COMMENT '区域范围：0部分 1全部' AFTER `price`;
/*[下午 13:35:05][762 ms]*/ ALTER TABLE `country_express_info_new` ADD COLUMN `prescription_type` TINYINT(2) DEFAULT 1 NOT NULL COMMENT '发货时间类型：1自然日，2工作日' AFTER `prescription_end`;
update country_express_info_new set area_type = 0;
UPDATE country_express_info_new SET area_type = 1 WHERE `name` = "US";
/*[上午 11:05:19][34 ms]*/ ALTER TABLE `country_express_info_area` ADD COLUMN `config_area_id` BIGINT(20) DEFAULT 0 NOT NULL AFTER `logistic_id`;

-- ------------------------- zjl end --------------------------

ALTER TABLE `order` ADD COLUMN `track_info` TEXT NULL COMMENT '物流信息' AFTER `add_warning_date`, ADD COLUMN `track_status` INT(1) DEFAULT 1 NULL COMMENT '1 未获取信息 2发货中 3发货完成 4超出同步时间' AFTER `track_info`;