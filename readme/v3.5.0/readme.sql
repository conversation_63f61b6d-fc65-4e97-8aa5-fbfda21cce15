ALTER TABLE `order`
  ADD COLUMN `shelve_release_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '释放时间' AFTER `product_names`;
ALTER TABLE `order`
  ADD COLUMN `shelve_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '搁置结束时间' AFTER `product_names`,
  ADD COLUMN `shelve_begin_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '搁置开始时间' AFTER `shelve_time`;
ALTER TABLE `order`
  ADD COLUMN `design_finish_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '设计完成时间' AFTER `laber_pdf`;


ALTER TABLE `order`
  ADD COLUMN `confirm_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL factory_orders COMMENT '确认时间' AFTER `product_names`;

  ALTER TABLE `order`
  ADD COLUMN `compensation_status` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赔付状态' AFTER `after_service_open_day`,
  ADD COLUMN `compensation_amount` DECIMAL(10,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赔付金额' AFTER `compensation_status`;

    ALTER TABLE `factory_order`
  ADD COLUMN `compensation_status` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赔付状态' AFTER `new_total_price`,
  ADD COLUMN `compensation_amount` DECIMAL(10,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赔付金额' AFTER `compensation_status`;

  ALTER TABLE `order`
  ADD COLUMN `compensation_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赔付的提交时间' AFTER `compensation_amount`;
ALTER TABLE factory_order
  ADD COLUMN `ship_over_time` BINARY(20) DEFAULT '0' NOT NULL COMMENT '超时的时间毫秒数' AFTER `cancel_time` ;
ALTER TABLE.`order`
  ADD COLUMN `cancel_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '取消时间' AFTER `design_finish_time`;

  ALTER TABLE `factory`
  ADD COLUMN `compensation_status` TINYINT(3) UNSIGNED DEFAULT 2 NOT NULL COMMENT '赔付状态 1开启 2关闭' AFTER `updated_date`;
ALTER TABLE `order`
  ADD COLUMN `factory_order_process_status` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '工厂订单进程状态' AFTER `advance_time`;

  ALTER TABLE `factory_order`
  ADD COLUMN `ship_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `ship_over_time`,
  ADD COLUMN `product_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `ship_time`,
  ADD COLUMN `confirm_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `product_time`;

  CREATE TABLE `factory_order_process_status_history`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `process_status` INT(10) UNSIGNED NOT NULL DEFAULT 0,
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
);

ALTER TABLE `order_item`
  ADD COLUMN `production_cycle_max` INT(10) UNSIGNED DEFAULT 0 NOT NULL AFTER `be_resend_for_lose`;


UPDATE product_supply SET retry_min_product_cycle=CEIL(IFNULL(production_cycle_min,0)/2),retry_max_product_cycle=CEIL(IFNULL(production_cycle_max,0)/2)


ALTER TABLE `factory_order`
  ADD COLUMN `blocking_time` BINARY(20) DEFAULT '0'  NOT NULL   COMMENT '冻结时间' AFTER `be_resend_for_lose`;

  ALTER TABLE `order`
  ADD COLUMN `shelve_user_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '搁置用户' AFTER `factory_order_process_status`;

UPDATE
  `order` o,
  order_item oi,
  `factory_order_operate_record` r SET o.`confirm_time` = r.`created_time`
WHERE o.`status` IN (3)
  AND o.`confirm_time` = 0
  AND oi.`order_id` = o.`id`
  AND r.`no` = oi.`no` AND r.`operate` = 1 ;


INSERT INTO `factory_order_process_status_history` (
  `order_id`,
  `process_status`,
  `created_time`
)
UPDATE `factory_order` fo,`factory_order_operate_record` r SET fo.`confirm_time` = r.`created_time` WHERE fo.`confirm_time` = 0 AND r.`operate` = 1 AND fo.`no` = r.`no`;
UPDATE `factory_order` fo,`factory_order_operate_record` r SET fo.`ship_time` = r.`created_time` WHERE fo.`ship_time` = 0 AND r.`operate` = 4 AND fo.`no` = r.`no` ;

  UPDATE `factory_order` fo,`factory_order_operate_record` r SET fo.`product_time` = r.`created_time` WHERE fo.`product_time` = 0 AND r.`operate` = 4 AND fo.`no` = r.`no`;
  UPDATE `factory_order` fo,`factory_order_operate_record` r SET fo.`ship_time` = r.`created_time` WHERE fo.`ship_time` = 0 AND r.`operate` = 5 AND fo.`no` = r.`no` ;
  SELECT o.id,1,o.`confirm_time` FROM `order` o LEFT JOIN `factory_order_process_status_history` h ON o.`id` = h.`order_id` AND h.`process_status` = 1 WHERE o.`status` = 3 AND h.`id` IS NULL  ;


UPDATE factory_order fo SET fo.`ship_time` = fo.`finished_time` WHERE fo.`finished_time` > 0 AND fo.`ship_time` = 0 ;
INSERT INTO `early_warning_type` (`id`, `name`, `type`, `color`, `del_flag`, `is_delete`, `parent_id`, `remark`) VALUES ('10', '加急', '2', '#4dabf7', '0', '0', '0', '系统人工加急');
UPDATE  factory_order  fo SET ship_over_time = ship_time - UNIX_TIMESTAMP(`out_cycle_date`) * 1000 WHERE  fo.`ship_time` >= fo.`cancel_time` AND fo.`ship_time` > UNIX_TIMESTAMP(`out_cycle_date`) * 1000 AND fo.`status` IN(6,98)


;


UPDATE `order` o SET o.`factory_order_process_status` = 1 WHERE o.`status` = 3 AND o.`factory_order_process_status` = 0 ;

-- ======================================== zjl start ==========================================

ALTER TABLE `after_service_audit` ADD COLUMN `resend_order_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '生成的售后订单ID' AFTER `order_id`, ADD COLUMN `cause_type` TINYINT(2) DEFAULT 0 NOT NULL COMMENT '售后原因 1 质量问题（破损/色差/印刷） 2 服务问题（发错/漏发） 3 物流问题（国内物流揽收段丢包，无揽收信息）' AFTER `type`, ADD COLUMN `duty_affiliation` TINYINT(2) DEFAULT 0 NOT NULL COMMENT '1 平台责任 2 工厂责任' AFTER `cause_type`, ADD COLUMN `country_express_info_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '售后订单物流ID' AFTER `create_user_contact`, ADD COLUMN `address_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '售后订单地址ID' AFTER `country_express_info_id`;
ALTER TABLE `after_service_audit_item` CHANGE `order_item_id` `order_item_id` BIGINT(20) NOT NULL COMMENT '售后订单itemID', ADD COLUMN `resend_order_item_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '生成的售后订单itemID' AFTER `order_item_id`;
ALTER TABLE `order` ADD COLUMN `be_after_service_order` TINYINT(1) DEFAULT 0 NOT NULL COMMENT ' 1 售后订单 0 非售后订单' AFTER `is_advance`;
ALTER TABLE `order` ADD COLUMN `original_as_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '原ID(售后)' AFTER `no`, ADD COLUMN `original_as_no` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '原编码(售后)' AFTER `original_as_id`;
ALTER TABLE `order` ADD COLUMN `duty_affiliation` TINYINT(2) DEFAULT 0 NOT NULL COMMENT '1 平台责任 2 工厂责任' AFTER `be_after_service_order`;
ALTER TABLE `order` ADD COLUMN `after_service_open_day` INT DEFAULT 28 NOT NULL COMMENT '订单售后开启天数' AFTER `duty_affiliation`;
ALTER TABLE `order_item` ADD COLUMN `original_as_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '原itemID(售后)' AFTER `order_id`;
ALTER TABLE `order_item` ADD COLUMN `be_resend_for_lose` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '是否为补件（平台丢件）' AFTER `serial`;
ALTER TABLE `order_item` ADD COLUMN `original_rfl_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '原itemID(补件)' AFTER `original_as_id`;
ALTER TABLE `refund_record` CHANGE `duty_affiliation` `duty_affiliation` TINYINT(2) DEFAULT 0 NOT NULL COMMENT '1 平台责任 2 工厂责任';
ALTER TABLE `factory_order` ADD COLUMN `be_after_service_order` TINYINT(1) DEFAULT 0 NOT NULL COMMENT ' 1 售后订单 0 非售后订单' AFTER `is_allocation`;
ALTER TABLE `factory_order` ADD COLUMN `original_as_no` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '原订单编号' AFTER `no`, ADD COLUMN `merchant_order_original_as_no` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '原商户订单号' AFTER `merchant_order_no`;
ALTER TABLE `factory_order` ADD COLUMN `duty_affiliation` TINYINT(2) DEFAULT 0 NOT NULL COMMENT '1 平台责任 2 工厂责任';
ALTER TABLE `factory_order` ADD COLUMN `refuse_num` INT DEFAULT 0 NOT NULL COMMENT '打回产品数' AFTER `duty_affiliation`, ADD COLUMN `refuse_type` TINYINT(2) DEFAULT 0 NOT NULL COMMENT '1 驳回 2 漏件' AFTER `refuse_num`;

-- ========================================  zjl end  ==========================================



ALTER TABLE `after_service_audit_item`
  ADD  INDEX `idx_order_item_id` (`order_item_id`),
  ADD  INDEX `idx_after_service_audit_id` (`after_service_audit_id`);
