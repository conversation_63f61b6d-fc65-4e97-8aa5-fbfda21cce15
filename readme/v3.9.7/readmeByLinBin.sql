--bin_lin sds.mc.logistics 库下新增表
CREATE TABLE `carriage_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(255) NOT NULL COMMENT '编号',
  `value` text NOT NULL COMMENT '值',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 否 1是',
  `create_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `update_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `carriage_declaration_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) unsigned NOT NULL,
  `merchant_store_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '商户ID',
  `order_id` bigint(20) unsigned NOT NULL,
  `logistics_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '对应的物流渠道',
  `carriage_version` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `weight` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '产品总重量',
  `price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '产品价格',
  `declare_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '申报价格',
  `address_info` text NOT NULL COMMENT '收件地址信息',
  `products_info` text NOT NULL COMMENT '申报产品信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 否 1是',
  `create_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `update_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='申报信息表';

CREATE TABLE `carriage_no_recode` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `merchant_id` bigint(20) unsigned NOT NULL COMMENT '商家id',
  `merchant_store_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '店铺ID',
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单id',
  `logistics_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '对应的物流渠道',
  `declaration_info_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '申报信息id',
  `status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '请求状态 1.待生成 2.生成成功 3.同步运单 4.生成失败',
  `order_status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单状态 1未付款 2待确认 3备货中 4已完成 98取消 99删除',
  `event_no` varchar(100) NOT NULL DEFAULT '' COMMENT '事件编号',
  `carriage_no` varchar(100) NOT NULL DEFAULT '' COMMENT '运单编号',
  `order_no` varchar(100) NOT NULL COMMENT '订单编号',
  `extend_no` varchar(100) NOT NULL DEFAULT '' COMMENT '申请号（自己生成的）',
  `operation_mode` tinyint(2) unsigned NOT NULL DEFAULT '2' COMMENT '1.运营人员手动申请 2.自主调用',
  `country_code` varchar(100) NOT NULL DEFAULT '' COMMENT '收件人国家简码',
  `carriage_version` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `sheet_label_status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '面单状态1.未生成 2.生成成功 3.生成失败',
  `label_url` varchar(255) NOT NULL DEFAULT '' COMMENT '面单地址',
  `track_status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '物流状态 1.创建 2.发货 3.揽收 4.中转 5.签收 6.其他 跟踪超期或无需跟踪',
  `api_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近调用时间',
  `latest_track_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近更新时间',
  `order_finish_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单完成时间',
  `receiving_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '揽收时间',
  `sign_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签收时间',
  `latest_track_info` varchar(1000) NOT NULL DEFAULT '' COMMENT '最后一条时间',
  `is_ignore` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否忽略物流信息',
  `error_info` varchar(1000) NOT NULL DEFAULT '' COMMENT '错误信息记录',
  `extra_info` varchar(1000) NOT NULL DEFAULT '' COMMENT '额外信息字段',
  `is_abandon` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否废弃  0 否 1是',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 否 1是',
  `create_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `update_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx__order_id` (`order_id`),
  KEY `idx__order_no` (`order_no`) USING BTREE,
  KEY `idx__carriage_no` (`carriage_no`) USING BTREE,
  KEY `idx__order_finish` (`order_finish_time`) USING BTREE,
  KEY `idx__api_update_time` (`api_update_time`) USING BTREE,
  KEY `idx__receiving_Time` (`receiving_time`) USING BTREE,
  KEY `idx__sign_time` (`sign_time`) USING BTREE,
  KEY `idx__merchant_store_id` (`merchant_store_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='运单申请记录表';

CREATE TABLE `carriage_request_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `event_no` varchar(100) NOT NULL DEFAULT '' COMMENT '事件编号',
  `event_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '事件类型 1.获取运单 2.获取打印面单 3.获取追踪路径',
  `cost_mses` bigint(20) NOT NULL DEFAULT '0' COMMENT '消耗毫秒数',
  `request_body` text NOT NULL COMMENT '请求信息',
  `response_body` text NOT NULL COMMENT '返回信息',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  `response_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '返回时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 否 1是',
  `create_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `update_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物流跟踪表';

CREATE TABLE `carriage_sheet_label` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `logistics_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '对应的物流渠道',
  `carriage_no_recode_id` bigint(20) unsigned NOT NULL,
  `event_no` varchar(100) NOT NULL DEFAULT '' COMMENT '事件编号',
  `carriage_no` varchar(100) NOT NULL DEFAULT '' COMMENT '运单编号',
  `label_url` varchar(1000) NOT NULL DEFAULT '' COMMENT '运单下载地址',
  `origin_label_url` varchar(1000) NOT NULL DEFAULT '' COMMENT '原运单下载地址',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 否 1是',
  `create_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `update_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='面单记录表';

CREATE TABLE `carriage_track_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL COMMENT '订单id',
  `logistics_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '对应的物流渠道',
  `carriage_no_recode_id` bigint(20) unsigned NOT NULL COMMENT '运单id',
  `event_no` varchar(100) NOT NULL DEFAULT '' COMMENT '事件编号',
  `carriage_no` varchar(100) NOT NULL DEFAULT '' COMMENT '运单编号',
  `track_info` text NOT NULL COMMENT '跟踪信息',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 否 1是',
  `create_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `update_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx__carriage_no_recode_id` (`carriage_no_recode_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物流跟踪表';

INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (1, 'shipper_address', '{\r\n	\"company\": \"ZG\",\r\n	\"name\": \"Zhang\",\r\n	\"telephone\": \"13376998925\",\r\n	\"mobile\": \"13376998925\",\r\n	\"address\": \"NO 52 Zone C Fuzhou Softeware Park FijianSheng china\",\r\n	\"country\": \"CN\",\r\n	\"post_code\": \"350003\",\r\n	\"province\": \"FuJian\",\r\n	\"email\": \"<EMAIL>\",\"city\": \"FuZhou\"\r\n}', 0, 0, 0, '2020-08-06 10:40:24', '2020-08-06 10:40:24');
INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (2, 'jd_token', '{\"expiresIn\":31536000,\"uid\":\"2055491609\",\"tokenCode\":\"yI4a55\",\"code\":0,\"createTime\":1560498874976,\"openId\":\"3ROPb_SSCkpwABhVT_Hjmr3RyOHgPMfX_3rpg1KJh-w\",\"scope\":\"snsapi_base\",\"time\":1560498830642,\"accessToken\":\"32eeeddc60c443188f24a97fe40d142czjlz\",\"tokenType\":\"bearer\",\"outTime\":1623760211056,\"refreshToken\":\"e558b75a11ca488392835353ae8a696fogi3\"}', 0, 0, 0, '2020-08-06 10:41:22', '2020-08-06 10:41:22');
INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (3, 'jd_sender_address', '{\r\n	\"customer_code\":\"020K392336\",\r\n	\"sender_address\":\"福建省福州市仓山区杨周路21号钱隆汇金2号楼103\",\r\n	\"warehouse_code\":\"13376998925\",\r\n	\"sender_name\":\"张建福\",\r\n	\"sender_mobile\":\"13376998925\"\r\n}', 0, 0, 0, '2020-08-06 10:42:17', '2020-08-06 10:42:17');
INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (4, 'receiving_keyword', '已揽收,Posting,已收寄,picked up shipment,Shipment arrived at facility and measured,快件到达作业中心,picked up the shipment,小包收件,Arrived at Sort Facility,Shipment picked up\r\n,Arrived Shipping Partner Facility,已收件,Accepted at USPS Regional Origin Facility,Shipment pick up,Shipment arrived at facility and measured', 0, 0, 0, '2020-09-03 15:59:25', '2020-09-14 11:44:31');
INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (5, 'sign_keyword', '签收,Delivery,successfully delivered,已妥投,DELIVERED,配達完了,快件已派送,delivered,妥投,Delivered', 0, 0, 0, '2020-09-03 15:59:51', '2020-09-11 12:17:13');
INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (6, 'create_no_keyword', 'Shipment information received,Shipping Label Created,订单信息已收到,快件电子信息已经收到,物流订单已创建', 0, 0, 0, '2020-09-10 21:00:09', '2020-09-11 12:17:23');
INSERT INTO `sds_mc_logistics`.`carriage_config`(`id`, `code`, `value`, `is_delete`, `create_uid`, `update_uid`, `create_time`, `update_time`) VALUES (7, 'no_sign_keyword', '终止揽收,退回妥投', 0, 0, 0, '2020-09-16 17:46:40', '2020-09-16 18:17:28');



--旧数据库
ALTER TABLE `logistic`
ADD COLUMN `is_butt_joint` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否是对接物流' AFTER `type`
ADD COLUMN `is_track_info` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1是，0否 是否有物流追踪信息' AFTER `is_butt_joint`;

update `logistic`  set is_butt_joint=1 where id in (1,2,3,4,5,6,7,9,10,11,12,19,20,21,22,23,24,25,26,27,28,66,67,68,69,70,71,72,73,81,82,83,90,92,93,94,95,97,98,99);
update `logistic`  set is_track_info=1 where id in (1,2,3,4,5,6,7,10,11,12,19,20,21,22,23,24,25,26,27,28,66,67,68,69,70,71,72,73,81,82,83,92,93,95,97,98,99);
update `logistic`  set service_provider_id=27 where id=99;


INSERT INTO `service_provider`(`id`, `name`, `status`) VALUES (26, '美邮宝物流', 1);
INSERT INTO `service_provider`(`id`, `name`, `status`) VALUES (27, '云途物流2', 1);
INSERT INTO `logistic`( `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`, `type`, `is_butt_joint`, `is_track_info`, `aliexpress_code`, `warehouse_carrier_service`, `refund_percent`, `refund_reset_num`, `standard_name`, `bulk_on_off`, `bulk_rate`, `issuing_bay_id`, `issuing_bay_area_id`, `method_name`, `all_product_status`, `service_amount`, `fixed_service_amount`, `custom_upload_lable`) VALUES ('美邮宝（美国特快专线）', 26, 0, '测试中~~', 1, 0, 'MYB', '', 0, 2, 1, 2, 1, 1, '', '', 0, 0, 'USPS', 0, 0.00, 1, 1, 'Standard', 1, 0.00, 0.00, 0);
INSERT INTO `logistic`( `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`, `type`, `is_butt_joint`, `is_track_info`, `aliexpress_code`, `warehouse_carrier_service`, `refund_percent`, `refund_reset_num`, `standard_name`, `bulk_on_off`, `bulk_rate`, `issuing_bay_id`, `issuing_bay_area_id`, `method_name`, `all_product_status`, `service_amount`, `fixed_service_amount`, `custom_upload_lable`) VALUES ( '中通快递(fba)', 12, 0, '', 1, 0, 'FBA-PACK', '', 0, 2, 1, 1, 0, 0, '', '', 0, 0, '中通(fba)', 0, 0.00, 1, 1, 'Standard', 1, 18.00, 0.00, 0);
INSERT INTO `logistic`( `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`, `type`, `is_butt_joint`, `is_track_info`, `aliexpress_code`, `warehouse_carrier_service`, `refund_percent`, `refund_reset_num`, `standard_name`, `bulk_on_off`, `bulk_rate`, `issuing_bay_id`, `issuing_bay_area_id`, `method_name`, `all_product_status`, `service_amount`, `fixed_service_amount`, `custom_upload_lable`) VALUES ( '顺丰快递(fba)', 12, 0, '', 1, 0, 'FBA-PACK', '', 0, 2, 1, 1, 0, 0, '', '', 0, 0, '顺丰(fba)', 0, 0.00, 1, 1, 'Standard', 1, 18.00, 0.00, 0);
INSERT INTO `logistic`( `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`, `type`, `is_butt_joint`, `is_track_info`, `aliexpress_code`, `warehouse_carrier_service`, `refund_percent`, `refund_reset_num`, `standard_name`, `bulk_on_off`, `bulk_rate`, `issuing_bay_id`, `issuing_bay_area_id`, `method_name`, `all_product_status`, `service_amount`, `fixed_service_amount`, `custom_upload_lable`) VALUES ( '自提(fba国内)', 12, 0, '', 1, 0, 'D-FBA-ZT', '', 0, 2, 1, 1, 0, 0, '', '', 0, 0, '自提(fba)', 0, 0.00, 1, 1, 'Standard', 1, 20.80, 0.00, 0);

ALTER TABLE `order`
MODIFY COLUMN `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1未付款 2待确认 3备货中 4已完成 5.搁置中 98取消 99删除' AFTER `design_status`;

ALTER TABLE `order`
ADD COLUMN `carriage_version` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '版本号' AFTER `carriage_no`,
ADD COLUMN `carriage_no_create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0)  COMMENT '运单号创建时间' AFTER `carriage_version`,
ADD COLUMN `carriage_track_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '物流状态 1.创建 2.发货 3.揽收 4.中转 5.签收 6.其他 跟踪超期或无需跟踪' AFTER `carriage_no_create_time` ,
ADD COLUMN `carriage_latest_track_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '最近更新时间' AFTER `carriage_track_status`,


