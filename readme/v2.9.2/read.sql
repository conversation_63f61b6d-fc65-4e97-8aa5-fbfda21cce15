CREATE TABLE `aliexpress_address`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `mobile` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '电话',
  `province` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '省份',
  `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '姓名',
  `county` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '区',
  `email` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '邮件',
  `city` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '城市',
  `country` VARCHAR(8) NOT NULL DEFAULT '' COMMENT '国家',
  `address_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '地址',
  `postcode` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '邮件',
  `member_type` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '类型',
  `street` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '街道',
  `fax` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '传真',
  `phone` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '电话',
  `street_address` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  PRIMARY KEY (`id`)
);

ALTER TABLE `aliexpress_address`
  ADD  INDEX `index-address-id` (`address_id`);

  ALTER TABLE `merchant_store`
  ADD COLUMN `aliexpress_sender_address_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '速卖通发货地址' AFTER `update_time`,
  ADD COLUMN `aliexpress_refund_address_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '速卖通退款地址' AFTER `aliexpress_sender_address_id`,
  ADD COLUMN `send_fail_handle` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '速卖通发货失败的类型RETURN 退回 DESTROY 销毁' AFTER `aliexpress_refund_address_id`;

  ALTER TABLE `merchant_store` CHANGE `tracking_number_sync_mode` `tracking_number_sync_mode` TINYINT(5) DEFAULT 0 NOT NULL COMMENT '同步运单号方式: 1 运单号生成后立即同步运单号 2 运单号生成后第二天当地时间 3 产品发出时同步运单号4物流有跟踪数据后同步运单号';


ALTER TABLE `logistic`
  ADD COLUMN `aliexpress_code` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '对应的速卖通code' AFTER `be_common`,
  ADD COLUMN `warehouse_carrier_service` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '对应的速卖通warehouse_carrier_service' AFTER `aliexpress_code`;



ALTER TABLE `order_item`
  ADD COLUMN `out_product_id` VARCHAR(64) DEFAULT '' NOT NULL COMMENT 'out_product_id' AFTER `out_order_item_id`;

-- ==================================== zjl start ===========================================
ALTER TABLE `order_import_record_order`
ADD COLUMN `client_selected_logistics` VARCHAR(100) DEFAULT '' NOT NULL COMMENT '客户指定物流' AFTER `out_platform_orders_time`,
ADD COLUMN `remark` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '订单备注' AFTER `client_selected_logistics`;

/*[上午 10:24:37][3631 ms]*/ ALTER TABLE `logistic` ADD COLUMN `be_common` TINYINT(1) DEFAULT 1 NOT NULL COMMENT '是否是自家物流' AFTER `express_status`;

/*[上午 10:27:29][26 ms]*/ INSERT INTO `service_provider` (`id`, `name`, `status`) VALUES (NULL, '速卖通物流', '1');
/*[上午 10:29:48][34 ms]*/ INSERT INTO `logistic` (`id`, `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`) VALUES (NULL, '无忧标准', '9', '0', '在线物流,仅供速卖通使用', '2', '2.5', '', '', '0', '2', '0');
/*[上午 10:30:05][33 ms]*/ INSERT INTO `logistic` (`id`, `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`) VALUES (NULL, '无忧简易', '9', '0', '在线物流,仅供速卖通使用', '2', '2.5', '', '', '0', '2', '0');
/*[上午 10:30:14][26 ms]*/ INSERT INTO `logistic` (`id`, `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`) VALUES (NULL, '无忧优先', '9', '0', '在线物流,仅供速卖通使用', '2', '2.5', '', '', '0', '2', '0');
/*[上午 10:30:31][32 ms]*/ INSERT INTO `logistic` (`id`, `name`, `service_provider_id`, `discount`, `remark`, `status`, `packing_charges`, `code_id`, `algorithm_type`, `min_add_weight`, `express_status`, `be_common`) VALUES (NULL, '无忧集运', '9', '0', '在线物流,仅供速卖通使用', '2', '2.5', '', '', '0', '2', '0');

-- insert country_express_info_new about sell fast logistics
INSERT INTO country_express_info_new (`name`, limit_priority, prescription_start, prescription_end, prescription_type, `status`, logistics_id, price)
(SELECT n.abbreviation, -1, 1, 7, 1, 1, l.id, '[{"weight":0,"price":0.0,"handle_price":0.0,"rate":1.0}]' FROM logistic l, nationality n WHERE l.service_provider_id = 9 ORDER BY l.id)
-- ====================================  zjl end  ===========================================
  ALTER TABLE `order` ADD COLUMN `track_info` TEXT NULL COMMENT '物流信息' AFTER `add_warning_date`, ADD COLUMN `track_status` INT(1) DEFAULT 1 NULL COMMENT '1 未获取信息 2发货中 3发货完成 4超出同步时间' AFTER `track_info`;
