ALTER TABLE `merchant`
  ADD COLUMN `last_by_goods_time` BIGINT(20) DEFAULT 0  NOT NULL   COMMENT '最后购买套餐时间' AFTER `free_gold`;
/*初始化没有购买会员记录确实年会员的最后购买时间，初始化时间为开始时间20190708*/
UPDATE merchant SET last_by_goods_time = 1562515200000 WHERE  id IN (1,5,12,13,22,23,24,26,28,30,31,32,33,34,51,52,56,75,76,77,78,79,80,81,93,107,110,121,134,220,232,236,258,262,270,279,285,298,330,334,366,371,373,388,389,399,401,419,423,433,449,460,465,479,480);

  UPDATE payment SET pay_time = created_time WHERE id IN
  (SELECT
    payment_id
  FROM
    platform_good_order
  WHERE platform_goods_id IN (2, 3)
    AND `status` = 2)
  AND pay_time = 0;

  UPDATE `merchant` AS mb,(SELECT
  pgo.*,
  p.`pay_time`
FROM
  platform_good_order AS pgo
  INNER JOIN payment AS p
    ON pgo.`payment_id` = p.`id`
WHERE pgo.`id` IN
  (SELECT
    MAX(id) AS pid
  FROM
    platform_good_order
  WHERE platform_goods_id IN (2, 3)
    AND `status` = 2
  GROUP BY merchant_id)) AS tm
SET mb.`last_by_goods_time` = tm.pay_time
WHERE mb.`id`=tm.merchant_id;

-- ========================zjl`s sql start===========================

-- tables
drop table if exists raw_material;

drop index unique on raw_material_category;

drop table if exists raw_material_category;

drop table if exists raw_material_item;

drop index uniq on raw_material_product_relation;

drop table if exists raw_material_product_relation;

drop table if exists raw_material_stock_record;

drop table if exists raw_material_stock_record_item;

/*==============================================================*/
/* Table: raw_material                                          */
/*==============================================================*/
create table raw_material
(
   id                   bigint(20) not null auto_increment,
   factory_id           bigint(20) not null,
   raw_material_category_id bigint(20) not null comment '原料分类ID',
   name                 varchar(100) not null comment '原料名称',
   be_delete            tinyint(1) not null default 0,
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   update_time          timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table raw_material comment '原料';

/*==============================================================*/
/* Table: raw_material_category                                 */
/*==============================================================*/
create table raw_material_category
(
   id                   bigint(20) not null auto_increment,
   factory_id           bigint(20) not null,
   parent_id            bigint(20) not null default 0,
   name                 varchar(15) not null,
   sort                 int not null default 1,
   be_default           tinyint(1) not null default 0,
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table raw_material_category comment '原料类目';

/*==============================================================*/
/* Index: unique                                                */
/*==============================================================*/
create unique index unique on raw_material_category
(
   factory_id,
   name
);

/*==============================================================*/
/* Table: raw_material_item                                     */
/*==============================================================*/
create table raw_material_item
(
   id                   bigint(20) not null auto_increment,
   factory_id           bigint(20) not null,
   raw_material_id      bigint(20) not null,
   code                 varchar(20) not null comment '编号',
   specification        varchar(255) not null comment '规格',
   warning_limit        double(14,2) not null default 0 comment '预警库存  为0不预警',
   stock                double(14,2) not null default -1 comment '库存 -1为不限',
   status               tinyint(2) not null default 1 comment '0 下架 1 上架',
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   update_time          timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

/*==============================================================*/
/* Table: raw_material_product_relation                         */
/*==============================================================*/
create table raw_material_product_relation
(
   id                   bigint(20) not null auto_increment,
   factory_id           bigint(20) not null,
   raw_material_id      bigint(20) not null comment '原料ID',
   raw_material_item_id bigint(20) not null comment '原料项ID',
   product_id           bigint(20) not null comment '产品ID',
   consume              double(14,2) not null comment '用量',
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table raw_material_product_relation comment '生产原料管理';

/*==============================================================*/
/* Index: uniq                                                  */
/*==============================================================*/
create unique index uniq on raw_material_product_relation
(
   raw_material_id,
   raw_material_item_id,
   product_id
);

/*==============================================================*/
/* Table: raw_material_stock_record                             */
/*==============================================================*/
create table raw_material_stock_record
(
   id                   bigint(20) not null auto_increment,
   factory_id           bigint(20) not null,
   factory_sys_user_id  bigint(20) not null comment '入库员ID 0为系统操作',
   batch_num            int not null comment '批次号',
   factory_order_no     varchar(64) not null default '' comment '工厂订单号',
   original_total       double(14,2) not null comment '原总数量',
   total                double(14,2) not null comment '总数量',
   type                 tinyint(2) not null comment '出入库类型: 0 初始化 1 入库(加) 2 出库(减) 3 盘点(修改)',
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table raw_material_stock_record comment '原料出入库记录';

/*==============================================================*/
/* Table: raw_material_stock_record_item                        */
/*==============================================================*/
create table raw_material_stock_record_item
(
   id                   bigint(20) not null auto_increment,
   raw_material_stock_record_id bigint(20) not null,
   raw_material_id      bigint(20) not null,
   raw_material_item_id bigint(20) not null,
   original_num         double(14,2) not null comment '原数量',
   num                  double(14,2) not null comment '数量',
   type                 tinyint(2) not null comment '出入库类型: 0 初始化 1 入库(加) 2 出库(减) 3 盘点(修改)',
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table raw_material_stock_record_item comment '原料出入库明细';


-- raw_material_category data init
INSERT INTO raw_material_category (factory_id, `name`, be_default) SELECT id, '默认分类', 1 FROM factory u;

-- menu init 注意204被占用则需要手动处理
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (204, '1', '0,1,', '库存管理', '50', '', NULL, 'table', '1', '', '2013-05-27 08:00:00', NULL, '0', '1');
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '204', '0,1,204,', '原料分类', '10', '/stock-manage/material-category', NULL, '', '1', '', '2019-02-24 09:39:43', NULL, '0', '1');
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '204', '0,1,204,', '原料库存', '20', '/stock-manage/material-stock', NULL, '', '1', '', '2019-02-24 09:39:43', NULL, '0', '1');
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '204', '0,1,204,', '生产原料管理', '30', '/stock-manage/product-material', NULL, '', '1', '', '2019-02-24 09:39:43', NULL, '0', '1');
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '204', '0,1,204,', '入库单', '40', '/stock-manage/stock-in', NULL, '', '1', '', '2019-02-24 09:39:43', NULL, '0', '1');
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '204', '0,1,204,', '出库单', '50', '/stock-manage/stock-out', NULL, '', '1', '', '2019-02-24 09:39:43', NULL, '0', '1');
INSERT INTO `factory_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '204', '0,1,204,', '盘点单', '60', '/stock-manage/stock-check', NULL, '', '1', '', '2019-02-24 09:39:43', NULL, '0', '1');

INSERT INTO factory_sys_role_menu (role_id, menu_id) SELECT r.id,m.id FROM factory_sys_role r,factory_sys_menu m WHERE (m.id = 204 OR m.parent_id = 204 ) AND r.role_type IS NULL;

-- ======================== zjl`s sql end ===========================


/* Alter table in target */
ALTER TABLE `export_history`
	CHANGE `site` `site` varchar(20)  COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' after `barcode_gen_type` ,
	ADD COLUMN `independent_domain_id` bigint(20) unsigned   NOT NULL DEFAULT 0 COMMENT '独立域名id' after `watermark_id` ,
	ADD COLUMN `main_img_add_watermark` tinyint(3) unsigned   NOT NULL DEFAULT 0 COMMENT '主图是否添加水印 1是 0否' after `independent_domain_id` ,
	ADD KEY `index-user-shop`(`user_id`,`shop_id`) ;

/* Create table in target */
CREATE TABLE `independent_domain`(
	`id` bigint(20) unsigned NOT NULL  auto_increment ,
	`domain` varchar(256) COLLATE utf8mb4_general_ci NOT NULL  DEFAULT '' COMMENT '域名' ,
	`created_time` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '创建时间' ,
	`user_id` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '用户' ,
	`merchant_id` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '商户' ,
	`update_time` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '更新时间' ,
	`handle_status` varchar(16) COLLATE utf8mb4_general_ci NOT NULL  DEFAULT '' COMMENT 'SUCCESS 成功 WAIT_HANDLE 待技术处理  FAILED  失败 WAIT_ANALYSIS 待解析' ,
	`failed_reason` varchar(100) COLLATE utf8mb4_general_ci NOT NULL  DEFAULT '' COMMENT '失败原因' ,
	`analysis_content` varchar(128) COLLATE utf8mb4_general_ci NOT NULL  DEFAULT '' COMMENT '解析内容' ,
	`delete_reason` varchar(128) COLLATE utf8mb4_general_ci NOT NULL  DEFAULT '' COMMENT '删除原因' ,
	`status` tinyint(3) unsigned NOT NULL  DEFAULT 0 COMMENT '状态' ,
	`relate_id` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT 'relate' ,
	PRIMARY KEY (`id`) ,
	KEY `index—merchant`(`merchant_id`) ,
	KEY `index-domain`(`domain`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_general_ci';


/* Create table in target */
CREATE TABLE `independent_domain_user`(
	`id` bigint(20) unsigned NOT NULL  auto_increment ,
	`domain_id` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '域名id' ,
	`user_id` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '用户id' ,
	`created_time` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '创建时间' ,
	`created_user_id` bigint(20) unsigned NOT NULL  DEFAULT 0 COMMENT '用户id' ,
	`status` tinyint(3) unsigned NOT NULL  DEFAULT 0 COMMENT '状态' ,
	PRIMARY KEY (`id`) ,
	KEY `index-domain`(`domain_id`) ,
	KEY `index-user`(`user_id`,`domain_id`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_general_ci';

ALTER TABLE `merchant_sys_menu`
	ADD COLUMN `permission_type` tinyint(3) unsigned   NOT NULL DEFAULT 0 COMMENT '部门权限，基本上0或者4(不需要或全部)' after `manage_flag` ;

	ALTER TABLE `notification`
	ADD COLUMN `relate_id` bigint(20) unsigned   NOT NULL DEFAULT 0 COMMENT '关联id' after `user_id` ;


ALTER TABLE `payment`
  ADD COLUMN `merchant_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '商户id' AFTER `refund_total_amount`;

  ALTER TABLE `merchant`
  ADD COLUMN `consume_total` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '消费总额' AFTER `last_by_goods_time`;
ALTER TABLE `payment`
  ADD COLUMN `add_consume` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否添加到商户年度消费' AFTER `merchant_id`;
ALTER TABLE `merchant`
  CHANGE `consume_total` `consume_total` DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '消费总额';
ALTER TABLE `refund_record`
  ADD COLUMN `add_consume` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否添加到商户年度消费' AFTER `merchant_id`;

-----zx-----
CREATE TABLE `order_early_warning` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `order_item_id` int(20) NOT NULL DEFAULT '0',
  `early_warning_id` int(20) NOT NULL,
  `content` varchar(255) DEFAULT '',
  `type` int(1) NOT NULL DEFAULT '2',
  `out_type` int(2) NOT NULL DEFAULT '1',
  `update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `out_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37447 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `early_warning_type` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` int(1) NOT NULL DEFAULT '2',
  `color` varchar(255) NOT NULL,
  `del_flag` int(1) NOT NULL DEFAULT '0',
  `is_delete` int(1) NOT NULL DEFAULT '1' COMMENT '0不能删除 1能删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4;


UPDATE payment p,`order` o  SET p.`merchant_id` = o.`merchant_id` WHERE p.`id` = o.`payment_id` ;
ALTER TABLE `refund_record`
  CHANGE `total_price` `total_price` DECIMAL(10,2) NULL COMMENT '商品金额',
  CHANGE `carriage_price` `carriage_price` DECIMAL(10,2) NULL COMMENT '运费',
  CHANGE `refund_price` `refund_price` DECIMAL(10,2) NULL COMMENT '退款金额';

UPDATE payment p,`platform_good_order` o  SET p.`merchant_id` = o.`merchant_id` WHERE p.`id` = o.`payment_id` ;
ALTER TABLE `platform_goods` ADD COLUMN `sort` INT(1) DEFAULT 0 NOT NULL COMMENT '1普通' AFTER `update_time`, ADD COLUMN `limit_level` INT(1) DEFAULT 1 NOT NULL AFTER `sort`;



UPDATE
  merchant m,
  (SELECT
    t.`merchant_id` merchant_id,
    SUM(t.`total_amount` - t.`free_gold`) amount
  FROM
    `payment` t
  WHERE `status` = 2
    AND pay_type != 3
  GROUP BY merchant_id) tmp
SET
  m.`consume_total` = tmp.amount
WHERE m.id = tmp.merchant_id;


SELECT
  tmp.*,
  m.`consume_total`
FROM
  merchant m,
  (SELECT
    t.`merchant_id` merchant_id,
    SUM(t.`total_amount` - t.`free_gold`) amount
  FROM
    `payment` t
  WHERE `status` = 2
    AND pay_type != 3
  GROUP BY merchant_id) tmp
WHERE m.id = tmp.merchant_id;

UPDATE
  `payment` t
SET
  add_consume = 1
WHERE `status` = 2
  AND pay_type != 3
  AND merchant_id > 0;
UPDATE
  merchant m,
  (SELECT
    t.`merchant_id` merchant_id,
    SUM(t.`refund_price` - t.`free_gold`) amount
  FROM
    `refund_record` t
  WHERE `status` = 2

  GROUP BY merchant_id) tmp
SET
  m.`consume_total` =  m.`consume_total` - tmp.amount
WHERE m.id = tmp.merchant_id;


SELECT
  tmp.*,
  m.`consume_total`
FROM
  merchant m,
  (SELECT
    t.`merchant_id` merchant_id,
    SUM(t.`refund_price` - t.`free_gold`) amount
  FROM
    `refund_record` t
  WHERE `status` = 2
  GROUP BY merchant_id) tmp
WHERE m.id = tmp.merchant_id;

UPDATE
  `refund_record` t
SET
  add_consume = 1
WHERE `status` = 2
  AND merchant_id > 0;
INSERT INTO `early_warning_type` (`id`, `name`, `color`, `is_delete`) VALUES ('2', '加急', '#4dabf7', '0');

INSERT INTO `early_warning_type` (`id`, `name`, `type`, `color`, `is_delete`) VALUES ('1', '超期', '1', '#ff0000', '0');


ALTER TABLE `independent_domain`
  ADD COLUMN `search_user_id` VARCHAR(1024) DEFAULT '' NOT NULL AFTER `relate_id`;


INSERT INTO sys_dict(`value`,label,`type`,description,sort,create_date,update_date)VALUES('domain_name_service','独立域名','merchantPermissionItem','商户权限项code',22,NOW(),NOW());

ALTER `factory_order` ADD COLUMN `warning_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '订单预警时间' AFTER `out_confirm_date`;

ALTER TABLE `order` ADD COLUMN `warning_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '根据预警排序时间' AFTER `max_can_use_gold`, ADD COLUMN `is_top` INT(1) DEFAULT 0 NOT NULL COMMENT '0 不置顶 1置顶' AFTER `warning_date`;

ALTER TABLE `order_item` ADD COLUMN `factory_order_status` INT(2) DEFAULT 1 NOT NULL COMMENT '98取消 1未完成 6已完成' AFTER `max_can_use_gold`;

ALTER TABLE `order` ADD COLUMN `out_date` TIMESTAMP NOT NULL COMMENT '最近触发的超期时间' AFTER `max_can_use_gold`, ADD COLUMN `add_warning_date` TIMESTAMP NOT NULL COMMENT '最近添加的预警时间' AFTER `out_date`;


UPDATE `order` SET out_date=4094945698000 WHERE `status` IN (4,98,99);

ALTER TABLE `factory_order` ADD COLUMN `out_date` BIGINT(50) NULL COMMENT '最近超期时间' AFTER `out_confirm_date`, ADD COLUMN `add_warning_date` BIGINT(50) NULL COMMENT '最近添加超期时间' AFTER `out_date`;

ALTER TABLE `factory_order` CHANGE `update_time` `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL;

