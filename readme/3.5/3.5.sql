ALTER TABLE `product`
  ADD COLUMN `production_cycle_min` INT(10) DEFAULT 0 NOT NULL COMMENT '最小发货周期' AFTER `order_num`,
  ADD COLUMN `production_cycle_max` INT(10) DEFAULT 0 NOT NULL COMMENT '最大发货周期' AFTER `production_cycle_min`;



ALTER TABLE `order`
  ADD COLUMN `is_advance` INT(1) DEFAULT 1  NOT NULL   COMMENT '1 2 预上网' AFTER `corp_billid`;


ALTER TABLE  `product_supply`
  ADD COLUMN `retry_min_product_cycle` INT(1) DEFAULT 0  NOT NULL   COMMENT '最小重发货周期小' AFTER `holiday_foreshow_day`,
  ADD COLUMN `retry_max_product_cycle` INT(1) DEFAULT 0  NOT NULL   COMMENT '最大重发货周期小' AFTER `retry_min_product_cycle`;


  ALTER TABLE `factory_order`
  ADD COLUMN `new_total_price` DECIMAL(20,2) DEFAULT 0.00  NOT NULL   COMMENT '可结算金额' AFTER `duty_affiliation`;


ALTER TABLE `factory_order`
  ADD COLUMN `express_time` BIGINT(20) DEFAULT 0  NOT NULL   COMMENT '发货时间' AFTER `new_total_price`;


ALTER TABLE .`factory_order`
  ADD COLUMN `cancel_time` BIGINT(20) UNSIGNED DEFAULT 0  NOT NULL   COMMENT '取消时间' AFTER `compensation_amount`;



UPDATE product p LEFT JOIN (SELECT product_id id,MIN(production_cycle_min) production_cycle_min,MAX(production_cycle_max) production_cycle_max FROM product_supply GROUP BY product_id) ps ON p.id=ps.id SET p.production_cycle_min=IFNULL(ps.production_cycle_min,0),p.production_cycle_max=IFNULL(ps.`production_cycle_max`,0)

UPDATE factory_order SET new_total_price =(total_price-compensation_amount)

ALTER TABLE `factory_order`
  ADD COLUMN `blocking_time` BINARY(20) DEFAULT '0'  NOT NULL   COMMENT '冻结时间' AFTER `after_service_time`;


ALTER TABLE `factory_order`
  DROP COLUMN `blocking_time`;


  ALTER TABLE `after_service_audit`
  ADD COLUMN `is_advance` INT(1) DEFAULT 1  NULL   COMMENT '预上网' AFTER `update_time`;


ALTER TABLE `order`
  ADD COLUMN `express_cost` DECIMAL DEFAULT 0.00  NOT NULL   COMMENT '物流成本' AFTER `shelve_user_id`;



ALTER TABLE `order`
  ADD COLUMN `express_income` DECIMAL DEFAULT 0.00  NOT NULL   COMMENT '物流收入' AFTER `express_cost`;

UPDATE `order` SET express_cost =carriage_amount ,express_income=carriage_amount

UPDATE `order` o  SET express_income =0 ,express_cost=0  WHERE STATUS=98

UPDATE `order` SET express_income =0 WHERE be_after_service_order=1

UPDATE `order` o LEFT JOIN after_service_audit asa  ON o.id= asa.order_id SET express_income  =0 WHERE asa.type=2