ALTER TABLE `material`
  ADD COLUMN `sync_es_date` DATETIME DEFAULT '2000-01-01 00:00:00' NOT NULL COMMENT '同步到es的时间' AFTER `create_date`;


ALTER TABLE `material`
  ADD COLUMN `online_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '上架时间' AFTER `length`;
UPDATE `material` m SET m.`online_status` = 2 WHERE m.`owner_id` = m.`user_id` AND online_status = 3 ;
UPDATE `material` m SET m.`online_time` = UNIX_TIMESTAMP(m.`create_date`) * 1000;
INSERT INTO `merchant_sys_menu` (parent_id,parent_ids,`name`,sort,permission,create_date,is_show) VALUES (165,"0,1,189,165,","查看所有人的素材",4000,"material:view:all",NOW(),0);
ALTER TABLE `sds_saas_test`.`material`
  ADD COLUMN `auto_keyword` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否提取过素材1是0否' AFTER `online_time`;

------郑响-------
CREATE TABLE `keyword` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `mkeyword` varchar(124) NOT NULL,
  `material_id` int(20) NOT NULL,
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '1识别类型 2添加类型',
  `delflag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1删除 0未删除',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0未选中 1选中',
  PRIMARY KEY (`id`),
  UNIQUE KEY `mkeyword_2` (`mkeyword`,`material_id`)
)

ALTER TABLE `order`
  ADD COLUMN `carriage_balance` DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '物流余额' AFTER `usable_free_gold`,
  ADD COLUMN `carriage_free_gold` DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '物流赠送金' AFTER `carriage_balance`;

  ALTER TABLE `order_item`
  ADD COLUMN `used_balance` DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '产品的可用余额' AFTER `compound_id`,
  ADD COLUMN `used_free_gold` DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '产品的赠送金' AFTER `used_balance`;


ALTER TABLE `order` ADD COLUMN `extend_no` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '扩张订单号(这里针对云途的,如果其他物流以后也可以用)' AFTER `carriage_free_gold`;
UPDATE `order` SET extend_no = `no`;
ALTER TABLE `sds_saas_test`.`carriage_no_recode`
  ADD COLUMN `order_no` VARCHAR(100) DEFAULT '' NOT NULL COMMENT '订单id' AFTER `create_time`;