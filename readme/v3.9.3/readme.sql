
//新品 初始化
UPDATE product p set p.new_status  = 2 ;
UPDATE product p set p.new_status = 1 WHERE p.parent_id = 0 ;


UPDATE `sds_test`.`platform_permission` SET `unit` = '普通/新品/精品/促销' WHERE `id` = '89';
UPDATE `sds_test`.`platform_permission` SET `unit` = '普通/新品/精品/促销' WHERE `id` = '88';
UPDATE `sds_test`.`platform_permission` SET `unit` = '普通/促销' WHERE `id` = '87';



ALTER TABLE `sds_test`.`order` ADD COLUMN `accomplish_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '订单全部质检完成时间' AFTER `prepaid_no`, ADD COLUMN `upload_laber_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '用户上传面单的时间' AFTER `accomplish_time`;



ALTER TABLE `sds_test`.`excel_template`
  ADD COLUMN `sku_parent` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '母体sku规则' AFTER `feed_product_type`,
  ADD COLUMN `sku_child` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '变体sku规则' AFTER `sku_parent`,
  ADD COLUMN `sku_simple` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '简单商品sku规则' AFTER `sku_child`,
  ADD COLUMN `title_auto_split` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '变体分割1是 0否' AFTER `sku_simple`;
ALTER TABLE `sds_test`.`order`
  ADD COLUMN `compensation_finish_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赔付的完成时间' AFTER `upload_laber_time`;
UPDATE product p set p.new_status = 1 WHERE p.parent_id = 0 ;



CREATE TABLE `excel_template_office` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `excel_template_id` bigint(20) NOT NULL COMMENT '素材编号',
  `office_id` bigint(20) NOT NULL COMMENT '部门编号',
  `show_type` int(1) NOT NULL DEFAULT '1' COMMENT '展示类型1全公司2部门',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_delete` int(1) NOT NULL DEFAULT '0' COMMENT '删除标记1删除0正常',
  PRIMARY KEY (`id`),
  UNIQUE KEY `material_id` (`excel_template_id`,`office_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `aliexpress_template_office` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `aliexpress_template_id` bigint(20) NOT NULL COMMENT '素材编号',
  `office_id` bigint(20) NOT NULL COMMENT '部门编号',
  `show_type` int(1) NOT NULL DEFAULT '1' COMMENT '展示类型1全公司2部门',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `is_delete` int(1) NOT NULL DEFAULT '0' COMMENT '删除标记1删除0正常',
  PRIMARY KEY (`id`),
  UNIQUE KEY `template_id` (`aliexpress_template_id`,`office_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE `sds_mc_publish`.`aliexpress_template_office`
  ADD COLUMN `create_uid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `is_delete`,
  ADD COLUMN `update_uid` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `create_uid`,
  ADD COLUMN `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL AFTER `update_uid`;



ALTER TABLE `sds_mc_publish`.`aliexpress_template`
  ADD COLUMN `merchant_id` BIGINT(20) UNSIGNED DEFAULT  NOT NULL COMMENT '商户id' AFTER `merchant_store_id`;
