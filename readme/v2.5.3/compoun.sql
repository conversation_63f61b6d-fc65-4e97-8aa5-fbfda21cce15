CREATE TABLE `sds_saas_dev`.`compound_history`(
  `id` BIGINT(20) UNSIGNED NOT NULL COMMENT 'id',
  `merchant_product_parent_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'merchant_product_parent_id',
  `product_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '母体id',
  `combine_rule` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '合成规则',
  `combine_rule_type` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合成规则类型',
  `prototypes` TEXT NOT NULL COMMENT '合成内容',
  `user_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`)
);


<PERSON>TER TABLE `task_child`
  ADD COLUMN `compound_history_id` BIGINT(20) UNSIGNED NOT NULL COMMENT '添加历史id' AFTER `group_id`
 ;

  CREATE TABLE `merchant_product_parent_combine_rule`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `merchant_product_parent_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联id',
  `user_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `combine_rule` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '组合规则',
  `combine_rule_type` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组合规则',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`)
);

ALTER TABLE `merchant_product_parent_combine_rule`
  ADD  UNIQUE INDEX `index-uni` (`user_id`, `merchant_product_parent_id`, `combine_rule`);

