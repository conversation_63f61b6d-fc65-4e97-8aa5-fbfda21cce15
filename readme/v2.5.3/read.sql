ALTER TABLE `sds_saas_dev`.`barcode_statistics`
  CHANGE `type` `type` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '1upc,2ean',
  CHANGE `stock` `stock` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '库存数';

  ALTER TABLE `sds_saas_dev`.`barcode`
  CHANGE `type` `type` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '1ean2upc',
  CHANGE `number` `number` VARCHAR(32) CHARSET latin1 COLLATE latin1_swedish_ci DEFAULT '' NOT NULL COMMENT 'ean_upc_code',
  CHANGE `status` `status` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '1不使用2使用',
  CHANGE `import_id` `import_id` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '概览统计id';


  ALTER TABLE `sds_saas_dev`.`import_barcode`
  CHANGE `type` `type` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '1ean2upc',
  CHANGE `qty` `qty` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '库存数';

  ALTER TABLE `sds_saas_dev`.`barcode`
  CHANGE `import_id` `import_id` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '导入id';

  ALTER TABLE `sds_saas_dev`.`import_barcode`
  ADD COLUMN `used_qty` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '使用数' AFTER `qty`,
  ADD COLUMN `makers_code` VARCHAR(10) DEFAULT '' NOT NULL COMMENT '产商代码' AFTER `used_qty`,
  ADD COLUMN `status` INT(4) DEFAULT 1 NOT NULL COMMENT '1可用2失效' AFTER `makers_code`,
  ADD COLUMN `merchant_id` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '商户id' AFTER `status`,
  ADD COLUMN `generation_type` TINYINT(4) UNSIGNED DEFAULT 2 NOT NULL COMMENT '生成类型1生成2導入' AFTER `merchant_id`;

  ALTER TABLE `sds_saas_dev`.`barcode`
  ADD COLUMN `country_code` VARCHAR(6) DEFAULT '' NOT NULL COMMENT '国家码/旗码' AFTER `search_times`,
  ADD COLUMN `makers_code` VARCHAR(10) DEFAULT '' NOT NULL COMMENT '产商代码' AFTER `country_code`,
  ADD COLUMN `product_code` bigint(20) DEFAULT 0 NOT NULL COMMENT '商品代码' AFTER `makers_code`,
  ADD COLUMN `generation_type` TINYINT(4) DEFAULT 2 NOT NULL COMMENT '1生成2导入' AFTER `product_code`;



  ALTER TABLE `sds_saas_dev`.`prototype` ADD COLUMN `design_scope` TINYINT(1) DEFAULT 1 NOT NULL COMMENT '0半 1全' AFTER `name`, ADD COLUMN `del_flag` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '0未删除1删除' AFTER `design_scope`;

 ALTER TABLE `sds_saas_dev`.`product` ADD COLUMN `color_name` VARCHAR(20) NOT NULL COMMENT '颜色名称' AFTER `color_sort`;
 ALTER TABLE `sds_saas_dev`.`prototype` CHANGE `detail_img_urls` `detail_img_urls` TEXT CHARSET utf8 COLLATE utf8_general_ci NULL;
  ADD COLUMN `status` INT(4) DEFAULT 1 NOT NULL COMMENT '1可用2失效' AFTER `makers_code`;

  ALTER TABLE `sds_saas_test`.`merchant`
  ADD COLUMN `is_insider` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '1是外部人员2是内部人员' AFTER `account_type`;


  ALTER TABLE `sds_saas_dev`.`barcode_statistics`
  ADD COLUMN `generation_type` TINYINT(4) UNSIGNED DEFAULT 2 NOT NULL COMMENT '1系统2导入' AFTER `stock`;




ALTER TABLE `compoun_group`
  ADD COLUMN `combine_rule` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '合成规则' AFTER `merchant_id`,
  ADD COLUMN `combine_rule_type` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '合成规则类型' AFTER `combine_rule`;

CREATE TABLE `sds_saas_dev`.`barcode_recode` (
  `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
  `country_code` varchar(20) NOT NULL DEFAULT '' COMMENT '国家码/旗码',
  `makers_code` varchar(20) NOT NULL DEFAULT '' COMMENT '产商代码',
  `status` int(4) unsigned NOT NULL DEFAULT '2' COMMENT '1不能使用2可以使用',
  `type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '1ena/2upc',
  `created_time` bigint(20) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4

ALTER TABLE `sds_saas_dev`.`barcode_statistics`
  DROP INDEX `index_user_type`,
  ADD  UNIQUE INDEX `index_user_type` (`user_id`, `type`, `generation_type`);



drop table if exists currency_transform_standard;

drop index name_uniq on pricing_template;

drop table if exists pricing_template;

drop table if exists pricing_template_product_item;

/*==============================================================*/
/* Table: currency_transform_standard                           */
/*==============================================================*/
create table currency_transform_standard
(
   id                   bigint(20) not null auto_increment,
   origin               varchar(5) not null default 'CN' comment '源币种国家code',
   target               varchar(5) not null comment '目标币种国家code',
   exchange_rate        double not null comment '汇率1/X',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table currency_transform_standard comment '币种转换标准';

/*==============================================================*/
/* Table: pricing_template                                      */
/*==============================================================*/
create table pricing_template
(
   id                   bigint(20) not null auto_increment,
   name                 varchar(60) not null,
   country_code         varchar(5) not null comment '使用国家（US）',
   user_id              bigint(20) not null,
   merchant_id          bigint(20) not null,
   currency_transform_standard_id bigint(20) not null comment '汇率ID',
   pricing_setting      text not null comment '定价设置',
   is_delete            tinyint(1) not null default 0 comment '删除',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table pricing_template comment '定价模板';

/*==============================================================*/
/* Table: pricing_template_product_item                         */
/*==============================================================*/
create table pricing_template_product_item
(
   id                   bigint(20) not null auto_increment,
   pricing_template_id  bigint(20) not null comment '定价模板ID',
   merchant_product_parent_id bigint(20) not null comment '产品库ID',
   product_parent_id    bigint(20) not null comment '父产品ID',
   product_id           bigint(20) not null comment '产品ID',
   product_price        double(18,2) not null comment '产品价格',
   shipment_price       double(18,2) not null comment '运费',
   cost_price           double(18,2) not null comment '原价',
   standard_price       double(18,2) comment '标准价格',
   sale_price           double(18,2) comment '特价',
   start_time           bigint(20) comment '特价开始时间',
   end_time             bigint(20) comment '特价结束时间',
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table pricing_template_product_item comment '定价模板产品项';

INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '172', '0,1,172,', '定价模板', '30', '/admin/price-template', NULL, 'star', '1', '', '2018-03-12 17:29:33', NULL, '0', '1');

/*[上午 10:33:43][32 ms]*/ INSERT INTO `attribute` (`id`, `user_id`, `code`, `name`, `type`, `content_type`, `status`, `describe`, `created_time`, `updated_time`) VALUES (NULL, '0', 'standard_price', '标准价格', '1', '1', '1', '标准价格', '0', '0');
/*[上午 10:34:16][44 ms]*/ INSERT INTO `attribute` (`id`, `user_id`, `code`, `name`, `type`, `content_type`, `status`, `describe`, `created_time`, `updated_time`) VALUES (NULL, '0', 'sale_price', '特价', '1', '1', '1', '特价', '0', '0');
/*[上午 10:34:30][20 ms]*/ INSERT INTO `attribute` (`id`, `user_id`, `code`, `name`, `type`, `content_type`, `status`, `describe`, `created_time`, `updated_time`) VALUES (NULL, '0', 'sale_from_date', '特价开始日期', '1', '1', '1', '特价开始日期', '0', '0');
/*[上午 10:34:42][16 ms]*/ INSERT INTO `attribute` (`id`, `user_id`, `code`, `name`, `type`, `content_type`, `status`, `describe`, `created_time`, `updated_time`) VALUES (NULL, '0', 'sale_end_date', '特价结束日期', '1', '1', '1', '特价结束日期', '0', '0');
/*[上午 10:34:50][21 ms]*/ INSERT INTO `attribute` (`id`, `user_id`, `code`, `name`, `type`, `content_type`, `status`, `describe`, `created_time`, `updated_time`) VALUES (NULL, '0', 'currency', '货币', '1', '1', '1', '货币', '0', '0');


insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('1','CN','US','0.14466','2019-06-04 16:57:11');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('2','CN','GB','0.11435','2019-06-04 16:57:43');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('7','CN','MX','2.8461','2019-06-04 17:06:33');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('4','CN','JP','15.638','2019-06-04 17:04:48');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('5','CN','FR','0.12858','2019-06-04 17:05:38');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('6','CN','DE','0.12858','2019-06-04 17:06:07');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('3','CN','CA','0.1943','2019-06-04 16:57:50');
insert into `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) values('8','CN','AU','0.20727','2019-06-04 17:07:02');
INSERT INTO `currency_transform_standard` (`id`, `origin`, `target`, `exchange_rate`, `create_time`) VALUES (NULL, 'CN', 'CN', '1', '2019-06-04 17:07:02');


insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('35','US','美国(美元)','currencyName','货币类型文本','1','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('36','GB','英国(英镑)','currencyName','货币类型文本','2','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('37','CA','加拿大(加元)','currencyName','货币类型文本','3','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('38','JP','日本(日元)','currencyName','货币类型文本','4','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('39','FR','法国(欧元)','currencyName','货币类型文本','5','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('40','DE','德国(欧元)','currencyName','货币类型文本','6','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('41','MX','墨西哥(墨西哥元)','currencyName','货币类型文本','7','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('42','AU','澳大利亚(澳元)','currencyName','货币类型文本','8','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('43','CN','中国(人民币)','currencyName','货币类型文本','9','0',NULL,'2019-06-04 17:10:04',NULL,'2019-06-04 17:10:08',NULL,'0');


CREATE TABLE `barcode_remember` (
  `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
  `country_code` varchar(100) NOT NULL DEFAULT '',
  `makers_code` varchar(100) NOT NULL DEFAULT '',
  `user_id` bigint(10) NOT NULL DEFAULT '0',
  `type` int(10) NOT NULL DEFAULT '0' COMMENT '1ena2upc',
  `create_time` bigint(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


ALTER TABLE `product`
  ADD COLUMN `color_code` VARCHAR(16) DEFAULT '' NOT NULL COMMENT '颜色编码' AFTER `color_name`,
  ADD COLUMN `color_opacity` TINYINT(3) DEFAULT 0 NOT NULL COMMENT '颜色透明度' AFTER `color_code`;


  UPDATE `import_barcode` SET STATUS = 2 WHERE qty = used_qty;


/*[下午 20:31:44][4541 ms]*/ ALTER TABLE `task_child_finished` ADD COLUMN `barcode_id` BIGINT(20) NULL COMMENT '条形码ID' AFTER `parent_attribute`;

UPDATE task_child_finished f SET barcode_id = (SELECT id FROM barcode b WHERE b.number = f.barcode LIMIT 1) WHERE f.barcode <> '';

ALTER TABLE `task_child_layer`
  ADD COLUMN `height` DOUBLE(10,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '图层搞' AFTER `create_date`,
  ADD COLUMN `width` DOUBLE(10,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '图层宽' AFTER `height`;


ALTER TABLE `task_child_layer`
  ADD COLUMN `src_filecode` VARCHAR(128) DEFAULT '' NOT NULL COMMENT '源图层数据' AFTER `width`;
UPDATE `amazon_template` SET country_code ="GB" WHERE country_code = "UK" ;



UPDATE `excel_template` SET country_code ="GB" WHERE country_code = "UK" ;

UPDATE  `task_child_layer` tcl ,`img_cut` ic SET tcl.`height` = ic.`height`,tcl.`width` = ic.`width`,tcl.`src_filecode` =REPLACE(ic.`src_url`, 'http://static-photo-center-prov.oss-cn-hangzhou.aliyuncs.com/images/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/', '')   WHERE tcl.`material_img_url` = ic.`md5`;


ALTER TABLE `barcode` CHANGE number number VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci;
ALTER TABLE `barcode` CHANGE country_code country_code VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci;
ALTER TABLE `barcode` CHANGE makers_code makers_code VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci;
ALTER TABLE `import_barcode` CHANGE makers_code makers_code VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci;