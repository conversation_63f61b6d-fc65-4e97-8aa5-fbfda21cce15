CREATE TABLE `product_details` (
  `id` INT(20) NOT NULL AUTO_INCREMENT,
  `product_id` INT(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商品id',
  `production_process` TEXT COMMENT '生产工艺',
  `material_description` TEXT COMMENT '材质说明',
  `accessory_construction` TEXT COMMENT '配件构造',
  `product_performance` TEXT COMMENT '产品性能',
  `applicable_scenarios` TEXT COMMENT '适用场景',
  `washing_instructions` TEXT COMMENT '洗涤说明',
  `special_description` TEXT COMMENT '特别说明',
  `reminder` TEXT COMMENT '温馨提示',
  `design_explanation` TEXT COMMENT '设计说明',
  `design_area` TEXT COMMENT '设计区域',
  `picture_request` TEXT COMMENT '图片要求',
  `product_size` TEXT COMMENT '产品尺码',
  `packaging_specification` TEXT COMMENT '包装规格',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`)
) ENGINE=INNODB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4
