ALTER TABLE `merchant` ADD COLUMN `account_type` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '0 普通用户，1 月结用户' AFTER `is_analyze`;

ALTER TABLE `order` ADD COLUMN `settle_accounts_status` TINYINT(3) UNSIGNED DEFAULT 2 NOT NULL COMMENT '定期结算订单：0 未结算 1 已结算 2非定期结算订单' AFTER `carriage_cost`;

ALTER TABLE `payment` CHANGE `method` `method` VARCHAR(20) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL;

ALTER TABLE `refund_record` CHANGE `payment_method` `payment_method` VARCHAR(20) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT ' 支付方式 参考订单';