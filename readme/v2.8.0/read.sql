CREATE TABLE `user_sku_rule`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `parent_sku_content` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '母体的内容',
  `child_sku_type` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '变体类型 COLOR_SIZE,END_PRODUCT_ID,SAME_PARENT',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `unique_user` (`user_id`)
);

CREATE TABLE `order_sync_history`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `status` TINYINT(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态 \"0不同步 1同步中 2同步成功 99同步失败',
  `error` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '错误信息',
  PRIMARY KEY (`id`)
);


ALTER TABLE `merchant_sys_menu`
  ADD COLUMN `role_office_permission_type` TINYINT(4) UNSIGNED DEFAULT 2 NOT NULL COMMENT '1有部门权限设置2没有' AFTER `type`;
/*[下午 15:48:11][1464 ms]*/ ALTER TABLE  `merchant_sys_role_menu` ADD COLUMN `role_office_permission` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT '1本人,2本部门,3本部门及下属部门,4全公司' AFTER `menu_id`;
  /*[下午 14:37:49][27 ms]*/ UPDATE `merchant_sys_menu` SET `role_office_permission_type` = '1' WHERE `id` = '209';
/*[下午 14:38:18][19 ms]*/ UPDATE `merchant_sys_menu` SET `role_office_permission_type` = '1' WHERE `id` = '201';
/*[下午 14:44:37][32 ms]*/ UPDATE `merchant_sys_menu` SET `role_office_permission_type` = '1' WHERE `id` = '185';
/*[下午 14:46:03][26 ms]*/ UPDATE `merchant_sys_menu` SET `role_office_permission_type` = '1' WHERE `id` = '187';
ALTER TABLE `order`
  ADD COLUMN `carriage_status` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '0不同步 1同步中 2同步成功 99同步失败' AFTER `carriage_no`		/* 复制栏位名称 */;


ALTER TABLE `order`
  ADD COLUMN `merchant_store_platform_code` VARCHAR(100) DEFAULT '' NOT NULL COMMENT '平台code' AFTER `transaction_code`,
  ADD COLUMN `merchant_store_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '店铺id' AFTER `merchant_store_platform_code`,
  ADD COLUMN `site` VARCHAR(100) DEFAULT '' NULL COMMENT '站点' AFTER `merchant_store_id`;


  ALTER TABLE `order_item`
  ADD COLUMN `product_type` INT(10) DEFAULT 0 NOT NULL COMMENT '1平台产品2私有' AFTER `used_free_gold`;

  ALTER TABLE `order_item`
  ADD COLUMN `out_order_item_id` VARCHAR(100) DEFAULT '' NOT NULL COMMENT '导入订单的orderitemsid' AFTER `product_type`;


  INSERT  INTO `attribute`(`user_id`,`code`,`name`,`type`,`content_type`,`status`,`describe`,`created_time`,`updated_time`) VALUES
(0,'shop_code','店铺代号',1,1,1,'店铺代号',0,0),
(0,'end_product_id','成品ID',1,1,1,'中英文数字组成的成品ID',0,0),
(0,'product_name_code','产品代号',1,1,1,'默认是产品英文名称的首字母，例产品名称：Cotton Pillowcase ,则产品代号为：CP',0,0),
(0,'site','站点',1,1,1,'仅亚马逊有，其他平台导出为空。站点为：US(美国); CA(加拿大); GB(英国);FR(法国); DE(德国); IT(意大利); ES(西班牙); JP(日本); MX(墨西哥); AU(澳大利亚)',0,0);

ALTER TABLE  `order`
  CHANGE `merchant_store_id` `merchant_store_code` VARCHAR(100) DEFAULT '' NOT NULL COMMENT '店铺code';


/*[上午 10:00:17][48 ms]*/ UPDATE `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '100';
/*[上午 10:00:20][10 ms]*/ UPDATE `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '99';
/*[上午 10:03:40][15 ms]*/ UPDATE  `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '104';
/*[上午 10:03:26][75 ms]*/ UPDATE  `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '103';
/*[上午 10:09:43][50 ms]*/ UPDATE  `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '106';
/*[上午 10:10:04][9 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '管理' , `permission` = 'sys:user:manage' WHERE `id` = '105';
/*[上午 10:15:07][11 ms]*/ UPDATE  `merchant_sys_menu` SET `role_office_permission_type` = '1' WHERE `id` = '105';
/*[上午 10:12:47][17 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '管理' WHERE `id` = '203';
/*[上午 10:13:10][24 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '查看' WHERE `id` = '201';
/*[上午 10:13:22][10 ms]*/ UPDATE  `merchant_sys_menu` SET `permission` = 'order:view:manage' WHERE `id` = '203';
/*[上午 10:35:39][40 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '查看' WHERE `id` = '187';
/*[上午 10:36:02][55 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '管理' , `permission` = 'endproduct:view:manage' WHERE `id` = '188';
/*[上午 10:39:00][13 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '查看' WHERE `id` = '185';
/*[上午 10:39:08][8 ms]*/ UPDATE  `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '186';

/*[上午 10:43:28][50 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '查看' WHERE `id` = '209';
/*[上午 10:44:03][13 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '分享/取消分享' WHERE `id` = '178';
/*[上午 10:44:51][9 ms]*/ UPDATE  `merchant_sys_menu` SET `name` = '管理' , `permission` = 'material:public:manage' WHERE `id` = '179';
/*[上午 10:48:33][17 ms]*/ UPDATE  `merchant_sys_menu` SET `parent_id` = '177' , `parent_ids` = '0,1,189,177,' WHERE `id` = '180';
/*[上午 10:51:35][52 ms]*/ INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`) VALUES ('213', '177', '0,1,189,177,', '查看', '3000', '', NULL, '', '0', 'material:public:view', '2018-06-19 05:03:06', NULL, '0', '1', '2');
ALTER TABLE `order`
  ADD COLUMN `sync_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '同步时间' AFTER `site`;



  ALTER TABLE `order`
  ADD COLUMN `order_import_record_order_id` BIGINT(100) DEFAULT 0 NOT NULL COMMENT 'import_id 表' AFTER `sync_error`;

ALTER TABLE `attribute`
  ADD COLUMN `show_in_sku` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '在sku的时候显示' AFTER `updated_time`;


  UPDATE `merchant_sys_role_menu` SET role_office_permission = 3 WHERE menu_id IN (187,209,201);
-- SELECT * FROM merchant_sys_role_menu WHERE menu_id IN (187,209,201)

-- ===============zjl start============
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'amz', '亚马逊', 'merchantStorePlatform', '店铺平台', '1', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'sell_fast', '速卖通', 'merchantStorePlatform', '店铺平台', '2', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'wish', 'wish', 'merchantStorePlatform', '店铺平台', '3', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'shopify', 'shopify', 'merchantStorePlatform', '店铺平台', '4', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'lazada', 'lazada', 'merchantStorePlatform', '店铺平台', '5', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'cat_hot', '天猫', 'merchantStorePlatform', '店铺平台', '6', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'taobao', '淘宝', 'merchantStorePlatform', '店铺平台', '7', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (NULL, 'other', '其他', 'merchantStorePlatform', '店铺平台', '8', '0', NULL, '2019-06-19 16:36:54', NULL, '2019-06-19 16:37:02', NULL, '0');

-- order_import_record_item` update
ALTER TABLE `order_import_record_item` CHANGE `sku` `sku` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '匹配sku(旧成品关联字段兼容性保留)', ADD COLUMN `order_import_record_order_id` BIGINT(20) DEFAULT 0 NOT NULL AFTER `postcode`, ADD COLUMN `end_product_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '成品ID' AFTER `order_import_record_order_id`;
ALTER TABLE `sds_saas_dev`.`order_import_record_item` CHANGE `detail` `detail` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址', ADD COLUMN `is_delete` TINYINT(1) DEFAULT 0 NOT NULL AFTER `end_product_id`, ADD COLUMN `import_product_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '导入产品名' AFTER `is_delete`, ADD COLUMN `import_color` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '导入颜色' AFTER `import_product_name`, ADD COLUMN `import_size` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '导入规格' AFTER `import_color`, ADD COLUMN `import_imgs` TEXT NULL COMMENT '导入图片' AFTER `import_size`, ADD COLUMN `img_load_status` TINYINT(4) DEFAULT 0 NOT NULL COMMENT '导入图片下载状态：0 未下载 1下载中 2 下载完成 3 下载失败' AFTER `import_imgs`;
ALTER TABLE `order_import_record_item` ADD COLUMN `item_order_no` VARCHAR(50) DEFAULT '' NOT NULL COMMENT 'amz_item订单号' AFTER `img_load_status`;
ALTER TABLE `order_import_record_item` ADD COLUMN `import_asin` VARCHAR(25) DEFAULT '' NOT NULL AFTER `item_order_no`;
-- init permission
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '172', '0,1,172,', '店铺管理', '5', '/admin/shop-manage', NULL, 'star', '1', '', '2018-03-12 17:29:33', NULL, '0', '1');
UPDATE `merchant_sys_menu` SET `sort` = '6' WHERE `id` = '204';
-- INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '210', '0,1,172,210,', '查看店铺', '1', '', NULL, '', '0', 'store:view:all', '2018-09-25 16:36:45', NULL, '0', '1');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '210', '0,1,172,210,', '管理店铺', '2', '', NULL, '', '0', 'store:edit:all', '2018-09-25 16:36:45', NULL, '0', '1');
/*[下午 15:51:01][53 ms]*/ UPDATE `merchant_sys_menu` SET `sort` = '1' WHERE `id` = '210';
/*[下午 15:51:30][53 ms]*/ UPDATE `merchant_sys_menu` SET `sort` = '2' WHERE `id` = '168';
/*[下午 17:20:34][55 ms]*/ UPDATE `merchant_sys_menu` SET `sort` = '3' WHERE `id` = '176';
/*[下午 17:20:35][54 ms]*/ UPDATE `merchant_sys_menu` SET `sort` = '4' WHERE `id` = '175';
/*[下午 17:20:37][54 ms]*/ UPDATE `merchant_sys_menu` SET `sort` = '5' WHERE `id` = '174';
/*[下午 17:21:19][53 ms]*/ UPDATE `merchant_sys_menu` SET `sort` = '4' WHERE `id` = '173';

drop table if exists merchant_store;

drop table if exists merchant_store_exclude_rule;

drop table if exists merchant_store_user_relation;

drop table if exists order_import_record_order;

/*==============================================================*/
/* Table: merchant_store                                        */
/*==============================================================*/
create table merchant_store
(
   id                   bigint(20) not null auto_increment,
   merchant_id          bigint(20) not null,
   merchant_sys_offices_id bigint(20) comment '部门ID',
   merchant_store_platform_code varchar(25) not null comment '平台码',
   creator_id           bigint(20) not null comment '创建用户ID',
   name                 varchar(30) not null comment '店铺名(同平台不重名)',
   code                 varchar(6) not null comment '店铺代号',
   site_department      varchar(25) not null default '' comment '站点系',
   site                 varchar(255) not null default '' comment '站点',
   amz_authorized       tinyint(1) not null default 0 comment '亚马逊授权',
   amz_authorized_status tinyint(4) not null default 1 comment '亚马逊授权状态:1未授权 2授权成功 3 授权失效',
   order_sync           tinyint(1) not null default 0 comment '自动同步订单',
   tracking_number_sync tinyint(1) not null default 0 comment '自动同步运单号',
   tracking_number_sync_mode tinyint(5) not null default 0 comment '同步运单号方式: 1 运单号生成后立即同步运单号 2 amz发货周期内同步 3 产品发出时同步运单号',
   seller_id            varchar(125) not null default '' comment 'Seller ID',
   mws_auth_token       varchar(125) not null default '' comment 'MWS Auth Token',
   status               tinyint(4) not null default 1 comment '0 禁用 1 正常 ',
   is_system            tinyint(1) not null default 0 comment '系统店铺: 0 否 1 是',
   is_delete            tinyint(1) not null default 0,
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   update_time          timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table merchant_store comment '店铺';

/*==============================================================*/
/* Table: merchant_store_exclude_rule                           */
/*==============================================================*/
create table merchant_store_exclude_rule
(
   id                   bigint(20) not null auto_increment,
   merchant_store_id    bigint(20) not null,
   include_mode         text comment '规则排除',
   exact_mode           text comment '精准排除',
   primary key (id)
);

alter table merchant_store_exclude_rule comment '商户店铺sku排除规则';

/*==============================================================*/
/* Table: merchant_store_user_relation                          */
/*==============================================================*/
create table merchant_store_user_relation
(
   id                   bigint(20) not null auto_increment,
   merchant_store_id    bigint(20) not null,
   merchant_sys_user_id bigint(20) not null,
   is_delete            tinyint(1) not null default 0,
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table merchant_store_user_relation comment '商户店铺用户关系(管理者)';

/*==============================================================*/
/* Table: order_import_record_order                             */
/*==============================================================*/
create table order_import_record_order
(
   id                   bigint(20) not null auto_increment,
   merchant_store_platform_code varchar(25) not null comment '平台码',
   merchant_store_id    bigint(20) not null,
   site                 varchar(25) not null default '' comment '归属站点',
   out_order_no         varchar(50) default NULL comment '订单号',
   receiver             varchar(128) comment '收件人姓名',
   country              varchar(10) comment '国家code如US',
   postcode             varchar(20) default NULL comment '邮编',
   mobile_phone         varchar(128) comment '移动电话',
   province             varchar(50) comment '省',
   province_code        varchar(20) not null default '' comment '省ID',
   city                 varchar(50) comment '市',
   city_code            varchar(20) not null default '' comment '市ID',
   detail               text comment '详细地址',
   source_type          tinyint(5) not null default 1 comment '数据来源: 1 导入 2 自动同步',
   repeat_no            int(11) not null default 0 comment '订单是否重复与导入数据重复',
   repetition           int(1) not null default 0 comment '订单是否被下过单',
   is_delete            tinyint(1) not null default 0,
   out_platform_orders_time bigint(20) comment '第三方平台下单时间',
   create_time          timestamp not null default CURRENT_TIMESTAMP,
   update_time          timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table order_import_record_order comment '订单导入记录(订单号归类)';

DROP TABLE IF EXISTS http_communication_moniter;

/*==============================================================*/
/* Table: http_communication_monitor                            */
/*==============================================================*/
CREATE TABLE http_communication_monitor
(
   id                   BIGINT(20) NOT NULL AUTO_INCREMENT,
   type_code            VARCHAR(50) NOT NULL COMMENT '唯一码(对应模块)',
   STATUS               TINYINT(3) NOT NULL DEFAULT 0 COMMENT '0: 待处理数据 1 完成数据 2 异常数据',
   about_info           TEXT COMMENT '当下请求环境',
   is_delete            TINYINT(1) NOT NULL DEFAULT 0,
   create_time          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
   update_time          TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (id)
);

ALTER TABLE http_communication_monitor COMMENT 'http通信监听表(监听通信并对异常状态数据进行处理,请不要插入日志类型数据)';

-- =============== zjl end ============

-- =============== zx ============

CREATE TABLE `public_product` (
  `id` int(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
  `task_child_finish_id` varchar(50) NOT NULL DEFAULT '' COMMENT '成品id',
  `color` varchar(255) NOT NULL DEFAULT '',
  `size` varchar(50) NOT NULL DEFAULT '' COMMENT '规格',
  `seller_sku` varchar(50) NOT NULL DEFAULT '',
  `barcode` varchar(50) DEFAULT '',
  `image_urls_str` text COMMENT '成品图片',
  `key_id` varchar(8) NOT NULL,
  `is_parent` tinyint(1) NOT NULL DEFAULT '2' COMMENT '2 母体 4变体',
  `export_history_id` int(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1334 DEFAULT CHARSET=utf8mb4;


 ALTER TABLE task_child_finished ADD COLUMN `key_id` VARCHAR(20) DEFAULT '' NULL AFTER `out_customer_id`;

 ALTER TABLE `export_history` ADD COLUMN `site_code` VARCHAR(50) NOT NULL COMMENT '站点' AFTER `msg`, ADD COLUMN `shop_id` INT(20) NOT NULL COMMENT '店铺id' AFTER `site_code`, ADD COLUMN `platform_code` VARCHAR(50) NOT NULL AFTER `shop_id`, ADD COLUMN `public_status` TINYINT(3) DEFAULT 1 NOT NULL COMMENT '发布状态 1发布中 2发布成功 -1发布失败' AFTER `platform_code`, ADD COLUMN `bar_type` VARCHAR(20) NOT NULL AFTER `public_status`, ADD COLUMN `pricing_template_id` INT(20) NOT NULL AFTER `bar_type`, ADD COLUMN `template_id` INT(20) NOT NULL AFTER `pricing_template_id`, ADD COLUMN `product_count` INT(20) NOT NULL AFTER `template_id`, ADD COLUMN `sku_count` INT(20) NOT NULL AFTER `product_count`, ADD COLUMN `filter_vaild` INT(20) NOT NULL AFTER `sku_count`, ADD COLUMN `sku_update_type` INT(20) NOT NULL AFTER `filter_vaild`, ADD COLUMN `barcode_gen_type` INT(20) NOT NULL AFTER `sku_update_type`;

UPDATE `order_item` SET product_type = 2 WHERE order_id IN (SELECT id FROM `order` WHERE production_type = 2);
UPDATE `order_item` SET product_type = 1 WHERE order_id IN (SELECT id FROM `order` WHERE production_type = 1);

UPDATE `merchant_sys_role_menu` SET role_office_permission = 1;
UPDATE `merchant_sys_role_menu` SET role_office_permission = 4 WHERE role_id IN (SELECT id FROM `merchant_sys_role` WHERE role_type = "boss")
UPDATE `merchant_sys_role_menu` SET role_office_permission = 3 WHERE menu_id IN (187,209,201);
ALTER TABLE `order_item` ADD COLUMN `key_id` VARCHAR(200) DEFAULT '' NOT NULL COMMENT '成品对应的key_id' AFTER `out_order_item_id`;

/*[上午 11:08:50][115 ms]*/ ALTER TABLE `merchant_sys_menu` ADD COLUMN `extend_type` TINYINT(4) UNSIGNED DEFAULT 1 NOT NULL COMMENT '业务员特殊显示1,不显示2' AFTER `role_office_permission_type`;
ALTER TABLE `merchant_sys_menu` ADD COLUMN `show_flag` TINYINT(4) DEFAULT 0 NOT NULL COMMENT '前端显示标识' AFTER `role_office_permission_type`;


/*[下午 20:17:06][66 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '187' WHERE `id` = '188';
/*[下午 20:17:13][68 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '187' WHERE `id` = '187';

/*[下午 20:14:22][69 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '209' WHERE `id` = '178';
/*[下午 20:15:08][67 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '209' WHERE `id` = '179';
/*[下午 20:15:12][68 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '209' WHERE `id` = '209';

/*[上午 10:31:13][71 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '201' WHERE `id` = '201';
/*[上午 10:31:35][70 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '201' WHERE `id` = '203';

/*[下午 13:49:00][70 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '213' WHERE `id` = '180';
/*[下午 13:49:04][68 ms]*/ UPDATE `merchant_sys_menu` SET `show_flag` = '213' WHERE `id` = '213';

ALTER TABLE `merchant_sys_menu` ADD COLUMN `manage_flag` TINYINT(4) UNSIGNED DEFAULT 2 NOT NULL COMMENT '1是2不是' AFTER `show_flag`;
/*[下午 18:27:58][69 ms]*/ UPDATE `merchant_sys_menu` SET `manage_flag` = '1' WHERE `id` = '209';
/*[下午 18:28:13][71 ms]*/ UPDATE `merchant_sys_menu` SET `manage_flag` = '1' WHERE `id` = '201';
/*[下午 18:28:57][70 ms]*/ UPDATE `merchant_sys_menu` SET `manage_flag` = '1' WHERE `id` = '187';
/*[下午 18:29:12][69 ms]*/ UPDATE `merchant_sys_menu` SET `manage_flag` = '1' WHERE `id` = '212';
/*[下午 18:29:39][72 ms]*/ UPDATE `merchant_sys_menu` SET `manage_flag` = '1' WHERE `id` = '185';
/*[下午 18:29:48][70 ms]*/ UPDATE `merchant_sys_menu` SET `manage_flag` = '1' WHERE `id` = '105';

UPDATE `merchant_sys_menu` SET `del_flag` = '1' WHERE `id` = '203';

UPDATE `merchant_sys_menu` SET `name` = '管理' WHERE `id` = '201';

/*[下午 19:58:10][67 ms]*/ UPDATE `merchant_sys_menu` SET `parent_ids` = '0,1,168,' WHERE `id` = '187';
/*[下午 19:58:17][66 ms]*/ UPDATE `merchant_sys_menu` SET `parent_ids` = '0,1,168,' WHERE `id` = '188';



