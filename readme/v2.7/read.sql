
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('53','main_account','老板号（主账号）','merchantPermissionItem','商户权限项code','1','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('54','sub_account','员工号（子账号）','merchantPermissionItem','商户权限项code','2','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('55','product_design','设计次数','merchantPermissionItem','商户权限项code','3','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('56','material_capacity','素材图库容量','merchantPermissionItem','商户权限项code','4','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('57','product_myself_consignment','开通自发货产品','merchantPermissionItem','商户权限项code','6','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('58','special_product_permission','特殊产品授权','merchantPermissionItem','商户权限项code','7','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('59','private_template','赠送私人独立模板','merchantPermissionItem','商户权限项code','8','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('60','material_keyword_extract','素材关键字提取','merchantPermissionItem','商户权限项code','9','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('61','barcode_tool','EAN/UPC工具','merchantPermissionItem','商户权限项code','10','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('62','automation_update_barcode','智能更新URL/UPC/EAN','merchantPermissionItem','商户权限项code','12','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('63','packaging_pick','包装分拣','merchantPermissionItem','商户权限项code','13','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('64','template_online','在线模板','merchantPermissionItem','商户权限项code','14','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('65','template_user_dispose','导入导出模板','merchantPermissionItem','商户权限项code','15','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('66','data_forms','数据报表','merchantPermissionItem','商户权限项code','16','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('67','calculate_costing','成本计算','merchantPermissionItem','商户权限项code','17','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('68','logistics_sync','运单生成同步','merchantPermissionItem','商户权限项code','18','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('69','invoice','开票（加税点）','merchantPermissionItem','商户权限项code','19','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('70','fba','FBA','merchantPermissionItem','商户权限项code','20','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('74','automation_generate_barcode','智能生成UPC/EAN','merchantPermissionItem','商户权限项code','11','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('75','product_permission','产品权限','merchantPermissionItem','商户权限项code','5','0',NULL,'2019-06-19 16:36:54',NULL,'2019-06-19 16:37:02',NULL,'0');

--  会员字典
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('71','free_set_meal','免费会员','merchantPermission','商户套餐','1','0',NULL,'2019-06-19 17:02:36',NULL,'2019-06-19 17:02:38',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('72','month_set_meal','月会员','merchantPermission','商户套餐','2','0',NULL,'2019-06-19 17:02:36',NULL,'2019-06-19 17:02:38',NULL,'0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('73','year_set_meal','年会员','merchantPermission','商户套餐','3','0',NULL,'2019-06-19 17:02:36',NULL,'2019-06-19 17:02:38',NULL,'0');


drop table if exists merchant_platform_permission;

drop table if exists merchant_platform_permission_set_meal;

drop table if exists platform_goods;

drop index uniq_code_fid on platform_permission;

drop table if exists platform_permission;

drop table if exists platform_permission_set_meal;

/*==============================================================*/
/* Table: merchant_platform_permission                          */
/*==============================================================*/
create table merchant_platform_permission
(
   id                   bigint(20) not null auto_increment,
   merchant_platform_permission_set_meal_id bigint(20) not null,
   merchant_id          bigint(20) not null,
   code                 varchar(100) not null comment '权限码',
   type                 tinyint(3) not null comment '权限类型 1 消耗 2 计量 3 授权',
   status               tinyint(3) not null default 0 comment '记录状态 0 未启用 1 使用中 2 失效(type 1 次数用完 2 过期)',
   is_increase          tinyint(1) not null default 1 comment '是否为增量权限（累加）非增量权限不可累加只取最大',
   is_period            tinyint(1) comment '是否为限时权限',
   start_time           bigint(20) comment '权限有效开始时间',
   end_time             bigint(20) comment '权限有效结束时间',
   value                int not null comment '数值',
   multiple             int not null default 1 comment '倍数(为适应产品变态显示设计)',
   total_value          bigint(20) not null comment '数值x倍数',
   used_value           bigint(20) not null default 0 comment '已消耗的数值',
   unit                 varchar(8) not null default '' comment '单位（显示符号）',
   expire_hint          tinyint(1) not null default 0 comment '是否触发过过期提示',
   is_delete            tinyint(1) not null default 0,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table merchant_platform_permission comment '商户权限';

/*==============================================================*/
/* Table: merchant_platform_permission_set_meal                 */
/*==============================================================*/
create table merchant_platform_permission_set_meal
(
   id                   bigint(20) not null auto_increment,
   platform_goods_order_id bigint(20) not null default 0,
   platform_permission_set_meal_id bigint(20) not null,
   merchant_id          bigint(20) not null,
   set_meal_name        varchar(25) not null,
   code                 varchar(100) not null comment '标识码',
   set_meal_type        tinyint(3) not null comment '商品类型 1 权限套餐 2 权限增值服务',
   level_type           tinyint(5) not null comment '商品类型(细)',
   status               tinyint(3) not null default 0 comment '记录状态 0 未启用 1 使用中 2 失效(type 1 次数用完 2 过期)',
   source_type          tinyint(3) not null comment '记录类型 0 免费套餐 1 商户套餐 2 商户增值项 3 平台赠送',
   period               int not null default 0 comment '权限有效周期(天)',
   period_units         varchar(5) not null comment '权限周期单(显示用)',
   start_time           bigint(20) comment '权限有效开始时间',
   end_time             bigint(20) comment '权限有效结束时间',
   is_delete            tinyint(1) not null default 0,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table merchant_platform_permission_set_meal comment '商户权限套餐记录';

/*==============================================================*/
/* Index: Index_merchant_id                                     */
/*==============================================================*/
create index Index_merchant_id on merchant_platform_permission_set_meal
(
   merchant_id,
   code
);

/*==============================================================*/
/* Table: platform_goods                                        */
/*==============================================================*/
create table platform_goods
(
   id                   bigint(20) not null auto_increment,
   name                 varchar(25) not null comment '商品名称',
   price                double(18,2) not null comment '价格',
   status               tinyint(3) not null default 1 comment '商品状态 0 下架 1 上架',
   sale_price           double(18,2) comment '特价',
   sale_price_start_time bigint(20) comment '特价开始时间',
   sale_price_end_time  bigint(20) comment '特价结束时间',
   start_time           bigint(20) comment '上架开始时间',
   end_time             bigint(20) comment '上架结束时间',
   is_delete            tinyint(1) not null default 0,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table platform_goods comment 'sds平台商品';

/*==============================================================*/
/* Table: platform_permission                                   */
/*==============================================================*/
create table platform_permission
(
   id                   bigint(20) not null auto_increment,
   platform_permission_set_meal_id bigint(20) not null,
   code                 varchar(100) not null comment '权限码',
   type                 tinyint(3) not null comment '权限类型 1 消耗 2 计量 3 授权',
   is_increase          tinyint(1) not null default 1 comment '是否为增量权限（累加）',
   is_period            tinyint(1) default 1 comment '是否为限时权限',
   value                int not null comment '数值',
   multiple             int not null default 1 comment '倍数(为适应产品变态显示设计)',
   unit                 varchar(8) not null default '' comment '单位（显示符号）',
   is_delete            tinyint(1) not null default 0,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   primary key (id)
);

alter table platform_permission comment '商户权限';

/*==============================================================*/
/* Index: uniq_code_fid                                         */
/*==============================================================*/
create unique index uniq_code_fid on platform_permission
(
   platform_permission_set_meal_id,
   code
);

/*==============================================================*/
/* Table: platform_permission_set_meal                          */
/*==============================================================*/
create table platform_permission_set_meal
(
   id                   bigint(20) not null auto_increment,
   code                 varchar(100) not null comment '标识码',
   platform_goods_id    bigint(20) not null,
   set_meal_type        tinyint(3) not null comment '商品类型 1 权限套餐 2 权限增值服务',
   level_type           tinyint(5) not null comment '商品类型(细)',
   period               int not null default 0 comment '权限有效周期(天)',
   period_units         varchar(5) not null default '' comment '权限周期单(显示用)',
   is_delete            tinyint(1) not null default 0,
   create_time          datetime not null default CURRENT_TIMESTAMP,
   update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   primary key (id)
);

alter table platform_permission_set_meal comment '商户权限套餐';


-- table modify
ALTER TABLE `platform_permission` CHANGE `unit` `unit` VARCHAR(12) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '单位（显示符号）';
ALTER TABLE `merchant_platform_permission` CHANGE `unit` `unit` VARCHAR(12) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '单位（显示符号）';
ALTER TABLE `platform_permission` CHANGE `type` `type` TINYINT(3) NOT NULL COMMENT '权限类型 1 消耗 2 计量 3 授权 4 复杂权限（只提供套餐相关显示，实际权限于相应代码模块控制）';
ALTER TABLE `merchant_platform_permission` CHANGE `type` `type` TINYINT(3) NOT NULL COMMENT '权限类型 1 消耗 2 计量 3 授权 4 复杂权限（只提供套餐相关显示，实际权限于相应代码模块控制）';

-- data init

insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('1','免费会员','0.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:25:10','2019-06-20 11:25:10');
insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('2','月会员','1000.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:25:27','2019-06-20 15:18:54');
insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('3','年会员','8000.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:25:35','2019-06-20 15:18:59');
insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('4','设计次数','200.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:27:29','2019-06-20 15:19:03');
insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('5','图库容量','5.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:27:35','2019-06-20 15:19:06');
insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('6','私有模板','50.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:27:42','2019-06-20 15:19:11');
insert into `platform_goods` (`id`, `name`, `price`, `status`, `sale_price`, `sale_price_start_time`, `sale_price_end_time`, `start_time`, `end_time`, `is_delete`, `create_time`, `update_time`) values('7','关键字提取','500.00','1',NULL,NULL,NULL,NULL,NULL,'0','2019-06-20 11:32:32','2019-06-20 15:19:14');


insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('1','free_set_meal','1','1','0','0','','0','2019-06-20 11:31:08','2019-06-20 11:31:08');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('2','month_set_meal','2','1','1','30','月','0','2019-06-20 11:31:33','2019-06-20 11:33:14');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('3','year_set_meal','3','1','2','365','年','0','2019-06-20 11:31:38','2019-06-20 11:33:20');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('4','value_added_services','4','2','0','0','','0','2019-06-20 11:31:52','2019-06-20 11:31:52');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('5','value_added_services','5','2','1','30','月','0','2019-06-20 11:31:57','2019-06-20 11:33:36');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('6','value_added_services','6','2','1','0','','0','2019-06-20 11:32:02','2019-06-20 11:32:02');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('7','value_added_services','7','2','1','0','','0','2019-06-20 11:32:55','2019-06-20 11:32:55');
insert into `platform_permission_set_meal` (`id`, `code`, `platform_goods_id`, `set_meal_type`, `level_type`, `period`, `period_units`, `is_delete`, `create_time`, `update_time`) values('8','value_added_services','0','2','1','0','','0','2019-06-20 11:32:55','2019-06-20 11:32:55');



insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('1','1','main_account','1','0','0','1','1','','0','2019-06-20 11:43:43');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('2','1','sub_account','2','1','0','3','1','个','0','2019-06-20 11:44:20');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('3','1','product_design','1','1','0','1000','1','次','0','2019-06-20 11:44:49');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('4','1','material_capacity','2','1','0','3','**********','G','0','2019-06-20 11:47:53');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('5','1','template_online','3','0','0','1','1','','0','2019-06-20 11:53:31');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('6','1','template_user_dispose','3','0','0','1','1','','0','2019-06-20 11:54:37');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('7','1','data_forms','3','0','0','1','1','','0','2019-06-20 11:54:52');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('8','1','calculate_costing','3','0','0','1','1','','0','2019-06-20 11:54:59');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('9','1','logistics_sync','3','0','0','1','1','','0','2019-06-20 11:55:03');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('10','1','invoice','3','0','0','1','1','','0','2019-06-20 11:55:10');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('11','1','fba','3','0','0','1','1','','0','2019-06-20 11:55:19');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('19','2','main_account','1','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('20','2','sub_account','2','1','1','30','1','个','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('21','2','product_design','1','1','1','5','10000','万次','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('22','2','material_capacity','2','1','1','50','**********','G','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('23','2','product_myself_consignment','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('24','2','special_product_permission','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('25','2','private_template','2','0','0','30','1','张','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('26','2','material_keyword_extract','1','0','1','3000','1','次','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('27','2','barcode_tool','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('28','2','automation_update_barcode','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('29','2','packaging_pick','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('30','2','template_online','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('31','2','template_user_dispose','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('32','2','data_forms','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('33','2','calculate_costing','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('34','2','logistics_sync','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('35','2','invoice','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('36','2','fba','3','0','1','1','1','','0','2019-06-20 12:04:46');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('50','3','main_account','1','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('51','3','sub_account','2','1','1','60','1','个','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('52','3','product_design','1','1','1','100','10000','万次','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('53','3','material_capacity','2','1','1','150','**********','G','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('54','3','product_myself_consignment','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('55','3','special_product_permission','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('56','3','private_template','2','0','0','50','1','张','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('57','3','material_keyword_extract','1','1','1','5','10000','万次','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('58','3','barcode_tool','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('59','3','automation_update_barcode','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('60','3','packaging_pick','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('61','3','template_online','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('62','3','template_user_dispose','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('63','3','data_forms','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('64','3','calculate_costing','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('65','3','logistics_sync','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('66','3','invoice','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('67','3','fba','3','0','1','1','1','','0','2019-06-20 12:11:17');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('81','4','product_design','1','1','0','1','10000','万次','0','2019-06-20 12:15:01');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('82','5','material_capacity','2','1','1','1','**********','G','0','2019-06-20 12:15:31');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('83','6','private_template','2','1','0','1','1','张','0','2019-06-20 12:19:01');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('84','7','material_keyword_extract','1','1','0','1','10000','万次','0','2019-06-20 12:19:38');
insert into `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) values('85','8','sub_account','2','1','0','1','1','个','0','2019-06-20 12:19:38');
INSERT INTO `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) VALUES (NULL, '3', 'automation_generate_barcode', '3', '0', '1', '1', '1', '', '0', '2019-06-20 12:11:17');
INSERT INTO `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) VALUES (NULL, '1', 'product_permission', '4', '0', '0', '1', '1', '普通/特惠', '0', '2019-06-20 11:55:19');
INSERT INTO `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) VALUES (NULL, '2', 'product_permission', '4', '0', '1', '1', '1', '普通/新品/精品/特惠', '0', '2019-06-20 11:55:19');
INSERT INTO `platform_permission` (`id`, `platform_permission_set_meal_id`, `code`, `type`, `is_increase`, `is_period`, `value`, `multiple`, `unit`, `is_delete`, `create_time`) VALUES (NULL, '3', 'product_permission', '4', '0', '1', '1', '1', '普通/新品/精品/特惠', '0', '2019-06-20 11:55:19');

-- free set meal init

INSERT INTO merchant_platform_permission_set_meal (
  platform_permission_set_meal_id,
  merchant_id,
  set_meal_name,
  CODE,
  set_meal_type,
  level_type,
  STATUS,
  source_type,
  period,
  period_units
)
SELECT
  r.id,
  m.id,
  '免费套餐',
  r.code,
  r.set_meal_type,
  r.level_type,
  1,
  0,
  r.period,
  r.period_units 
FROM
  merchant m,
  (SELECT 
    * 
  FROM
    platform_permission_set_meal 
  WHERE CODE = 'free_set_meal') r;
  
  
INSERT INTO merchant_platform_permission (
  merchant_platform_permission_set_meal_id,
  merchant_id,
  CODE,
  TYPE,
  is_increase,
  is_period,
  VALUE,
  multiple,
  total_value,
  used_value,
  unit,
  STATUS
) 
SELECT 
  m.id,
  m.merchant_id,
  r.CODE,
  r.TYPE,
  r.is_increase,
  r.is_period,
  r.VALUE,
  r.multiple,
  r.VALUE * r.multiple,
  0,
  r.unit,
  1 
FROM
  merchant_platform_permission_set_meal m,
  (SELECT 
    * 
  FROM
    platform_permission 
  WHERE platform_permission_set_meal_id = 
    (SELECT 
      id 
    FROM
      platform_permission_set_meal 
    WHERE CODE = 'free_set_meal')) r;

-- init year member
DROP TABLE IF EXISTS temp_year_user_info;

CREATE TABLE IF NOT EXISTS `temp_year_user_info`( `id` BIGINT(20), `date` DATETIME);

insert into `temp_year_user_info` (`id`, `date`) values('399','2019-08-25 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('22','2019-09-23 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('24','2019-09-24 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('26','2019-09-24 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('77','2020-03-24 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('76','2020-03-24 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('75','2020-03-24 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('78','2020-03-25 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('79','2020-03-25 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('81','2020-03-26 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('80','2020-03-26 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('28','2020-05-30 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('110','2020-05-31 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('30','2020-05-31 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('23','2020-05-31 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('232','2020-05-31 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('270','2020-06-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('121','2020-06-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('298','2020-06-10 17:26:00');
insert into `temp_year_user_info` (`id`, `date`) values('285','2020-06-23 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('236','2020-06-30 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('262','2020-06-30 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('279','2020-06-30 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('388','2020-06-30 23:05:00');
insert into `temp_year_user_info` (`id`, `date`) values('389','2020-06-30 23:05:00');
insert into `temp_year_user_info` (`id`, `date`) values('423','2020-07-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('330','2020-07-01 08:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('93','2020-07-03 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('419','2020-07-25 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('107','2020-07-28 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('373','2020-08-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('334','2020-08-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('433','2020-08-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('460','2020-08-01 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('401','2020-08-01 15:59:00');
insert into `temp_year_user_info` (`id`, `date`) values('371','2020-08-03 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('465','2020-08-04 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('449','2020-08-31 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('366','2020-08-31 08:58:00');
insert into `temp_year_user_info` (`id`, `date`) values('258','2020-09-02 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('134','2020-09-04 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('479','2020-09-08 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('480','2020-09-08 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('220','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('34','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('33','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('32','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('31','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('13','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('12','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('5','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('1','2029-03-18 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('51','2029-03-19 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('52','2029-03-22 00:00:00');
insert into `temp_year_user_info` (`id`, `date`) values('56','2031-06-02 16:33:00');

INSERT INTO merchant_platform_permission_set_meal (
  platform_permission_set_meal_id,
  merchant_id,
  set_meal_name,
  CODE,
  set_meal_type,
  level_type,
  STATUS,
  source_type,
  period,
  period_units,
  start_time,
  end_time
)
SELECT
  r.id,
  m.id,
  '年会员',
  r.code,
  r.set_meal_type,
  r.level_type,
  1,
  1,
  r.period,
  r.period_units,
  UNIX_TIMESTAMP(STR_TO_DATE(DATE_FORMAT(NOW(), '%Y-%m-%d'), '%Y-%m-%d'))*1000,
  UNIX_TIMESTAMP(i.date)*1000
FROM
  merchant m,
  (SELECT
    *
  FROM
    platform_permission_set_meal
  WHERE CODE = 'year_set_meal') r, temp_year_user_info i
  WHERE m.id = i.id AND (SELECT COUNT(1) FROM merchant_platform_permission_set_meal WHERE `status` = 1 AND merchant_id = m.id AND `code` IN ('month_set_meal','year_set_meal')) = 0;


INSERT INTO merchant_platform_permission (
  merchant_platform_permission_set_meal_id,
  merchant_id,
  CODE,
  TYPE,
  is_increase,
  is_period,
  VALUE,
  multiple,
  total_value,
  used_value,
  unit,
  STATUS,
  start_time,
  end_time
)
SELECT
  m.id,
  m.merchant_id,
  r.CODE,
  r.TYPE,
  r.is_increase,
  r.is_period,
  r.VALUE,
  r.multiple,
  r.VALUE * r.multiple,
  0,
  r.unit,
  1,
  UNIX_TIMESTAMP(STR_TO_DATE(DATE_FORMAT(NOW(), '%Y-%m-%d'), '%Y-%m-%d'))*1000,
  UNIX_TIMESTAMP(i.date)*1000
FROM
  merchant_platform_permission_set_meal m,
  (SELECT
    *
  FROM
    platform_permission
  WHERE platform_permission_set_meal_id =
    (SELECT
      id
    FROM
      platform_permission_set_meal
    WHERE CODE = 'year_set_meal')) r, temp_year_user_info i
  WHERE m.merchant_id = i.id AND m.code = 'year_set_meal' AND (SELECT COUNT(1) FROM merchant_platform_permission p WHERE p.merchant_platform_permission_set_meal_id = m.id) = 0;

DROP TABLE IF EXISTS temp_year_user_info;

/*[上午 10:37:12][575 ms]*/ ALTER TABLE `merchant_sys_user` ADD COLUMN `status` TINYINT(3) DEFAULT 1 NOT NULL COMMENT '0 冻结 1 正常';



CREATE TABLE `out_platform`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `appid` VARCHAR(64) NOT NULL,
  `appsecret` VARCHAR(256) NOT NULL,
  `merchant_id` BIGINT(20) UNSIGNED NOT NULL,
  `created_time` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
);

ALTER TABLE `out_platform`
  ADD  INDEX `index_app` (`appid`);
ALTER TABLE `task_child_finished`
  ADD COLUMN `out_customer_id` VARCHAR(64) DEFAULT '' NOT NULL COMMENT '外部用户id' AFTER `barcode_id`;
ALTER TABLE `order`
  ADD COLUMN `out_customer_id` VARCHAR(32) DEFAULT '' NOT NULL COMMENT '第三方平台用户id' AFTER `carriage_sto_info`,
  ADD  INDEX `index_merchant` (`merchant_id`, `out_customer_id`);

ALTER TABLE `order`
  CHANGE `origin` `origin` VARCHAR(16) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '提交订单的源路径,CART购物车 IMPORT导入';

------------6-27----------------------
ALTER TABLE `payment` ADD COLUMN `pay_type` INT(4) UNSIGNED DEFAULT 1 NOT NULL COMMENT '1商品下单，2支付，3充值' AFTER `total_amount`;
ALTER TABLE `payment` ADD COLUMN `balance` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '金额' AFTER `pay_type`, ADD COLUMN `free_gold` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '使用金额' AFTER `balance`;
 ALTER TABLE `refund_record` ADD COLUMN `balance` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '余额' AFTER `update_time`, ADD COLUMN `free_gold` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '赠送金' AFTER `balance`;


 ALTER TABLE `payment` ADD COLUMN `refund_balance` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '已退款的余额' AFTER `free_gold`, ADD COLUMN `refund_free_gold` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '已退款的赠送金' AFTER `refund_balance`;

 ALTER TABLE `payment`
  ADD COLUMN `refund_total_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '退款的总金额' AFTER `refund_free_gold`;


ALTER TABLE `refund_record`
  ADD COLUMN `merchant_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '商户id' AFTER `free_gold`;

UPDATE  refund_record,merchant SET refund_record.`merchant_id` = merchant.`id` WHERE refund_record.`merchant_no` = merchant.`code` ;


CREATE TABLE `platform_good_order` (
  `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
  `no` varchar(255) NOT NULL DEFAULT '' COMMENT '订单号',
  `platform_goods_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
  `user_id` bigint(10) unsigned NOT NULL DEFAULT '0',
  `merchant_id` bigint(10) unsigned NOT NULL DEFAULT '0',
  `create_time` bigint(10) unsigned NOT NULL DEFAULT '0',
  `update_time` bigint(10) unsigned NOT NULL DEFAULT '0',
  `status` int(4) unsigned NOT NULL DEFAULT '1' COMMENT '1未付款 2支付成功 ，3-申请退款，4-退款成功，98取消 99删除',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际支付金额',
  `payment_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付id',
  `payment_method` varchar(100) NOT NULL DEFAULT '' COMMENT '支付方式',
  `product_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '平台产品原价',
  `platform_goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品名称',
  `value` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买个数',
  `activity_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '活动id',
  `usable_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户余额支付金额',
  `usable_free_gold` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用了多少赠送金',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `recharges_recode` (
  `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(10) unsigned NOT NULL DEFAULT '0',
  `merchant_id` bigint(10) unsigned NOT NULL DEFAULT '0',
  `balance` decimal(20,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '充值额',
  `free_gold` decimal(20,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '赠送金',
  `payment_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付id',
  `activity_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '活动id',
  `create_time` bigint(10) unsigned NOT NULL DEFAULT '0',
  `update_time` bigint(10) unsigned NOT NULL DEFAULT '0',
  `status` int(4) unsigned NOT NULL DEFAULT '1' COMMENT '1没支付2成功',
  `no` varchar(100) NOT NULL DEFAULT '',
  `type` int(4) unsigned NOT NULL DEFAULT '0' COMMENT '1支付宝2线下3平台',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `activity` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
  `appoint_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '指定金额',
  `free_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '送多少钱',
  `free_time` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '送多少次/0不限制',
  `begin_time` bigint(100) unsigned NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `end_time` bigint(100) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `status` int(4) unsigned NOT NULL DEFAULT '2' COMMENT '1启用/2不启用',
  `code` varchar(100) NOT NULL DEFAULT '' COMMENT '唯一code',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
CREATE TABLE `merchant_activity` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '商户id',
  `activity_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '活动id',
  `status` int(4) unsigned NOT NULL DEFAULT '2' COMMENT '1参与成功2没参与成',
  `user_id` bigint(10) unsigned NOT NULL DEFAULT '0',
  `activity_pay_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '1商品下单，2支付，3充值',
  `create_time` bigint(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4

ALTER TABLE `product` ADD COLUMN `bargain_status` TINYINT(3) DEFAULT 2 NULL COMMENT '1特价2不是' AFTER `color_opacity`;

ALTER TABLE `merchant` ADD COLUMN `balance` DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '余额' AFTER `is_insider`, ADD COLUMN `free_gold` DOUBLE(20,2) DEFAULT 0.00 NOT NULL COMMENT '赠送金' AFTER `balance`;

ALTER TABLE `order_item`
  ADD COLUMN `count` INT(10) UNSIGNED DEFAULT 0 NOT NULL AFTER `payment_id`,
  ADD COLUMN `category_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `count`;
ALTER TABLE `order_item`
  ADD COLUMN `parent_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `category_id`;


CREATE TABLE `merchant_bill` (
  `id` INT(20) NOT NULL AUTO_INCREMENT,
  `order_no` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '订单id',
  `user_id` INT(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `purpose` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '用途 1充值，2支付，3提现，4返现，5退款',
  `order_money` DECIMAL(20,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `account` VARCHAR(255) NOT NULL DEFAULT '',
  `total_money` DECIMAL(20,2) NOT NULL DEFAULT '0.00' COMMENT '账户总金额',
  `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `order_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '订单名称',
  `remarks` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
  `specific_purpose` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '具体用途',
  `merchant_id` INT(20) NOT NULL DEFAULT '0' COMMENT '商户id',
  `free_gold` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '免费金',
  PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;
CREATE TABLE `plan_statistics` (
  `id` INT(20) NOT NULL AUTO_INCREMENT,
  `count` INT(20) NOT NULL COMMENT '合成次数',
  `merchant_id` INT(20) NOT NULL COMMENT '合成用户',
  `merchant_platform_permission_id` INT(20) NOT NULL COMMENT '权限列表',
  `created_time` DATETIME NOT NULL COMMENT '创建时间',
  `unit` VARCHAR(30) NOT NULL COMMENT '单位',
  `type` TINYINT(1) NOT NULL COMMENT '1 消耗 2 返还(数据释放)',
  `del_flag` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0显示 1已经删除',
  `user_id` INT(20) NOT NULL,
  PRIMARY KEY (`id`)
)ENGINE=INNODB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;
CREATE TABLE `notification` (
  `id` INT(20) NOT NULL AUTO_INCREMENT,
  `type` TINYINT(1) NOT NULL COMMENT '1体现，2线下充值，3服务',
  `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `content` VARCHAR(255) NOT NULL COMMENT '内容',
  `remarks` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注',
  `merchant_id` INT(20) NOT NULL,
  `del_flag` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0未删除 1删除',
  `status` TINYINT(1) NOT NULL COMMENT '1被驳回（提现） 2通过（提现）  3平台主动开通会员套餐 4平台主动提高功能额度 5平台主动变更会员套餐 6年会员套餐只剩最后一个月 7 年会员套餐只剩24小时 8 月会员套餐只剩最后7天',
  `title` VARCHAR(255) NOT NULL COMMENT '标题',
  `is_read` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0未读 1已读',
  `user_id` INT(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=INNODB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;

ALTER TABLE `material` ADD COLUMN `length`  DECIMAL(20,2) DEFAULT 0.00 NOT NULL COMMENT '图片大小kb';
ALTER TABLE `image_upload` ADD COLUMN `length` DECIMAL(20,2) DEFAULT 0 NOT NULL COMMENT '图片大小kb' AFTER `create_uid`;
ALTER TABLE `merchant_bill` ADD COLUMN `total_free_gold` DECIMAL(10,2) DEFAULT 0.00 NOT NULL COMMENT '赠送金总额' AFTER `free_gold`


ALTER TABLE `carriage_no_recode` ADD COLUMN `error_info` VARCHAR(1000) DEFAULT '' NOT NULL COMMENT '错误信息记录' AFTER `no`;
ALTER TABLE `carriage_no_recode` ADD COLUMN `logistics_id` BIGINT(100) UNSIGNED DEFAULT 0 NOT NULL COMMENT '对应的渠道' AFTER `error_info`;
ALTER TABLE `platform_good_order` ADD COLUMN `num_period` BIGINT(100) DEFAULT 0 NOT NULL COMMENT '购买周期' AFTER `usable_free_gold`;
INSERT INTO `admin_configs` (`code`, `value`) VALUES ('activity_code', '{\"code\":\"pay_activity_2\"}');
INSERT INTO `admin_configs` (`code`, `value`) VALUES ('pay_password', '{\"password\":\"8dc5d546b4f7352eedaddbf7069512365f61c027\",\"salt\":\"ec7799e5e0ae078e\"}');





ALTER TABLE `compoun_group`
  ADD COLUMN `type` TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '1正常提交，2实时生成不需要任务同步' AFTER `combine_rule_type`;



UPDATE `compoun_group` SET TYPE = 1;


ALTER TABLE `task_child_layer`
  ADD COLUMN `fit_level` TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '素材匹配等级 越高越差' AFTER `src_filecode`;

  ALTER TABLE `task_child` ADD COLUMN `compound_json` TEXT NULL COMMENT '合成json' AFTER `compound_history_id`;


ALTER TABLE `task_child_layer`
  ADD COLUMN `design_data` TEXT NULL COMMENT '设计信息' AFTER `fit_level`;



  ALTER TABLE `activity` ADD COLUMN `content` TEXT NULL COMMENT '活动内容' AFTER `code`;

UPDATE `activity` SET `content` = '[\r\n    {\r\n        \"appoint_amount\": 5000,\r\n        \"free_amount\": 50\r\n    },\r\n    {\r\n        \"appoint_amount\": 10000,\r\n        \"free_amount\": 150\r\n    },\r\n    {\r\n        \"appoint_amount\": 50000,\r\n        \"free_amount\": 1000\r\n    },\r\n    {\r\n        \"appoint_amount\": 100000,\r\n        \"free_amount\": 2500\r\n    }\r\n]' WHERE `id` = '1';


/*[下午 16:15:57][125 ms]*/ UPDATE `sds_product`.`activity` SET `name` = '充值活动' , `free_time` = '100' , `begin_time` = '1561910400000' , `end_time` = '1565452799000' , `code` = 'pay_activity_1' , `content` = '[\r\n    {\r\n        \"appoint_amount\": 5000,\r\n        \"free_amount\": 50\r\n    },\r\n    {\r\n        \"appoint_amount\": 10000,\r\n        \"free_amount\": 150\r\n    },\r\n    {\r\n        \"appoint_amount\": 50000,\r\n        \"free_amount\": 1000\r\n    },\r\n    {\r\n        \"appoint_amount\": 100000,\r\n        \"free_amount\": 2500\r\n    }\r\n]' WHERE `id` = '1';
