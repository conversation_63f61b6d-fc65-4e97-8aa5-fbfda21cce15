ALTER TABLE `order`
  ADD COLUMN `refund_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '退款金额' AFTER `carriage_free_gold`,
  ADD COLUMN `refund_product_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '退款产品金额' AFTER `refund_amount`,
  ADD COLUMN `refund_carriage_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '退款物流金额' AFTER `refund_product_amount`;


ALTER TABLE `logistic`
  ADD COLUMN `refund_percent` TINYINT(3) DEFAULT 0 NOT NULL COMMENT '退款要收的手续费' AFTER `warehouse_carrier_service`,
  ADD COLUMN `refund_reset_num` TINYINT(3) DEFAULT 0 NOT NULL COMMENT '退款后是否重新生成运单号' AFTER `refund_percent`;


ALTER TABLE `order`
  ADD COLUMN `current_carriage_amount` DECIMAL(20,2) UNSIGNED DEFAULT 0.00 NOT NULL COMMENT '退款后的运费' AFTER `carriage_amount`;

UPDATE `order` SET current_carriage_amount = carriage_amount ;

 ALTER TABLE `order_sync_history` ADD COLUMN `logistics_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '物流id' AFTER `pdf`;
-- ======================== zjl start ===========================
ALTER TABLE `order` ADD COLUMN `copy_order_id` BIGINT(20) DEFAULT 0 NOT NULL COMMENT '复制的订单ID' AFTER `add_warning_date`;

-- 权限优化
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`, `extend_type`, `show_flag`, `manage_flag`, `permission_type`) VALUES (NULL, '198', '0,1,196,198,', '管理', '1', '', NULL, '', '0', 'import_order:view:all', '2019-05-23 13:40:51', NULL, '0', '1', '1', '1', '201', '1', '0');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`, `extend_type`, `show_flag`, `manage_flag`, `permission_type`) VALUES (NULL, '221', '0,1,220,221', '管理', '1', '', NULL, '', '0', 'sell_collect:view:all', '2019-05-23 13:40:51', NULL, '0', '1', '1', '1', '201', '1', '0');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`, `extend_type`, `show_flag`, `manage_flag`, `permission_type`) VALUES (NULL, '222', '0,1,220,222', '管理', '1', '', NULL, '', '0', 'sell_detail:view:all', '2019-05-23 13:40:51', NULL, '0', '1', '1', '1', '201', '1', '0');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`, `extend_type`, `show_flag`, `manage_flag`, `permission_type`) VALUES (NULL, '223', '0,1,220,223', '管理', '1', '', NULL, '', '0', 'design_collect:view:all', '2019-05-23 13:40:51', NULL, '0', '1', '1', '1', '201', '1', '0');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`, `extend_type`, `show_flag`, `manage_flag`, `permission_type`) VALUES (NULL, '224', '0,1,220,224', '管理', '1', '', NULL, '', '0', 'resource_rank:view:all', '2019-05-23 13:40:51', NULL, '0', '1', '1', '1', '201', '1', '0');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`, `role_office_permission_type`, `extend_type`, `show_flag`, `manage_flag`, `permission_type`) VALUES (NULL, '225', '0,1,220,225', '管理', '1', '', NULL, '', '0', 'carriage_no_collect:view:all', '2019-05-23 13:40:51', NULL, '0', '1', '1', '1', '201', '1', '0');

-- 个人店铺处理
UPDATE merchant_store s SET s.`name` = CONCAT((SELECT u.`username` FROM merchant_sys_user u WHERE u.id = s.creator_id),'个人店铺'),
s.merchant_sys_offices_id = ((SELECT u.office_id FROM merchant_sys_user u WHERE u.id = s.creator_id))
WHERE is_system = 1;
-- 个人店铺处理后结果查看
SELECT * FROM merchant_store s WHERE is_system = 1;

INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 231, rm.role_office_permission FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 231 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 230, rm.role_office_permission FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 230 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 229, rm.role_office_permission FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 229 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 228, rm.role_office_permission FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 228 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 227, rm.role_office_permission FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 227 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 226, rm.role_office_permission FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 198 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 226 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;

INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 225, 1 FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 225 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 224, 1 FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 224 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 223, 1 FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 223 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 222, 1 FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 222 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 221, 1 FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 221 AND rmi.role_id = r.id) = 0 AND rm.role_id IS NOT NULL GROUP BY r.id;
INSERT INTO merchant_sys_role_menu (role_id, menu_id, role_office_permission) SELECT r.id, 198, 1 FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND rm.role_id IS NOT NULL
AND (SELECT COUNT(1) FROM merchant_sys_role_menu rmi WHERE rmi.menu_id = 198 AND rmi.role_id = r.id) = 0
GROUP BY r.id;

UPDATE merchant_sys_role_menu rmo RIGHT JOIN (SELECT r.id, rm.role_office_permission p FROM merchant_sys_role r LEFT JOIN merchant_sys_role_menu rm ON rm.role_id = r.id AND rm.menu_id = 201 WHERE r.role_type IS NULL AND rm.role_id IS NOT NULL GROUP BY r.id ) re ON rmo.role_id = re.id
SET rmo.role_office_permission = re.p
WHERE rmo.menu_id = 226;

-- ========================= zjl end ===========================



ALTER TABLE `order`
  ADD COLUMN `refund_free_gold` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '退款的赠送金' AFTER `refund_carriage_amount`;

  ALTER TABLE `order`
  ADD COLUMN `refund_balance` DECIMAL(20,2) UNSIGNED DEFAULT 0 NOT NULL COMMENT '退款余额' AFTER `refund_carriage_amount`;



UPDATE
  `order` o,
  (SELECT
    SUM(r.`total_price`) refund_product_amount,
    SUM(r.`carriage_price`) refund_carriage_amount,
    SUM(r.`refund_price`) refund_amount,
    SUM(r.`balance`) refund_balance,
    SUM(r.`free_gold`) refund_free_gold,
    r.`no`
  FROM
    `refund_record` r
  WHERE r.`status` = 2
  GROUP BY r.`no`) t
SET
  o.`refund_amount` = t.`refund_amount`,
  o.`refund_balance` = t.`refund_balance`,
  o.`refund_carriage_amount` = t.`refund_carriage_amount`,
  o.`refund_free_gold` = t.`refund_free_gold`,
  o.`refund_product_amount` = t.`refund_product_amount`
WHERE o.`no` = t.no;


