
ALTER TABLE `sds_test`.`order` ADD COLUMN `accomplish_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '订单全部质检完成时间' AFTER `prepaid_no`, ADD COLUMN `upload_laber_time` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '用户上传面单的时间' AFTER `accomplish_time`;


ALTER TABLE `sds_test`.`order_item`
  ADD COLUMN `compensation_status` VARCHAR(8) DEFAULT 'close' NOT NULL COMMENT '增加赔付状态 compensationStatus open开启 close 关闭' AFTER `fba_update`;


CREATE TABLE `sds_test`.`export_history_product`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `export_history_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '导出id',
  `product_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品id',
  `product_parent_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '产品母体id',
  `product_color_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '产品颜色',
  `product_size` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '产品尺寸',
  PRIMARY KEY (`id`)
);


