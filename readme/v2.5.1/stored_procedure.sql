-- --------------------------------------初化始老板
DELIMITER $$

USE `sds_saas_dev`$$

DROP PROCEDURE IF EXISTS `init_merchant_boss_user`$$

CREATE DEFINER=`root`@`%` PROCEDURE `init_merchant_boss_user`(IN merchant_id_in BIGINT, IN merchant_phone VARCHAR(50))
BEGIN
	DECLARE user_id_p BIGINT DEFAULT NULL;
	DECLARE username_p VARCHAR(50);
	DECLARE role_id BIGINT;
	DECLARE flag INT DEFAULT 0;
	DECLARE i INT DEFAULT 0; -- 循环下标
	DECLARE v_done INT DEFAULT 0; -- 游标退出标志
	DECLARE v_uid INT;
	DECLARE v_uname VARCHAR(50); -- 游标退出标志
	DECLARE cur_t_user CURSOR FOR (SELECT id, username FROM merchant_sys_user m WHERE m.merchant_id = merchant_id_in AND m.del_flag = 0); -- 声明游标
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = 1; -- 声明游标退出时动作

	#临时表中存在则不再处理
	SELECT i.user_id INTO user_id_p FROM init_merchant_boss_record i WHERE i.merchant_id = merchant_id_in;
	IF user_id_p IS NULL THEN
		SET user_id_p = NULL;
		#添加boss角色
		INSERT INTO merchant_sys_role (`name`, role_type, 	data_scope, useable, create_date, update_date, remarks, del_flag, merchant_id)
					VALUES('老板', 'boss', 		'8', 		1, 	NOW(), 		NOW(), 'old data init boss', 0, merchant_id_in);
		SELECT LAST_INSERT_ID() INTO role_id;
		#select concat('role_id:',role_id);

		#找到指定boss用户
		OPEN cur_t_user; -- 打开游标
		loop_user:LOOP -- 循环
		  FETCH cur_t_user INTO v_uid,v_uname; -- 遍历
			#set i=i+1;
			IF v_done = 1 THEN
				LEAVE loop_user;
			END IF;
			IF v_uname <> 'root' AND v_uname = merchant_phone THEN
				UPDATE merchant_sys_user_role SET role_id = role_id WHERE user_id = v_uid;
				INSERT INTO init_merchant_boss_record (user_id, merchant_id, username)VALUE(v_uid, merchant_id_in, v_uname);
				SET flag = 1;
				LEAVE loop_user;
			END IF;
		END LOOP;
		CLOSE cur_t_user; -- 关闭游标
		#没有商户手机号用户则默认第二个用户
		IF flag = 0 THEN
			SELECT id, username INTO user_id_p,username_p FROM merchant_sys_user m WHERE m.merchant_id = merchant_id_in AND m.del_flag = 0 ORDER BY id LIMIT 1,1;
			IF user_id_p IS NOT NULL THEN
				DELETE FROM merchant_sys_user_role WHERE user_id = user_id_p;
				INSERT INTO merchant_sys_user_role (role_id,user_id) VALUE(role_id,user_id_p);
				INSERT INTO init_merchant_boss_record (user_id, merchant_id, username)VALUE(user_id_p, merchant_id_in, username_p);
				SET flag = 1;
			END IF;
		END IF;
		#没有未删除用户直接用第二条删除用户
		IF flag = 0 THEN
			SELECT id, username INTO user_id_p,username_p FROM merchant_sys_user m WHERE m.merchant_id = merchant_id_in ORDER BY id LIMIT 1,1;
			IF user_id_p IS NOT NULL THEN
				DELETE FROM merchant_sys_user_role WHERE user_id = user_id_p;
				INSERT INTO merchant_sys_user_role (role_id,user_id) VALUE(role_id,user_id_p);
				UPDATE merchant_sys_user SET del_flag = 0 WHERE id = user_id_p;
				INSERT INTO init_merchant_boss_record (user_id, merchant_id, username)VALUE(user_id_p, merchant_id_in, username_p);

			ELSE
				DELETE FROM merchant_sys_role WHERE id = role_id;
			END IF;
		END IF;
	END IF;

    END$$

DELIMITER ;

-- ----------------------------------------初化始老板start入口
DELIMITER $$

USE `sds_saas_dev`$$

DROP PROCEDURE IF EXISTS `init_merchant_boss_user_start`$$

CREATE DEFINER=`root`@`%` PROCEDURE `init_merchant_boss_user_start`()
BEGIN
	DECLARE v_done INT DEFAULT 0; -- 游标退出标志
	DECLARE v_uid INT;
	DECLARE v_uname VARCHAR(50); -- 游标退出标志
	DECLARE cur_t_user CURSOR FOR (SELECT id, contact_tel FROM merchant m); -- 声明游标
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = 1; -- 声明游标退出时动作

	CREATE TEMPORARY TABLE IF NOT EXISTS init_merchant_boss_record
	( 	user_id BIGINT NOT NULL,
		merchant_id BIGINT NOT NULL,
		username VARCHAR(50) NOT NULL
	);

	OPEN cur_t_user; -- 打开游标
	loop_user:LOOP -- 循环
	  FETCH cur_t_user INTO v_uid,v_uname; -- 遍历
		IF v_done = 1 THEN
			LEAVE loop_user;
		END IF;
		CALL init_merchant_boss_user(v_uid, v_uname);
	END LOOP;
	CLOSE cur_t_user; -- 关闭游标
	SELECT id,`name` AS not_dispose_merchant FROM merchant m WHERE m.id NOT IN (SELECT merchant_id FROM init_merchant_boss_record);
	SELECT * FROM init_merchant_boss_record;
    END$$

DELIMITER ;