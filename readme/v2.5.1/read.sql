ALTER TABLE `order` ADD INDEX `index_customer_id` (`customer_id`);
ALTER TABLE `order_item` ADD INDEX `index_order_id` (`order_id`);
ALTER TABLE `refund_record` ADD INDEX `index_no` (`no`);
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '197', '0,1,196,197,', '查看所有人订单', '1', '', NULL, '', '0', 'order:view:all', '2019-05-23 13:40:51', NULL, '0', '1');
INSERT INTO `merchant_sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_date`, `remarks`, `del_flag`, `type`) VALUES (NULL, '197', '0,1,196,197,', '查看', '1', '', NULL, '', '0', 'order:view', '2019-05-23 13:40:51', NULL, '0', '1');
ALTER TABLE `product`
  ADD COLUMN `size_sort` TINYINT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT 'size排序' AFTER `seqence`,
ALTER TABLE `sds_saas_test`.`product`
  ADD COLUMN `size_sort` TINYINT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT 'size排序' AFTER `seqence`,
  ADD COLUMN `color_sort` TINYINT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT 'color排序' AFTER `size_sort`;  ADD COLUMN `color_sort` TINYINT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT 'color排序' AFTER `size_sort`;
CREATE TABLE `is_tip`(
	`id` int(20) NOT NULL  auto_increment ,
	`user_id` int(20) NOT NULL  DEFAULT 0 ,
	`is_tip` tinyint(3) unsigned zerofill NOT NULL  DEFAULT 000 COMMENT '0不提示  1提示' ,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_general_ci';

CREATE TABLE `announcement_type`(
	`id` int(20) NOT NULL  auto_increment ,
	`announcement_type` varchar(100) COLLATE utf8mb4_general_ci NOT NULL  COMMENT '公告类型' ,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_general_ci';

CREATE TABLE `announcement`(
	`title` varchar(255) COLLATE utf8mb4_general_ci NOT NULL  COMMENT '公告标题' ,
	`id` int(20) NOT NULL  auto_increment ,
	`announcement_type_id` int(20) NOT NULL  DEFAULT 0 COMMENT '公告类型' ,
	`content` mediumtext COLLATE utf8mb4_general_ci NOT NULL  COMMENT '公告内容' ,
	`created_time` bigint(20) NOT NULL  COMMENT '公告创建时间' ,
	`updated_time` bigint(20) unsigned zerofill NULL  DEFAULT 00000000000000000000 COMMENT '公告更新时间' ,
	`start_display` bigint(20) NOT NULL  COMMENT '开始展示时间' ,
	`end_display` bigint(20) NOT NULL  COMMENT '展示结束时间' ,
	`del_flag` tinyint(1) unsigned zerofill NOT NULL  COMMENT '0为未删除，1为删除' ,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET='utf8mb4' COLLATE='utf8mb4_bin';