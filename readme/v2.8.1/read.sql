-- ================== zjl start ========================
ALTER TABLE `country_express_info`
ADD COLUMN `first_priority_cost` DECIMAL(20,2) DEFAULT 0 NOT NULL COMMENT '首重收费金额' AFTER `price`,
ADD COLUMN `minus_first_priority` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '阶梯收费-是否减去首重 1 是 0 否' AFTER `first_priority_cost`,
ADD COLUMN `rule_weight` TINYINT(2) DEFAULT 1 NOT NULL COMMENT '重量计算规则 0 区间最大重量 1 实际重量 2 取整' AFTER `minus_first_priority`,
ADD COLUMN `rule_weight_round` DECIMAL(20,2) DEFAULT 0 NOT NULL COMMENT '重量计算规则-取整值' AFTER `rule_weight`;

ALTER TABLE `order_import_record_order` ADD COLUMN `urgent_express` TINYINT(1) DEFAULT 0 NOT NULL COMMENT '特快（加急）' AFTER `earliest_ship_time`;
-- ==================  zjl end  ========================





CREATE TABLE `merchant_product_parent_count`(
  `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `merchant_product_parent_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `merchant_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `user_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0,
  `compose_count` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '合成次数',
  PRIMARY KEY (`id`)
) ENGINE=INNODB CHARSET=utf8mb4;


ALTER TABLE `merchant_product_parent_count`
  ADD COLUMN `create_time` BIGINT(20) UNSIGNED DEFAULT 0  NOT NULL AFTER `compose_count`,
  ADD COLUMN `update_time` BIGINT(20) DEFAULT 0  NOT NULL AFTER `create_time`;
/*[上午 10:25:40][93 ms]*/ ALTER TABLE `merchant_product_parent_count` ADD COLUMN `product_id` BIGINT(20) UNSIGNED DEFAULT 0 NOT NULL AFTER `update_time`;


  ALTER TABLE `order` ADD COLUMN `express_type` TINYINT(4) DEFAULT 0 NOT NULL COMMENT '特快0不是1是' AFTER `order_import_record_order_id`;

 ALTER TABLE `prototype` DROP COLUMN `format`, ADD COLUMN `format` VARCHAR(20) NULL AFTER `lens_id`;

  /*[下午 15:53:56][480 ms]*/ ALTER TABLE `logistic` ADD COLUMN `express_status` TINYINT(4) UNSIGNED DEFAULT 2 NOT NULL COMMENT '1特快2不是' AFTER `min_add_weight`;

ALTER TABLE `logistic` CHANGE `code` `code_id` VARCHAR(100) CHARSET utf8 COLLATE utf8_general_ci DEFAULT '' NULL COMMENT '产品代码';
