
UPDATE `order` o set o.origin_id = o.order_import_record_order_id WHERE o.order_import_record_order_id > 0 and o.origin_id = 0 ;

	UPDATE `order` o set o.origin_id = order_import_record_order_fba_id WHERE o.order_import_record_order_fba_id > 0 and o.origin_id = 0 ;


-- 初始化关联信息
INSERT INTO `order_item_import_product` (
  `id`,
  `merchant_store_id`,
  `platform_code`,
  `site`,
  `num`,
  `import_sku`,
  `import_product_name`,
  `import_color`,
  `import_size`,
  `import_imgs`,
   `import_asin`,
  `hicustom_sku`,
  `import_asin_parent`
)
SELECT
oi.id,
	o.merchant_store_id,
	o.merchant_store_platform_code,
	o.site,
	i.num,
	i.import_sku,
	i.import_product_name,
	i.import_color,
	i.import_size,
	if(i.import_imgs is null ,"",i.import_imgs),
	import_asin,
	i.hicustom_sku,
	i.import_asin_parent
FROM
	`order` o,
	order_item oi,
	order_import_record_item i
WHERE
	oi.order_import_item_id = i.id
	AND o.id = oi.order_id;


	-- 初始化关联信息
INSERT INTO `order_item_import_product` (
  `id`,
  `merchant_store_id`,
  `platform_code`,
  `site`,
  `num`,
  `import_sku`,
  `import_product_name`,
  `import_color`,
  `import_size`,
  `import_imgs`,
   `import_asin`,
  `hicustom_sku`
)
SELECT
	oi.id,
	o.merchant_store_id,
	o.merchant_store_platform_code,
	o.site,
	i.num,
	i.import_sku,
	i.import_product_name,
	i.import_color,
	i.import_size,
	if(i.import_imgs IS NULL, "", i.import_imgs),
		import_asin,
		i.hicustom_sku
	FROM
		`order` o,
		order_item oi,
		order_import_record_order_fba_item i
	WHERE
		oi.order_import_fba_item_id = i.id AND o.id = oi.order_id
	;


UPDATE
	order_item oi
SET
	oi.origin_id = oi.order_import_fba_item_id
WHERE
	oi.origin_id = 0
	AND oi.order_import_fba_item_id > 0 ;


	INSERT INTO logistics_service_amount (logistics_id, amount_code, amount)
SELECT t.id,"fba_box",18 FROM sds_test.logistic t WHERE t.service_amount in(18) ;


INSERT INTO logistics_service_amount (logistics_id, amount_code, amount)
SELECT t.id,"take_self",2.8 FROM sds_test.logistic t WHERE t.service_amount in(20.8) ;


INSERT ignore INTO `sds_design`.`design_product_import_sku_rel` (
  `design_product_id`,
  `merchant_id`,
  `import_sku`,
  `import_asin`,
  `key_id`,
  `hicustom_sku`,
  `import_asin_parent`
)

SELECT
	i.end_product_id,ms.merchant_id,i.import_sku,i.import_asin,i.key_id,i.hicustom_sku,i.import_asin_parent
FROM
	sds_test.order_import_record_item i,
	sds_test.order_import_record_order o,
	sds_test.merchant_store ms
WHERE
ms.id = o.merchant_store_id and
	i.end_product_id > 0 and o.id = i.order_import_record_order_id order by i.id desc



	UPDATE
	`order` t,
	merchant_store ms set t.merchant_store_platform_code = ms.merchant_store_platform_code
WHERE
	t.merchant_store_platform_code = "" and ms.id = t.merchant_store_id ;

	SELECT
	o.cancel_remark,o.*
FROM
	`order` o ,order_import_record_order o2
WHERE
	o.status = 98 and o.origin_id = o2.id and o2.is_delete= 1
	AND o.cancel_remark = "已自动取消" LIMIT 1000;

INSERT INTO `sds_product`.`order_import_extra_info` (
  `id`,
  `fba_type`,
  `buyer_id`,
  `address_type`,
  `express_type`,
  `client_selected_logistics`,
  `logistics_amount`,
  `amount`,
  `currency_code`,
  `exchange_rate`
)
SELECT
o.id,
	io.fba_type,
	io.buyer_id,
	io.address_type,
	io.urgent_express,
	io.client_selected_logistics,
	o.out_logistics_amount,
	o.out_total_amount,
	o.out_currency_code,
	o.exchange_rate
FROM
	`order` o,
	order_import_record_order io
WHERE
	io.id = o.order_import_record_order_id
ORDER BY
	io.id desc ;


update order_import_extra_info t set t.address_type = "" WHERE t.address_type = "0"

update order_import_extra_info t set t.address_type = "Commercial" WHERE t.address_type = "1"

update order_import_extra_info t set t.address_type = "Residential" WHERE t.address_type = "2"

UPDATE order_import_extra_info t set t.fba_type ="UNKNOWN" WHERE t.fba_type = 0 order by id desc

UPDATE order_import_extra_info t set t.fba_type ="FBM" WHERE t.fba_type = 2 order by id desc

UPDATE order_import_extra_info t set t.fba_type ="FBA" WHERE t.fba_type = 1 order by id desc

	 UPDATE `order` o ,order_item oi ,order_item_import_product t set t.platform_code = o.merchant_store_platform_code WHERE o.id= oi.order_id and oi.id = t.id and t.platform_code = "" ;


	 UPDATE
	order_item oi,product t set oi.img_url = t.blank_design_url,oi.imgs = CONCAT("[\"",t.blank_design_url,"\"]")
WHERE
	oi.product_id > 0 and oi.product_id = t.id and oi.factory_id = 0
	AND oi.end_product_id = 0
	AND oi.img_url NOT LIKE "http://static.nvzz.com%" ;



	UPDATE `sds_test`.`amazon_template_content_dictionary` SET `text_type` = 2, `content` = '<variation_theme>,Size,Color,colorsize,sizename,colorname,SizeName-ColorName' WHERE `id` = 190;