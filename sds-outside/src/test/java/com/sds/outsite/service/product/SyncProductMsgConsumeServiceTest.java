package com.sds.outsite.service.product;

import com.alibaba.fastjson.JSON;
import com.sds.outsite.SdsOutsiteApplication;
import com.sdsdiy.ecommercedata.bo.OutsiteSyncProductMsg;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {SdsOutsiteApplication.class})
class SyncProductMsgConsumeServiceTest {

    @Autowired
    private SyncProductMsgConsumeService syncProductMsgConsumeService;

    @Test
    void consume() {
        String msgContent = "{\"productId\":\"8558835171556\",\"publishedAt\":1734594804000,\"publishedVariantIds\":[\"45889735393508\"],\"shopName\":\"cs-test-test\",\"currencySymbol\":\"CNY\",\"uuid\":\"48e7046e-84bc-4cef-8637-943792e7ad0c\",\"platform\":\"shopify\",\"token\":\"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\",\"createTime\":\"2024-012-19T15:55:16.994\",\"currencyCode\":\"CNY\"}";
        msgContent = "{\"createTime\":\"2025-04-23T00:16:02.435\",\"currencyCode\":\"USD\",\"currencySymbol\":\"US$\",\"platform\":\"shopify\",\"productId\":\"8065827635394\",\"publishedAt\":1745127275000,\"publishedVariantIds\":[\"45066748592322\",\"45066746593474\",\"45066746953922\",\"45066747805890\",\"45066748854466\",\"45066747543746\",\"45066747183298\",\"45066748133570\",\"45066747281602\",\"45066747740354\",\"45066748068034\",\"0\",\"45066748330178\",\"45066748395714\",\"45066746167490\",\"45066746069186\",\"45066746003650\",\"45066747904194\",\"45066747379906\",\"45066746790082\",\"45066747314370\",\"45066746626242\",\"45066747216066\",\"45066746888386\",\"45066745938114\",\"45066748657858\",\"45066746724546\",\"45066747510978\",\"45066746691778\",\"45066746331330\",\"45066747642050\",\"45066748362946\",\"45066748821698\",\"45066746527938\",\"45066747838658\",\"45066745741506\",\"45066745807042\",\"45066747117762\",\"45066746396866\",\"45066748428482\",\"45066748166338\",\"45066745708738\",\"45066745872578\",\"45066748199106\",\"45066746495170\",\"45066747576514\",\"45066746560706\",\"45066745643202\",\"45066746822850\",\"45066748002498\",\"45066747412674\",\"45066747674818\",\"45066745905346\",\"45066748723394\",\"45066748461250\",\"45066747609282\",\"45066746233026\",\"45066746101954\",\"45066748100802\",\"45066747936962\",\"45066747347138\",\"45066748526786\",\"45066746036418\",\"45066746757314\",\"45066746298562\",\"45066748788930\",\"45066747248834\",\"45066747969730\",\"45066746265794\",\"45066748559554\",\"45066745675970\",\"45066746986690\",\"45066747150530\",\"45066747871426\",\"45066748625090\",\"45066746855618\",\"45066748756162\",\"45066745774274\",\"45066748494018\",\"45066747773122\",\"45066746462402\",\"45066746364098\",\"45066746659010\",\"45066745970882\",\"45066747052226\",\"45066747445442\",\"45066748035266\",\"45066748690626\",\"45066747084994\",\"45066745839810\",\"45066746134722\",\"45066746429634\",\"45066748297410\",\"45066747478210\",\"45066747019458\",\"45066745610434\",\"45066746200258\",\"45066748231874\",\"45066746921154\",\"45066747707586\",\"45066748264642\"],\"shopName\":\"hageuj-8w\",\"token\":\"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\",\"uuid\":\"ec6ff0ac-ee1d-4f32-bace-bdca6492aff7\"}";
        OutsiteSyncProductMsg msg = JSON.parseObject(msgContent, OutsiteSyncProductMsg.class);
        syncProductMsgConsumeService.consume(msg);
    }
}
