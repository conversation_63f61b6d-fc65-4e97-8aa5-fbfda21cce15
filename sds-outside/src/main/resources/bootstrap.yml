spring:
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_SERVER:nacos-go.sdspod.com:80}   #配置中心地址
        file-extension: properties  #后缀名
        username: ${NACOS_USERNAME:read-test}
        name: application-out
        password: ${NACOS_PASSWORD:FYEbMh6UpHnq3wI9}
        namespace: ${NACOS_NAMESPACE:ebb84f52-e442-407a-80a0-6c370d406d5b}  #命名空间的id
        extension-configs[0]:
          data-id: application-out_${NACOS_ENV:test}.properties
          group: DEFAULT_GROUP
          refresh: true

sentry:
  dsn: http://f012ff8497904b12976dcce87c85a7b6@54.223.160.11:9000/2
hystrix:
  command:
    MerchantPlatformPermissionSetMealFeign#oldData(Long,GenSetMealParamReqDto):
      circuitBreaker:
        enabled: false
      execution:
        timeout:
          enabled: false

    AliexpressProductFeign#postProduct(AliexpressPostProductDTO):
      execution:
        timeout:
          enabled: false

    OrderCarriageFeign#applyCarriageNo(ApplyCarriageNoParam):
      circuitBreaker:
        enabled: false
      execution:
        timeout:
          enabled: false
    EtsyReceiptFeign#batchGetReceiptJson(EtsyBatchQueryV3ReceiptJsonParam):
      circuitBreaker:
        enabled: false
      execution:
        timeout:
          enabled: false
    BarcodeFeign#findBarcodeUsable(BarcodeUsableReqDTO):
      circuitBreaker:
        enabled: false
      execution:
        timeout:
          enabled: false

    OrderCarriageFeign#postList(BaseListReqDto):
      circuitBreaker:
        enabled: false
      execution:
        timeout:
          enabled: false
#    PrototypeGroupFeign#addAndBind(PrototypeGroupDto):
#      circuitBreaker:
#        enabled: false
#      execution:
#        timeout:
#          enabled: false
#    PrototypeGroupFeign#update(PrototypeGroupDto):
#      circuitBreaker:
#        enabled: false
#      execution:
#        timeout:
#          enabled: false
#    PrototypeRequire#addAndBind(PrototypeGroupDto):
#      circuitBreaker:
#        enabled: false
#      execution:
#        timeout:
#          enabled: false

    default:  #default全局有效，service id指定应用有效
      circuitBreaker:
        enabled: false
      execution:
        timeout:
          #是否开启超时熔断
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 30000 #断路器超时时间，默认1000ms
ribbon:
  ConnectTimeout: 5000
  ReadTimeout: 30000

seat_config_condition: false
outeco:
  domain:
    us: http://sds-outeco:9020 #随便配，反正不调用，只是为了能启动服务