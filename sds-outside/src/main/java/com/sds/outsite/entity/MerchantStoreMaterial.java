package com.sds.outsite.entity;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * 商家店铺素材表(MerchantStoreMaterial)实体类
 *
 * <AUTHOR>
 * @since 2020-11-24 09:51:24
 */
@Data
public class MerchantStoreMaterial implements Serializable {
    private static final long serialVersionUID = -17133789652277696L;

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    /**
     * 商家id
     */
    private Long merchantId;

    /**
     * 商家店铺id
     */
    private Long merchantStoreId;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * 店铺素材分类id
     */
    private Long merchantStoreMaterialCategoryId;

    private Integer isDelete;
    private Long createUid;
    private Long updateUid;
    private Date createTime;
    private Date updateTime;

}