package com.sds.outsite.entity;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * 店铺素材分类表(MerchantStoreMaterialCategory)实体类
 *
 * <AUTHOR>
 * @since 2020-11-24 09:52:17
 */
@Data
public class MerchantStoreMaterialCategory implements Serializable {
    private static final long serialVersionUID = -23919723485190612L;
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    /**
     * 商家id
     */
    private Long merchantId;

    /**
     * 商家店铺id
     */
    private Long merchantStoreId;

    /**
     * 素材分类名称
     */
    private String name;

    /**
     * 1系统创建 2.用户创建
     */
    private Integer type;

    /**
     * 素材数量
     */
    private Integer num;


    private Long createUid;
    private Long updateUid;
    private Date createTime;
    private Date updateTime;
    private Integer isDelete;

}