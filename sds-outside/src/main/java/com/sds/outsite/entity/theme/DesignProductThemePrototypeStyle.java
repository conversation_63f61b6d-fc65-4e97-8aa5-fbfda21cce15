package com.sds.outsite.entity.theme;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/30
 */
@Data
public class DesignProductThemePrototypeStyle implements Serializable {

    private static final long serialVersionUID = -3535593423523445L;

    @Id
    private Long id;
    private Long styleId;
    private String styleName;
    private Integer sort;
    private Long themePrototypeId;
    private String themeName;
    private String fabricJson;
    private String formJson;
    private String frontPic;
    private Integer version;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @ApiModelProperty("成品图层id")
    private Long designProductLayerId;
    @ApiModelProperty("风格选中状态")
    private Integer selected;
    @ApiModelProperty("商户id")
    private Long merchantId;
    private String compoundId;
    private Long prototypeLayerId;
    private Long themeTimeSign;
}
