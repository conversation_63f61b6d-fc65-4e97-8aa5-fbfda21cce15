/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.validator.constraints.Length;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class DesignProductImage implements Comparable<DesignProductImage> {
	
	
	//可以直接使用: @Length(max=50,message="用户名长度不能大于50")显示错误消息
	//columns START
	@JsonProperty("id")
    
	@Id
	@GeneratedValue(generator = "JDBC")
	private Long id;
	@JsonProperty("compound_id")
    @NotBlank
	private String compoundId;
	@JsonProperty("psd_file_code")
    @NotBlank @Length(max=255)
	private String psdFileCode;
	@JsonProperty("sort")
    @NotNull
	private Long sort;
	@JsonProperty("merchant_id")
	@Id
	private Long merchantId;

	@JsonProperty("img_url")
    @NotBlank
	private String imgUrl;
	private String imgType;
	@JsonProperty("design_md5")
    @NotBlank @Length(max=64)
	private String designMd5;
	@JsonProperty("created_time")
    @NotNull
	private Long createdTime;
	@JsonProperty("updated_time")
    @NotNull
	private Long updatedTime;
	private Long psdSort;
	//columns END


	public String getImgType() {
		return imgType;
	}

	public void setImgType(String imgType) {
		this.imgType = imgType;
	}

	public Long getPsdSort() {
		return psdSort;
	}

	public void setPsdSort(Long psdSort) {
		this.psdSort = psdSort;
	}

	public DesignProductImage(){
	}

	public DesignProductImage(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}

	public Long getId() {
		return this.id;
	}
	public void setCompoundId(String value) {
		this.compoundId = value;
	}

	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	public String getCompoundId() {
		return this.compoundId;
	}
	public void setPsdFileCode(String value) {
		this.psdFileCode = value;
	}

	public String getPsdFileCode() {
		return this.psdFileCode;
	}
	public void setSort(Long value) {
		this.sort = value;
	}

	public Long getSort() {
		return this.sort;
	}


	public void setImgUrl(String value) {
		this.imgUrl = value;
	}

	public String getImgUrl() {
		return this.imgUrl;
	}
	public void setDesignMd5(String value) {
		this.designMd5 = value;
	}

	public String getDesignMd5() {
		return this.designMd5;
	}
	public void setCreatedTime(Long value) {
		this.createdTime = value;
	}

	public Long getCreatedTime() {
		return this.createdTime;
	}
	public void setUpdatedTime(Long value) {
		this.updatedTime = value;
	}

	public Long getUpdatedTime() {
		return this.updatedTime;
	}


	@Override
	public int compareTo(DesignProductImage o) {
		long res = this.sort - o.getSort();
		if(res == 0L){
			res = this.getPsdSort() - o.getPsdSort();
		}
		int s = 0;
		if(res > 0){
			s = 1;
		}else if(res < 0){
			s = -1;
		}else{
			s = 0;
		}
		return s;	}
}

