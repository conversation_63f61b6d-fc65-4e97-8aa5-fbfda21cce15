package com.sds.outsite.entity.theme;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;

/**
 * 成品主题关联表
 * <AUTHOR>
 * @date 2021/12/24
 */
@Data
public class DesignProductLayerThemeRel implements Serializable {

    private static final long serialVersionUID = -35355934443837092L;

    @Id
    private Long id;
    @ApiModelProperty("合成id")
    private String compoundId;
    @ApiModelProperty("商户id")
    private Long merchantId;
    @ApiModelProperty("成品图层id")
    private Long designProductLayerId;
    @ApiModelProperty("主题id")
    private Long themePrototypeId;
    @ApiModelProperty("风格id")
    private Long styleId;

}
