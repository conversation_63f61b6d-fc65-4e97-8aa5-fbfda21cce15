package com.sds.outsite.entity.theme;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/2/10
 */
@Data
@Accessors(chain = true)
@TableName("design_product_theme_default_rel")
@ApiModel(value = "DesignProductThemeDefaultRel对象", description = "主题半成品与默认成品的关系")
public class DesignProductThemeDefaultRel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private Long id;
    private Date createTime;
    @ApiModelProperty("半成品id")
    private Long halfDesignProductId;
    @ApiModelProperty("默认成品id")
    private Long defaultDesignProductId;
    @ApiModelProperty("默认keyid")
    private String defaultKeyId;
}
