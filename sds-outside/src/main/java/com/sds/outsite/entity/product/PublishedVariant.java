package com.sds.outsite.entity.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.math.BigDecimal;

/**
 * 汇出的变体信息
 *
 * <AUTHOR>
 */
@Data
public class PublishedVariant {

    @Id
    private Long id;

    @ApiModelProperty("平台code")
    private String platform;

    @ApiModelProperty("产品ID")
    private String productId;

    @ApiModelProperty("变体ID")
    private String variantId;

    @ApiModelProperty("价格")
    private BigDecimal price;
}
