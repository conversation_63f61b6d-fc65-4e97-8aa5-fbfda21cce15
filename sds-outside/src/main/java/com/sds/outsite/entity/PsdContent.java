package com.sds.outsite.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BasePO;
import lombok.Data;

import javax.persistence.Id;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/21
 */
@Data
public class PsdContent  {
    @Id
    private Long id;
    private Date createTime;
    private String psdCode;
    private String value;
}
