/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.validator.constraints.Length;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class DesignScheduledTask {
	
	
	//可以直接使用: @Length(max=50,message="用户名长度不能大于50")显示错误消息
	//columns START
	@JsonProperty("id")
    
	@Id
	@GeneratedValue(generator = "JDBC")
	private Long id;
	@JsonProperty("status")
    @NotNull @Max(127)
	private Integer status;
	@JsonProperty("created_time")
    @NotNull
	private Long createdTime;
	@JsonProperty("update_time")
    @NotNull
	private Long updateTime;
	@JsonProperty("finish_time")
    @NotNull
	private Long finishTime;
	@JsonProperty("design_total")
    @NotNull
	private Integer designTotal;

	@JsonProperty("user_id")
	@NotNull
	private Long userId;
	@JsonProperty("merchant_id")
	@NotNull
	private Long merchantId;

	@NotBlank @Length(max=64)
	private String combineRule;
	@JsonProperty("combine_rule_type")
	@NotNull @Max(127)
	private Integer combineRuleType;
	private Integer type;
	@JsonProperty("task_id")
	@NotNull
	private Long taskId;
	//columns END

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	public String getCombineRule() {
		return combineRule;
	}

	public void setCombineRule(String combineRule) {
		this.combineRule = combineRule;
	}

	public Integer getCombineRuleType() {
		return combineRuleType;
	}

	public void setCombineRuleType(Integer combineRuleType) {
		this.combineRuleType = combineRuleType;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public DesignScheduledTask(){
	}

	public DesignScheduledTask(
		Long id
	){
		this.id = id;
	}

	public void setId(Long value) {
		this.id = value;
	}

	public Long getId() {
		return this.id;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}

	public Integer getStatus() {
		return this.status;
	}
	public void setCreatedTime(Long value) {
		this.createdTime = value;
	}

	public Long getCreatedTime() {
		return this.createdTime;
	}
	public void setUpdateTime(Long value) {
		this.updateTime = value;
	}

	public Long getUpdateTime() {
		return this.updateTime;
	}
	public void setFinishTime(Long value) {
		this.finishTime = value;
	}

	public Long getFinishTime() {
		return this.finishTime;
	}
	public void setDesignTotal(Integer value) {
		this.designTotal = value;
	}

	public Integer getDesignTotal() {
		return this.designTotal;
	}

	

}

