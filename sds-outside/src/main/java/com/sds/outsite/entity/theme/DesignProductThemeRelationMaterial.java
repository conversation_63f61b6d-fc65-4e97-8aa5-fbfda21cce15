package com.sds.outsite.entity.theme;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 成品主题关联素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2021/12/30
 */
@Data
@Accessors(chain = true)
@TableName("design_product_theme_relation_material")
@ApiModel(value = "DesignProductThemeRelationMaterial对象", description = "成品主题关联素材表")
public class DesignProductThemeRelationMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    @ApiModelProperty(value = "成品id")
    private Long designProductId;

    @ApiModelProperty(value = "主题id")
    private Long themeId;

    @ApiModelProperty(value = "素材id")
    private Long materialId;

    @ApiModelProperty(value = "主题关联素材分类id")
    private Long themeRelationMaterialCategoryId;

    @Id
    private Long id;
    private Long createUid;
    private Long updateUid;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private Integer isDelete;
}
