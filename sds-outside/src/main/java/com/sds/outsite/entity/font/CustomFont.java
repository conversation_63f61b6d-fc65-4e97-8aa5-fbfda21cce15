package com.sds.outsite.entity.font;

import lombok.Data;

import javax.persistence.Id;
import java.util.Date;

/**
 * 自定义字体表(CustomFont)实体类
 *
 * <AUTHOR>
 * @since 2024-02-04 10:55:30
 */
@Data
public class CustomFont {
    /**
     * id
     */
    @Id
    private Long id;
    /**
     * 自定义字体名称
     */    
    private String customName;
    /**
     * 字体名称
     */
    private String postscriptName;
    /**
     * 预览图
     */    
    private String previewImage;
    /**
     * fileCode
     */
    private String fileCode;
    /**
     * 状态，渲染中：processing,待确认：unconfirmed,待生效：inactive,已生效：activated,失败：fail
     */
    private String status;
    /**
     * 类型，系统：system，用户：user
     */
    private String type;
    /**
     * 商户id
     */    
    private Long merchantId;
    /**
     * 确认时间
     */    
    private Long confirmTime;
    /**
     * 创建者id
     */    
    private Long createUid;
    /**
     * 创建时间
     */    
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新者id
     */
    private Long updateUid;

    private Integer isDelete;

    private String failMessage;


}
