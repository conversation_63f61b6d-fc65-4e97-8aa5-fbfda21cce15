/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class DesignProductLayer {
	
	
	//可以直接使用: @Length(max=50,message="用户名长度不能大于50")显示错误消息
	//columns START
	@JsonProperty("id")
    
	@Id
	@GeneratedValue(generator = "JDBC")
	private Long id;
	@JsonProperty("compound_id")
    @NotBlank
	private String compoundId;
	@JsonProperty("prototype_layer_id")
    @NotNull 
	private Long prototypeLayerId;
	@JsonProperty("merchant_id")
    @NotNull
	@Id
	private Long merchantId;
	@JsonProperty("prototype_layer_name")
    @NotBlank @Length(max=255)
	private String prototypeLayerName;
	@JsonProperty("type")
    @NotNull @Max(127)
	private Integer type;
	@JsonProperty("content")
    @NotBlank @Length(max=1024)
	private String content;
	private String fontName;
	@JsonProperty("content_md5")
    @NotBlank @Length(max=1024)
	private String contentMd5;
	@JsonProperty("material_id")
    @NotNull 
	private Long materialId;
	@JsonProperty("material_img_url")
    @NotBlank @Length(max=255)
	private String materialImgUrl;

	@JsonProperty("height")
    @NotNull 
	private Double height;
	@JsonProperty("width")
    @NotNull 
	private Double width;
	@JsonProperty("src_filecode")
    @NotBlank
	private String srcFilecode;
	@JsonProperty("print_height")
    @NotNull 
	private Double printHeight;
	@JsonProperty("print_width")
    @NotNull 
	private Double printWidth;
	@JsonProperty("image_height")
    @NotBlank @Length(max=32)
	private String imageHeight;
	@JsonProperty("image_width")
    @NotBlank @Length(max=32)
	private String imageWidth;
	@JsonProperty("fit_level")
    @NotNull @Max(127)
	private Integer fitLevel;
	@JsonProperty("design_data")
    @NotBlank
	private String designData;
	@JsonProperty("resize_mode")
    @NotBlank @Length(max=8)
	private String resizeMode;
	@JsonProperty("lens_code")
    @NotBlank @Length(max=16)
	private String lensCode;
	@JsonProperty("material_json")
    @NotBlank @Length(max=1024)
	private String materialJson;
	@JsonProperty("design_data_status")
    @NotBlank @Length(max=16)
	private String designDataStatus;

	private Long designBackgroundId;

	@Getter
	@Setter
	@ApiModelProperty("主题id")
	@Transient
	private Long themePrototypeId;
	@Getter
	@Setter
	@ApiModelProperty("风格id")
	@Transient
	private Long styleId;
	//columns END

	public DesignProductLayer(){
	}

	public DesignProductLayer(
		Long id
	){
		this.id = id;
	}


	public Long getDesignBackgroundId() {
		return designBackgroundId;
	}

	public void setDesignBackgroundId(Long designBackgroundId) {
		this.designBackgroundId = designBackgroundId;
	}

	public void setId(Long value) {
		this.id = value;
	}
	
	public Long getId() {
		return this.id;
	}
	public void setCompoundId(String value) {
		this.compoundId = value;
	}
	
	public String getCompoundId() {
		return this.compoundId;
	}
	public void setPrototypeLayerId(Long value) {
		this.prototypeLayerId = value;
	}
	
	public Long getPrototypeLayerId() {
		return this.prototypeLayerId;
	}
	public void setPrototypeLayerName(String value) {
		this.prototypeLayerName = value;
	}
	
	public String getPrototypeLayerName() {
		return this.prototypeLayerName;
	}
	public void setType(Integer value) {
		this.type = value;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setContent(String value) {
		this.content = value;
	}
	
	public String getContent() {
		return this.content;
	}
	public void setMaterialId(Long value) {
		this.materialId = value;
	}
	
	public Long getMaterialId() {
		return this.materialId;
	}
	public void setMaterialImgUrl(String value) {
		this.materialImgUrl = value;
	}
	
	public String getMaterialImgUrl() {
		return this.materialImgUrl;
	}
	public void setHeight(Double value) {
		this.height = value;
	}
	
	public Double getHeight() {
		return this.height;
	}
	public void setWidth(Double value) {
		this.width = value;
	}
	
	public Double getWidth() {
		return this.width;
	}
	public void setSrcFilecode(String value) {
		this.srcFilecode = value;
	}
	
	public String getSrcFilecode() {
		return this.srcFilecode;
	}
	public void setPrintHeight(Double value) {
		this.printHeight = value;
	}
	
	public Double getPrintHeight() {
		return this.printHeight;
	}
	public void setPrintWidth(Double value) {
		this.printWidth = value;
	}
	
	public Double getPrintWidth() {
		return this.printWidth;
	}
	public void setImageHeight(String value) {
		this.imageHeight = value;
	}
	
	public String getImageHeight() {
		return this.imageHeight;
	}
	public void setImageWidth(String value) {
		this.imageWidth = value;
	}
	
	public String getImageWidth() {
		return this.imageWidth;
	}
	public void setFitLevel(Integer value) {
		this.fitLevel = value;
	}
	
	public Integer getFitLevel() {
		return this.fitLevel;
	}
	public void setDesignData(String value) {
		this.designData = value;
	}
	
	public String getDesignData() {
		return this.designData;
	}
	public void setResizeMode(String value) {
		this.resizeMode = value;
	}
	
	public String getResizeMode() {
		return this.resizeMode;
	}
	public void setLensCode(String value) {
		this.lensCode = value;
	}
	
	public String getLensCode() {
		return this.lensCode;
	}
	public void setMaterialJson(String value) {
		this.materialJson = value;
	}
	
	public String getMaterialJson() {
		return this.materialJson;
	}
	public void setDesignDataStatus(String value) {
		this.designDataStatus = value;
	}
	
	public String getDesignDataStatus() {
		return this.designDataStatus;
	}

	public String getContentMd5() {
		return contentMd5;
	}

	public void setContentMd5(String contentMd5) {
		this.contentMd5 = contentMd5;
	}

	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	public String getFontName() {
		return fontName;
	}

	public void setFontName(String fontName) {
		this.fontName = fontName;
	}
}

