/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.validator.constraints.Length;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class DesignProduct   {
	
	
	//可以直接使用: @Length(max=50,message="用户名长度不能大于50")显示错误消息
	//columns START
	@JsonProperty("id")
    
	@Id
	@GeneratedValue(generator = "JDBC")
	private Long id;
	@JsonProperty("pid")
    @NotNull
	private Long pid;
	@JsonProperty("compound_id")
    @NotBlank
	private String compoundId;
	@JsonProperty("design_task_id")
    @NotNull
	private Long designTaskId;
	@JsonProperty("merchant_id")
    @NotNull
	private Long merchantId;
	@JsonProperty("user_id")
    @NotNull
	private Long userId;
	@JsonProperty("username")
    @NotBlank
	private String username;
	@JsonIgnore
	private String materialKey;

	@JsonProperty("material_ids")
    @NotBlank
	private String materialIds;
	@JsonProperty("src_content")
    @NotBlank
	private String srcContent;
	@JsonProperty("prototype_id")
    @NotNull
	private Long prototypeId;
	@JsonProperty("product_id")
    @NotNull
	private Long productId;
	@JsonProperty("product_parent_id")
    @NotNull
	private Long productParentId;
	@JsonProperty("created_time")
    @NotNull
	private Long createdTime;
	@JsonProperty("finish_time")
    @NotNull
	private Long finishTime;
	@JsonProperty("update_time")
    @NotNull
	private Long updateTime;
	@JsonProperty("status")
    @NotNull
	private Integer status;
	@JsonProperty("type")
    @NotNull @Max(127)
	private Integer type;
	@JsonProperty("images_total")
    @NotNull
	private Integer imagesTotal;
	@JsonProperty("compound_json")
    @NotBlank
	private String compoundJson;
	@JsonProperty("scheduled_task_id")
    @NotNull
	private Long scheduledTaskId;
	@JsonProperty("key_id")
    @NotBlank
	private String keyId;

	@JsonProperty("color")
    @NotBlank @Length(max=64)
	private String color;
	@JsonProperty("size")
    @NotBlank
	private String size;
	@JsonProperty("product_color")
    @NotBlank @Length(max=64)
	private String productColor;
	@JsonProperty("product_size")
    @NotBlank
	private String productSize;

	@JsonProperty("material_img_name")
    @NotBlank
	private String materialImgName;
	@JsonProperty("keyword")
    @NotBlank
	private String keyword;
	@JsonProperty("export_time")
    @NotNull
	private Integer exportTime;
	@JsonProperty("parent_attribute")
    @NotNull @Max(127)
	private Integer parentAttribute;
	@JsonProperty("material_group_size")
    @NotNull @Max(127)
	private Integer materialGroupSize;
	@JsonProperty("attributes_json")
	private String attributesJson;
	@JsonProperty("export_name")
	@NotBlank
	private String exportName;

	@JsonIgnore
	private Long materialGroupPid;
	private Integer materialGroupType;
	@JsonIgnore
	private String materialColor;
	//columns END

	public Long getMaterialGroupPid() {
		return materialGroupPid;
	}

	public void setMaterialGroupPid(Long materialGroupPid) {
		this.materialGroupPid = materialGroupPid;
	}

	public Integer getMaterialGroupSize() {
		return materialGroupSize;
	}

	public void setMaterialGroupSize(Integer materialGroupSize) {
		this.materialGroupSize = materialGroupSize;
	}

	public Integer getMaterialGroupType() {
		return materialGroupType;
	}

	public void setMaterialGroupType(Integer materialGroupType) {
		this.materialGroupType = materialGroupType;
	}

	public String getMaterialColor() {
		return materialColor;
	}

	public void setMaterialColor(String materialColor) {
		this.materialColor = materialColor;
	}

	public String getAttributesJson() {
		return attributesJson;
	}

	public void setAttributesJson(String attributesJson) {
		this.attributesJson = attributesJson;
	}

	public DesignProduct(){
	}

	public DesignProduct(
		Long id
	){
		this.id = id;
	}

	public Long getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Long finishTime) {
		this.finishTime = finishTime;
	}

	public String getExportName() {
		return exportName;
	}

	public void setExportName(String exportName) {
		this.exportName = exportName;
	}

	public void setId(Long value) {
		this.id = value;
	}

	public Long getId() {
		return this.id;
	}
	public void setPid(Long value) {
		this.pid = value;
	}

	public Long getPid() {
		return this.pid;
	}
	public void setCompoundId(String value) {
		this.compoundId = value;
	}

	public String getCompoundId() {
		return this.compoundId;
	}
	public void setDesignTaskId(Long value) {
		this.designTaskId = value;
	}

	public Long getDesignTaskId() {
		return this.designTaskId;
	}
	public void setMerchantId(Long value) {
		this.merchantId = value;
	}

	public Long getMerchantId() {
		return this.merchantId;
	}
	public void setUserId(Long value) {
		this.userId = value;
	}

	public Long getUserId() {
		return this.userId;
	}
	public void setUsername(String value) {
		this.username = value;
	}

	public String getUsername() {
		return this.username;
	}
	public void setMaterialIds(String value) {
		this.materialIds = value;
	}

	public String getMaterialIds() {
		return this.materialIds;
	}
	public void setSrcContent(String value) {
		this.srcContent = value;
	}

	public String getMaterialKey() {
		return materialKey;
	}

	public void setMaterialKey(String materialKey) {
		this.materialKey = materialKey;
	}

	public String getSrcContent() {
		return this.srcContent;
	}
	public void setPrototypeId(Long value) {
		this.prototypeId = value;
	}

	public Long getPrototypeId() {
		return this.prototypeId;
	}
	public void setProductId(Long value) {
		this.productId = value;
	}

	public Long getProductId() {
		return this.productId;
	}
	public void setProductParentId(Long value) {
		this.productParentId = value;
	}

	public Long getProductParentId() {
		return this.productParentId;
	}
	public void setCreatedTime(Long value) {
		this.createdTime = value;
	}

	public Long getCreatedTime() {
		return this.createdTime;
	}
	public void setUpdateTime(Long value) {
		this.updateTime = value;
	}

	public Long getUpdateTime() {
		return this.updateTime;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}

	public Integer getStatus() {
		return this.status;
	}
	public void setType(Integer value) {
		this.type = value;
	}

	public Integer getType() {
		return this.type;
	}
	public void setImagesTotal(Integer value) {
		this.imagesTotal = value;
	}

	public Integer getImagesTotal() {
		return this.imagesTotal;
	}
	public void setCompoundJson(String value) {
		this.compoundJson = value;
	}

	public String getCompoundJson() {
		return this.compoundJson;
	}
	public void setScheduledTaskId(Long value) {
		this.scheduledTaskId = value;
	}

	public Long getScheduledTaskId() {
		return this.scheduledTaskId;
	}
	public void setKeyId(String value) {
		this.keyId = value;
	}

	public String getKeyId() {
		return this.keyId;
	}

	public void setColor(String value) {
		this.color = value;
	}

	public String getColor() {
		return this.color;
	}
	public void setSize(String value) {
		this.size = value;
	}

	public String getSize() {
		return this.size;
	}
	public void setProductColor(String value) {
		this.productColor = value;
	}

	public String getProductColor() {
		return this.productColor;
	}
	public void setProductSize(String value) {
		this.productSize = value;
	}

	public String getProductSize() {
		return this.productSize;
	}
	public void setMaterialImgName(String value) {
		this.materialImgName = value;
	}

	public String getMaterialImgName() {
		return this.materialImgName;
	}
	public void setKeyword(String value) {
		this.keyword = value;
	}

	public String getKeyword() {
		return this.keyword;
	}
	public void setExportTime(Integer value) {
		this.exportTime = value;
	}

	public Integer getExportTime() {
		return this.exportTime;
	}
	public void setParentAttribute(Integer value) {
		this.parentAttribute = value;
	}
	
	public Integer getParentAttribute() {
		return this.parentAttribute;
	}



}

