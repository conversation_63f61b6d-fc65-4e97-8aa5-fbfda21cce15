package com.sds.outsite.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;

/**
 * <AUTHOR>
@Data
public class MerchantStoreDesign {

	@Id
	private Long id;
	private String storePlatform;

	@ApiModelProperty(value = "设计器开关  online 开 offline 关")
	private String status;

	@ApiModelProperty(value = "设计器按钮名称")
	private String buttonName;

	@ApiModelProperty(value = "设计数")
	private Integer designProductQty;

	@ApiModelProperty(value = "素材数")
	private Integer materialQty;

	@ApiModelProperty(value = "授权token")
	private String token;

	@ApiModelProperty(value = "店铺唯一标识（sellerId、shopifyShopId）")
	private String storePlatformId;

	@ApiModelProperty(value = "素材列表开关 online 开 offline 关")
	private String materialListStatus;

	@ApiModelProperty(value = "产品列表开关 online 开 offline 关")
	private String productListStatus;
}

