package com.sds.outsite.controller;

import com.ps.dto.SqlMsg;
import com.sds.outsite.service.DataSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("dataSync")
@Controller
public class DataSyncController {


    @Autowired
    DataSyncService service;

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST)
    public void post(@RequestBody SqlMsg msg) {
        service.listen(msg);
    }
}

