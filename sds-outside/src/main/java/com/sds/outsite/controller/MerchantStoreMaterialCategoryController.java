package com.sds.outsite.controller;

import com.ps.support.ListResponse;
import com.sds.outsite.dto.VariantDesignProductDTO;
import com.sds.outsite.service.MerchantStoreMaterialCategoryService;
import com.sds.outsite.service.VariantDesignProductService;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.materialapi.dto.resp.MerchantStoreMaterialCategoryResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: bin_lin
 * @date: 2020/11/26 19:57
 * @desc:
 */
@Api("店铺素材分组")
@RestController
@RequestMapping("/merchants")
public class MerchantStoreMaterialCategoryController {
    @Resource
    private MerchantStoreMaterialCategoryService merchantStoreMaterialCategoryService;
    @Autowired
    private VariantDesignProductService variantDesignProductService;


    @GetMapping("/shopifyVariantDesignProductIds/{shopifyVariantId}/merchantStoreMaterialCategory")
    public ListResponse<MerchantStoreMaterialCategoryResp> getMerchantStoreMaterialCategoryList(@PathVariable("shopifyVariantId") Long shopifyVariantId) {
        return listMerchantStoreMaterialCategory(shopifyVariantId.toString(), MerchantStorePlatformEnum.SHOPIFY.getCode());
    }

    @ApiOperation("列表")
    @GetMapping("/{platform}/variantDesignProduct/{variantId}/storeMaterialCategory")
    public ListResponse<MerchantStoreMaterialCategoryResp> listMerchantStoreMaterialCategory(@PathVariable("variantId") String variantId,
                                                                                             @PathVariable("platform") String platform) {
        VariantDesignProductDTO dto = variantDesignProductService.getVariantDesignProductDTO(variantId, platform);
        List<MerchantStoreMaterialCategoryResp> list = merchantStoreMaterialCategoryService
                .getMerchantStoreMaterialCategoryList(dto.getMerchantId(), dto.getMerchantStoreId());
        return new ListResponse<>(list.size(), list);
    }

}

