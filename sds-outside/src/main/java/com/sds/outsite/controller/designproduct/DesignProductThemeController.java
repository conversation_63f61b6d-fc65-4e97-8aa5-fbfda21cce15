package com.sds.outsite.controller.designproduct;

import com.ps.support.IdGenerator;
import com.ps.tool.SnowFlakeDto;
import com.sds.outsite.dto.DesignConvertTemporaryDTO;
import com.sds.outsite.entity.ShopifyVariantDesignProduct;
import com.sds.outsite.entity.ShoplazzaVariantDesignProduct;
import com.sds.outsite.service.DesignConvertTemporaryService;
import com.sds.outsite.service.ShopifyVariantDesignProductService;
import com.sds.outsite.service.ShoplazzaVariantDesignProductService;
import com.sds.outsite.service.theme.DesignProductThemeRelationMaterialService;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.designproductdata.dto.theme.DesignProductThemeCategoryImgDTO;
import com.sdsdiy.designproductdata.dto.theme.DesignProductThemeRelImgReqDTO;
import com.sdsdiy.designproductdata.dto.theme.DesignProductThemeRelationMaterialDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/30
 */
@Api("主题成品")
@RestController
@RequestMapping("/design/product/")
public class DesignProductThemeController {
    @Autowired
    private DesignProductThemeRelationMaterialService designProductThemeRelationMaterialService;
    @Autowired
    private ShopifyVariantDesignProductService shopifyVariantDesignProductService;
    @Resource
    private DesignConvertTemporaryService designConvertTemporaryService;
    @Autowired
    private ShoplazzaVariantDesignProductService shoplazzaVariantDesignProductService;

    /**
     * 需求变化，这个接口有问题，别用 yrs
     */
//    @ApiOperation("分组下-关联素材图片")
    @GetMapping("/{platformVariantId}/category/{categoryId}/relation/img")
    public DesignProductThemeCategoryImgDTO categoryRelationImg(@ApiParam("shopfiy变体id") @PathVariable("platformVariantId") String platformVariantId
            , @ApiParam("分组id") @PathVariable("categoryId") Long categoryId
            , @ApiParam("平台：shopify、shoplazza") @RequestParam(required = false, defaultValue = "shopify") String platform) {
        DesignProductThemeRelImgReqDTO reqDTO = new DesignProductThemeRelImgReqDTO();
        if ("shopify".equalsIgnoreCase(platform)) {
            ShopifyVariantDesignProduct shopifyVariant = shopifyVariantDesignProductService.findByShopifyVariantId(Long.parseLong(platformVariantId));
            reqDTO.setDesignProductId(shopifyVariant.getDesignProductId());
        } else if ("shoplazza".equalsIgnoreCase(platform)) {
            ShoplazzaVariantDesignProduct shoplazzaVariantDesignProduct = shoplazzaVariantDesignProductService.findByShoplazzaVariantId(platformVariantId);
            reqDTO.setDesignProductId(shoplazzaVariantDesignProduct.getDesignProductId());
        } else {
            return null;
        }
        reqDTO.setThemeRelationMaterialCategoryId(categoryId);
        DesignProductThemeCategoryImgDTO imgDTO = new DesignProductThemeCategoryImgDTO();
        imgDTO.setCategoryId(categoryId);
        List<DesignProductThemeRelationMaterialDTO> list = designProductThemeRelationMaterialService.listMaterial(reqDTO);
        imgDTO.setMaterialList(ListUtil.copyProperties(list, DesignProductThemeCategoryImgDTO.MaterialImgDTO.class));
        return imgDTO;
    }

    @ApiOperation("暂存设计信息（主题设计转素材设计）")
    @PostMapping("saveConvert")
    public SnowFlakeDto saveConvert(@RequestBody DesignConvertTemporaryDTO converMessage) {
        SnowFlakeDto snowFlakeDto = new SnowFlakeDto();
        String nextStringId = String.valueOf(IdGenerator.nextId());
        snowFlakeDto.setId(nextStringId);
        DesignConvertTemporaryDTO designConvertTemporaryDTO = new DesignConvertTemporaryDTO(nextStringId, converMessage.getDesignData());
        designConvertTemporaryService.saveConvert(designConvertTemporaryDTO);
        return snowFlakeDto;
    }


    @ApiOperation("获取设计信息（主题设计转素材设计）")
    @GetMapping("getConvert")
    public DesignConvertTemporaryDTO getConvert(@RequestParam String id) {
        return designConvertTemporaryService.getConvert(id);
    }
}