package com.sds.outsite.controller;

import com.ps.support.utils.ConvertUtil;
import com.sds.outsite.dto.OutSiteProductCompounDTO;
import com.sds.outsite.dto.ShopifyStoreDesignRespDto;
import com.sds.outsite.service.OutDesignProductService;
import com.sds.outsite.service.ShoplazzaVariantDesignProductService;
import com.sdsdiy.core.file.storage.util.FileUtils;
import com.sdsdiy.designproductdata.dto.platform.ShoplazzaVariantDesignProductDto;
import com.ziguang.base.dto.CompounDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Api("海外成品")
@RequestMapping("/outSiteProducts")
@Controller
public class OutSiteDesignProductController {


    @Autowired
    OutDesignProductService outDesignProductService;

    @Autowired
    ShoplazzaVariantDesignProductService shoplazzaVariantDesignProductService;

    @ApiOperation("保存设计信息，生成sds成品")
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST)
    public ShopifyStoreDesignRespDto post(@RequestBody OutSiteProductCompounDTO compounDTO) {
        ShopifyStoreDesignRespDto res ;
        if ("shopify".equalsIgnoreCase(compounDTO.getPlatform())) {
            CompounDTO item = ConvertUtil.dtoConvert(compounDTO, CompounDTO.class);
            item.setShopifyVariantId(Long.valueOf(compounDTO.getId()));
            item.setDesignType(compounDTO.getDesignType());
            res = outDesignProductService.designShopifyV2(item);
        } else {
            res = outDesignProductService.designShoplazzaV2(compounDTO);
        }
        res.setImageUrl(FileUtils.usToCdn(res.getImageUrl()));
        res.setThumbImageUrl(FileUtils.usToCdn(res.getThumbImageUrl()));
        return res;
    }

    @ApiOperation("保存主题设计信息，生成sds成品")
    @ResponseBody
    @RequestMapping(value = "theme", method = RequestMethod.POST)
    public ShopifyStoreDesignRespDto themePost(@RequestBody OutSiteProductCompounDTO compounDTO) {
        ShopifyStoreDesignRespDto res ;
        if ("shopify".equalsIgnoreCase(compounDTO.getPlatform())) {
            CompounDTO item = ConvertUtil.dtoConvert(compounDTO, CompounDTO.class);
            item.setShopifyVariantId(Long.valueOf(compounDTO.getId()));
            item.setDesignType(compounDTO.getDesignType());
            res = outDesignProductService.designShopifyThemeV2(item);
        } else {
            res = outDesignProductService.designShoplazzaThemeV2(compounDTO);
        }
        res.setImageUrl(FileUtils.usToCdn(res.getImageUrl()));
        res.setThumbImageUrl(FileUtils.usToCdn(res.getThumbImageUrl()));
        return res;
    }

    @ApiOperation("保存文字或来图设计信息，生成sds成品")
    @ResponseBody
    @RequestMapping(value = "wordAndCustomImage", method = RequestMethod.POST)
    public ShopifyStoreDesignRespDto wordAndCustomImage(@RequestBody OutSiteProductCompounDTO compounDTO) {
        if ("shopify".equalsIgnoreCase(compounDTO.getPlatform())) {
            CompounDTO item = ConvertUtil.dtoConvert(compounDTO, CompounDTO.class);
            item.setShopifyVariantId(Long.valueOf(compounDTO.getId()));
            item.setDesignType(compounDTO.getDesignType());
            return outDesignProductService.designShopifyWordAndCustomImage(item);
        } else {
            return outDesignProductService.designShoplazzaWordAndCustomImage(compounDTO);
        }
    }

    @ResponseBody
    @RequestMapping(value = "shoplazzaVariantDesignProductServiceAdd", method = RequestMethod.POST)
    public void shoplazzaVariantDesignProductServiceAdd(@RequestBody ShoplazzaVariantDesignProductDto shoplazzaVariantDesignProductDto) {
        shoplazzaVariantDesignProductService.add(shoplazzaVariantDesignProductDto);
    }
}

