package com.sds.outsite.controller;

import com.sds.outsite.entity.MerchantStoreDesign;
import com.sds.outsite.service.MerchantStoreDesignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: bin_lin
 * @date: 2020/11/26 19:57
 * @desc:
 */
@Api("设计器")
@RestController
@RequestMapping("/merchants")
public class ShopifyStoreDesignController {
    @Resource
    private MerchantStoreDesignService merchantStoreDesignService;

    @GetMapping("/shopifyVariantDesignProductIds/{shopifyVariantId}/shopifyStoreDesign")
    public MerchantStoreDesign getShopifyStoreDesign(@PathVariable("shopifyVariantId") String shopifyVariantId) {
        return getMerchantStoreDesign("shopify", shopifyVariantId);
    }

    @ApiOperation("根据平台变体获取")
    @GetMapping("merchantStoreDesign")
    public MerchantStoreDesign getMerchantStoreDesign(@RequestParam("platform") String platform, @RequestParam("id") String id) {
        return merchantStoreDesignService.getDesignByPlatformVariantId(platform, id);
    }

}

