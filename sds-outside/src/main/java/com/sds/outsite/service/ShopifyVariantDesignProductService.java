/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import cn.hutool.core.bean.BeanUtil;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.google.common.collect.Lists;
import com.ps.amazon.s3.SqsService;
import com.ps.base.service.TKMapperBaseService;
import com.ps.ps.service.ConfigService;
import com.ps.support.IdGenerator;
import com.sds.outsite.dao.ShopifyVariantDesignProductDao;
import com.sds.outsite.dto.MerchantStoreDesignDto;
import com.sds.outsite.dto.ShopifyVariantProductDesignRespDto;
import com.sds.outsite.entity.MerchantStoreDesign;
import com.sds.outsite.entity.ShopifyVariantDesignProduct;
import com.sdsdiy.core.mq.SendTemplate;
import com.sdsdiy.core.mq.core.RocketMQTemplate;

import com.sdsdiy.core.mq.queue.OutsiteTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.designproductdata.dto.platform.ShopifyVariantDesignProductDto;
import com.sdsdiy.ecommerceapi.dto.shopify.ProductRespDto;
import com.sdsdiy.ecommerceapi.dto.shopify.VariantRespDto;
import com.sdsdiy.publishapi.myenum.EnumShopifyDesignModelType;
import com.ziguang.base.support.JsonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;


@Service
public class ShopifyVariantDesignProductService extends TKMapperBaseService<ShopifyVariantDesignProductDao, ShopifyVariantDesignProduct> {
    public ShopifyVariantDesignProduct findByShopifyVariantId(Long id) {
        ShopifyVariantDesignProduct data = new ShopifyVariantDesignProduct();
        data.setShopifyVariantId(id);
        return dao.selectOne(data);
    }


    public List<ShopifyVariantDesignProduct> findByShopifyProductId(Long id, Long sdsDesignProductGroupId) {
        ShopifyVariantDesignProduct data = new ShopifyVariantDesignProduct();
        data.setShopifyProductId(id);
        data.setSdsDesignProductGroupId(sdsDesignProductGroupId);
        return dao.select(data);
    }

    @Autowired
    AmazonSQSAsync amazonSQSAsync;

    @Autowired
    SqsService sqsService;

    public ShopifyVariantDesignProduct save(ShopifyVariantDesignProduct entity) {
        ShopifyVariantDesignProduct res = super.save(entity);
        CompletableFuture.runAsync(() -> {
            this.syncToSqs(entity);
        });
        //通知sqs
        return res;
    }

    @Autowired
    ConfigService configService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void syncToSqs(ShopifyVariantDesignProduct entity) {
        List<SendMessageBatchRequestEntry> entries = Lists.newArrayList();
        String json = JsonUtil.toJson(entity);
        logger.info("shopify_design_success" + json);
        String md5 = IdGenerator.nextStringId();
        SendMessageBatchRequestEntry entry = new SendMessageBatchRequestEntry(md5, json);
        entries.add(entry);
//        String sqsUrl = String.format(SqsUrlConstant.SHOPIFY_DESIGN_SUCCESS, configService.getEnv());
//        sqsService.sendToSqs(amazonSQSAsync, sqsUrl, entries);
        rocketMQTemplate.sendDelay(RocketMqTopicConst.EVENT_OUT_DELAY, OutsiteTagConst.OUT_SHOPIFY_DESIGN_SUCCESS, entity, Duration.ofSeconds(1));
    }

    public static void main(String[] args) {
        ShopifyVariantDesignProduct a = new ShopifyVariantDesignProduct();
        a.setOldDesignProductId(12421L);
        System.out.println(JsonUtil.toJson(a));
    }

    @Autowired
    @Lazy
    DesignProductService designProductService;

    public void add(ShopifyVariantDesignProductDto shopifyVariantDesignProductDto) {
        ShopifyVariantDesignProduct shopifyVariantDesignProduct = this.findByShopifyVariantId(shopifyVariantDesignProductDto.getShopifyVariantId());
        if (shopifyVariantDesignProduct != null) {
            return;
        }
        designProductService.mapperAdd(shopifyVariantDesignProductDto.getMerchantId(), shopifyVariantDesignProductDto.getDesignProductId());
        ShopifyVariantDesignProduct data = new ShopifyVariantDesignProduct();
        BeanUtil.copyProperties(shopifyVariantDesignProductDto, data);
        data.setCreateTime(new Date());
        this.save(data);
    }

    @Resource
    private MerchantStoreDesignService merchantStoreDesignService;

    public ShopifyVariantProductDesignRespDto getDesignInfo(Long id) {
        ShopifyVariantDesignProduct shopifyVariantDesignProduct = this.findByShopifyVariantId(id);
        if (shopifyVariantDesignProduct == null) {
            return null;
        }
        ShopifyVariantProductDesignRespDto shopifyVariantProductDesignRespDto = new ShopifyVariantProductDesignRespDto();
        shopifyVariantProductDesignRespDto.setDesignModel(shopifyVariantDesignProduct.getDesignModel());
        shopifyVariantProductDesignRespDto.setStoreId(shopifyVariantDesignProduct.getMerchantStoreId());
        MerchantStoreDesign merchantStoreDesign = merchantStoreDesignService.get(shopifyVariantDesignProduct.getMerchantStoreId());
        if (merchantStoreDesign != null) {
            MerchantStoreDesignDto merchantStoreDesignDto = new MerchantStoreDesignDto();
            merchantStoreDesignDto.setButtonName(merchantStoreDesign.getButtonName());
            merchantStoreDesignDto.setStatus(merchantStoreDesign.getStatus());
            shopifyVariantProductDesignRespDto.setShopifyStoreDesign(merchantStoreDesignDto);
        }
        return shopifyVariantProductDesignRespDto;
    }


}

