package com.sds.outsite.service.product;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 自动导入的订单解析工厂，根据不同的平台获取不同的解析方式
 *
 * <AUTHOR>
 */
@Service
public class SyncPublishedProductFactory implements ApplicationContextAware {

    private static Map<String, ISyncPublishedProduct> IMPL_MAP = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ISyncPublishedProduct> map = applicationContext.getBeansOfType(ISyncPublishedProduct.class);
        map.forEach((key, value) -> IMPL_MAP.put(value.getPlatformCode(), value));
    }

    public static <T extends ISyncPublishedProduct> T getConsumeService(String platformCode) {
        return (T) IMPL_MAP.get(platformCode);
    }
}
