package com.sds.outsite.service.theme;

import cn.hutool.core.collection.CollUtil;
import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.ps.support.BeanUtils;
import com.sds.outsite.dao.theme.DesignProductThemePrototypeStyleDao;
import com.sds.outsite.entity.theme.DesignProductThemePrototypeStyle;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.designproductdata.dto.DesignProductThemePrototypeStyleDTO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/30
 */
@Service
public class DesignProductThemePrototypeStyleService extends TKBaseService<DesignProductThemePrototypeStyleDao, DesignProductThemePrototypeStyle> {

    public List<DesignProductThemePrototypeStyleDTO> getByCompoundId(String compoundId) {
        TkExample example = new TkExample(DesignProductThemePrototypeStyle.class);
        example.createCriteria().andEqualTo("compoundId", compoundId);
        List<DesignProductThemePrototypeStyle> prototypeStyles = dao.selectByExample(example);
        return ListUtil.copyProperties(prototypeStyles, DesignProductThemePrototypeStyleDTO.class);
    }

    public List<DesignProductThemePrototypeStyle> listByLayerIdAndTimeSign(Collection<Long> layerIds, Collection<Long> timeSigns) {
        TkExample example = new TkExample(DesignProductThemePrototypeStyle.class);
        example.createCriteria().andIn("designProductLayerId", layerIds)
                .andIn("themeTimeSign", timeSigns);
        return dao.selectByExample(example);
    }

    public List<DesignProductThemePrototypeStyle> listByLayerIds(Collection<Long> layerIds) {
        if (CollUtil.isEmpty(layerIds)) {
            return Collections.emptyList();
        }
        TkExample example = new TkExample(DesignProductThemePrototypeStyle.class);
        example.createCriteria().andIn("designProductLayerId", layerIds);
        return dao.selectByExample(example);
    }

    public List<DesignProductThemePrototypeStyleDTO> getByLayerIds(Collection<Long> layerIds) {
        List<DesignProductThemePrototypeStyle> prototypeStyles = this.listByLayerIds(layerIds);
        return ListUtil.copyProperties(prototypeStyles, DesignProductThemePrototypeStyleDTO.class);
    }

    public void batchSave(List<DesignProductThemePrototypeStyleDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        Set<Long> layerIds = new HashSet<>();
        Set<Long> timeSigns = new HashSet<>();
        dtoList.forEach(dto -> {
            layerIds.add(dto.getDesignProductLayerId());
            timeSigns.add(dto.getThemeTimeSign());
        });
        List<DesignProductThemePrototypeStyle> oldList = listByLayerIdAndTimeSign(layerIds, timeSigns);
        List<String> uniqKey = oldList.stream().map(old -> old.getDesignProductLayerId() + "&&" + old.getStyleId() + "&&" + old.getThemeTimeSign()).collect(Collectors.toList());
        List<DesignProductThemePrototypeStyle> saveList = new ArrayList<>();
        dtoList.forEach(dto -> {
            String key = dto.getDesignProductLayerId() + "&&" + dto.getStyleId() + "&&" + dto.getThemeTimeSign();
            if (uniqKey.contains(key)) {
                return;
            }
            uniqKey.add(key);
            saveList.add(BeanUtils.copyProperties(dto, DesignProductThemePrototypeStyle.class));
        });
        this.saveBatch(saveList);
    }

    public void deleteByDesignProductIds(Collection<Long> designProductIds) {
        if (CollUtil.isEmpty(designProductIds)) {
            return;
        }
        dao.deleteByDesignProductIds(designProductIds);
    }
}
