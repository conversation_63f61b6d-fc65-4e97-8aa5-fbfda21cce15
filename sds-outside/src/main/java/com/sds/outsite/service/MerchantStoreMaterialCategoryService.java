package com.sds.outsite.service;

import cn.hutool.core.collection.CollectionUtil;
import com.beust.jcommander.internal.Maps;
import com.ps.base.service.TKBaseService;
import com.ps.ps.service.MaterialService;
import com.ps.tool.BeanTool;
import com.sds.outsite.dao.MerchantStoreMaterialCategoryDao;
import com.sds.outsite.entity.MerchantStoreMaterial;
import com.sds.outsite.entity.MerchantStoreMaterialCategory;
import com.sdsdiy.materialdata.dto.material.MaterialRespDto;
import com.sdsdiy.materialapi.dto.resp.MerchantStoreMaterialCategoryResp;
import com.ziguang.base.model.Material;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;

/**
 * 功能描述: <br>
 *
 * @Author: lin_bin
 * @Date: 2020/11/26 19:56
 */
@Service
@Log4j2
public class MerchantStoreMaterialCategoryService extends TKBaseService<MerchantStoreMaterialCategoryDao, MerchantStoreMaterialCategory> {
    @Resource
    private MaterialService materialService;
    @Resource
    private MerchantStoreMaterialService merchantStoreMaterialService;


    public List<MerchantStoreMaterialCategory> getMerchantStoreMaterialCategory(Long merchantId, Long merchantStoreId) {
        Example example = new Example(MerchantStoreMaterialCategory.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("merchantId", merchantId);
        criteria.andEqualTo("merchantStoreId", merchantStoreId);
        criteria.andEqualTo("isDelete", NO);
        return dao.selectByExample(example);
    }

    public List<MerchantStoreMaterialCategoryResp> getMerchantStoreMaterialCategoryList(Long merchantId, Long merchantStoreId) {
        // 获取该商户该店铺所有的分类
        List<MerchantStoreMaterialCategory> merchantStoreMaterialCategories = this.getMerchantStoreMaterialCategory(merchantId, merchantStoreId);
        List<MerchantStoreMaterialCategoryResp> list = BeanTool.copyBeanList(merchantStoreMaterialCategories, MerchantStoreMaterialCategoryResp.class);
        // 每个分类对应的第一个素材id
        List<MerchantStoreMaterial> storeMaterials = merchantStoreMaterialService.list(merchantId, merchantStoreId);
        Map<Long, Long> materialIdMap = storeMaterials.stream().collect(Collectors
                .toMap(MerchantStoreMaterial::getMerchantStoreMaterialCategoryId, MerchantStoreMaterial::getMaterialId));
        Map<Long, Material> materialMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(materialIdMap.values())) {
            // 查素材
            List<Material> materials = materialService.findByIds(materialIdMap.values().stream().distinct().collect(Collectors.toList()));
            materialMap = materials.stream().collect(Collectors.toMap(Material::getId, m -> m));
        }
        for (MerchantStoreMaterialCategoryResp mc : list) {
            Long materialId = materialIdMap.get(mc.getId());
            if (materialId == null) {
                continue;
            }
            Material material = materialMap.get(materialId);
            if (material == null) {
                continue;
            }
            mc.setFirstMaterial(BeanTool.copyBean(material, MaterialRespDto.class));
        }
        return list;
    }

}

