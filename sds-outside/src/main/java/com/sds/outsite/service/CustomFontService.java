package com.sds.outsite.service;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.ps.amazon.s3.S3ServiceV2;
import com.sds.outsite.constant.CustomFontConstant;
import com.sds.outsite.dto.font.FontFileRespDto;
import com.sds.outsite.dto.font.FontRespDto;
import com.sds.outsite.dto.font.MerchantFontRespDto;
import com.sds.outsite.entity.font.CustomFont;
import com.sds.outsite.mapper.CustomFontMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/02/26 10:36
 **/
@Service
public class CustomFontService {
    @Resource
    private CustomFontMapper customFontMapper;
    @Resource
    private S3ServiceV2 s3ServiceV2;
    @Resource
    private OutMerchantPlatformPermissionSetMealService outMerchantPlatformPermissionSetMealService;

    private static List<FontRespDto> getFontDto(List<CustomFont> customFonts) {
        List<FontRespDto> systemFonts= Lists.newArrayList();
        for (CustomFont customFont : customFonts) {
            FontRespDto fontRespDto=new FontRespDto();
            fontRespDto.setCustomName(customFont.getCustomName());
            fontRespDto.setFileCode(customFont.getFileCode());
            fontRespDto.setPreviewImage(customFont.getPreviewImage());
            systemFonts.add(fontRespDto);
        }
        return systemFonts.stream().sorted(Comparator.comparing(FontRespDto::getCustomName)).collect(Collectors.toList());
    }

    @NotNull
    private static List<FontRespDto> getSystemFontDto(List<CustomFont> customFonts) {
        List<FontRespDto> systemFonts= Lists.newArrayList();
        for (CustomFont customFont : customFonts) {
            FontRespDto fontRespDto=new FontRespDto();
            fontRespDto.setCustomName(customFont.getCustomName());
            fontRespDto.setFileCode(customFont.getCustomName());
            fontRespDto.setPreviewImage(customFont.getPreviewImage());
            systemFonts.add(fontRespDto);
        }
        return systemFonts.stream().sorted(Comparator.comparing(FontRespDto::getCustomName)).collect(Collectors.toList());
    }

    public MerchantFontRespDto merchantFonts(Long merchantId) {
        List<CustomFont> userCustomFonts=Lists.newArrayList();
        if(outMerchantPlatformPermissionSetMealService.isCustomFontMember(merchantId)){
            userCustomFonts = customFontMapper.getByMerchantId(merchantId, CustomFontConstant.StatusEnum.ACTIVATED.getCode());
        }
        List<CustomFont> systemCustomFonts = customFontMapper.getSystem();

        List<FontRespDto> systemFonts = getSystemFontDto(systemCustomFonts);
        List<FontRespDto> userFonts = getFontDto(userCustomFonts);
        MerchantFontRespDto respDto = new MerchantFontRespDto();
        respDto.setSystems(systemFonts);
        respDto.setUsers(userFonts);
        return respDto;
    }

    public List<FontFileRespDto> fontFiles() {
        List<CustomFont> customFonts = customFontMapper.getAllFontFiles();
        customFonts = customFonts.stream().filter(i -> i.getStatus().equalsIgnoreCase(CustomFontConstant.StatusEnum.ACTIVATED.getCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(customFonts)) {
            return Collections.emptyList();
        }
        List<FontFileRespDto> fontFileRespDtos = Lists.newArrayList();
        List<FontFileRespDto> systemFontFileRespDtos = getSystemFontFileRespDtos(customFonts);
        fontFileRespDtos.addAll(systemFontFileRespDtos);
        List<FontFileRespDto> userFontFileRespDtos = getUserFontFileRespDtos(customFonts);
        fontFileRespDtos.addAll(userFontFileRespDtos);
        return fontFileRespDtos;
    }

    @NotNull
    private List<FontFileRespDto> getUserFontFileRespDtos(List<CustomFont> customFonts) {
        List<CustomFont> users = customFonts.stream().filter(i -> i.getType().equalsIgnoreCase(CustomFontConstant.TypeEnum.USER.getCode())).collect(Collectors.toList());
        List<FontFileRespDto> userFontFileRespDtos = Lists.newArrayList();
        List<CustomFont> distinctUsers = users.stream()
            .collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(CustomFont::getFileCode))), ArrayList::new));
        for (CustomFont customFont : distinctUsers) {
            FontFileRespDto fontFileRespDto = new FontFileRespDto();
            fontFileRespDto.setFileCode(customFont.getFileCode());
            String fontDownloadUrl = s3ServiceV2.getFontDownloadUrl(customFont.getFileCode());
            fontFileRespDto.setFontUrl(fontDownloadUrl);
            userFontFileRespDtos.add(fontFileRespDto);
        }
        return userFontFileRespDtos;
    }

    @NotNull
    private List<FontFileRespDto> getSystemFontFileRespDtos(List<CustomFont> customFonts) {
        List<CustomFont> systems = customFonts.stream().filter(i -> i.getType().equalsIgnoreCase(CustomFontConstant.TypeEnum.SYSTEM.getCode())).collect(Collectors.toList());
        List<FontFileRespDto> dtos = Lists.newArrayList();
        for (CustomFont customFont : systems) {
            FontFileRespDto fontFileRespDto = new FontFileRespDto();
            fontFileRespDto.setFileCode(customFont.getPostscriptName());
            String fontDownloadUrl = s3ServiceV2.getFontDownloadUrl(customFont.getFileCode());
            fontFileRespDto.setFontUrl(fontDownloadUrl);
            dtos.add(fontFileRespDto);
        }
        return dtos;
    }
}
