package com.sds.outsite.service;

import com.ps.support.Assert;
import com.sds.outsite.dto.VariantDesignProductDTO;
import com.sds.outsite.entity.ShopifyVariantDesignProduct;
import com.sds.outsite.entity.ShoplazzaVariantDesignProduct;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/9/7
 */
@Service
public class VariantDesignProductService {
    @Resource
    private ShopifyVariantDesignProductService shopifyVariantDesignProductService;
    @Resource
    private ShoplazzaVariantDesignProductService shoplazzaVariantDesignProductService;

    public VariantDesignProductDTO getVariantDesignProductDTO(String variantId, String platform) {
        MerchantStorePlatformEnum platformEnum = MerchantStorePlatformEnum.checkCode(platform);
        // 获取变体成品
        VariantDesignProductDTO dto = new VariantDesignProductDTO();
        switch (platformEnum) {
            case SHOPIFY:
                ShopifyVariantDesignProduct shopifyVariant = shopifyVariantDesignProductService.findByShopifyVariantId(Long.parseLong(variantId));
                Assert.validateNull(shopifyVariant, "变体信息异常！！");
                dto.setMerchantId(shopifyVariant.getMerchantId());
                dto.setMerchantStoreId(shopifyVariant.getMerchantStoreId());
                break;
            case Shoplazza:
                ShoplazzaVariantDesignProduct shoplazzaVariant = shoplazzaVariantDesignProductService.findByShoplazzaVariantId(variantId);
                Assert.validateNull(shoplazzaVariant, "变体信息异常！！");
                dto.setMerchantId(shoplazzaVariant.getMerchantId());
                dto.setMerchantStoreId(shoplazzaVariant.getMerchantStoreId());
                break;
            default:
                Assert.wrong("platform error");
        }
        return dto;
    }
}
