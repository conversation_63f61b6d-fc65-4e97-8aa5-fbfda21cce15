

package com.sds.outsite.service;

import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.PrototypeResultImageDao;
import com.sds.outsite.entity.PrototypeResultImage;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class PrototypeResultImageService extends TKBaseService<PrototypeResultImageDao, PrototypeResultImage> {
    public List<PrototypeResultImage> getByPsdFileIds(List<Long> ids, Long prototypeId) {
        return dao.getByPsdFileIds(ids, prototypeId);
    }

    public List<PrototypeResultImage> getByPrototypeId(Long prototypeId) {
        TkExample example = new TkExample(PrototypeResultImage.class);
        example.createCriteria().andEqualTo("prototypeId", prototypeId);
        example.setOrderBySort("+sort");
        return dao.selectByExample(example);
    }
}

