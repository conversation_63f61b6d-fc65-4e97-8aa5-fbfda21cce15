package com.sds.outsite.service.theme;

import cn.hutool.core.collection.CollUtil;
import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.theme.DesignProductThemeRelationMaterialCategoryDao;
import com.sds.outsite.entity.theme.DesignProductThemeRelationMaterialCategory;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.designproductdata.dto.theme.DesignProductThemeCategoryImgDTO;
import com.sdsdiy.designproductdata.dto.theme.DesignProductThemeRelImgReqDTO;
import com.sdsdiy.designproductdata.dto.theme.DesignProductThemeRelationMaterialDTO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/4
 */
@Service
@Log4j2
public class DesignProductThemeRelationMaterialCategoryService extends TKBaseService<DesignProductThemeRelationMaterialCategoryDao, DesignProductThemeRelationMaterialCategory> {
    @Autowired
    private DesignProductThemeRelationMaterialService designProductThemeRelationMaterialService;

    public Map<Long, String> mapCategoryNameByDesignProduct(Long merchantId, Long designProductId) {
        List<DesignProductThemeRelationMaterialCategory> categoryList = this.listCategory(merchantId, designProductId);
        return categoryList.stream().collect(Collectors.toMap(DesignProductThemeRelationMaterialCategory::getThemeRelationMaterialCategoryId
                , DesignProductThemeRelationMaterialCategory::getName, (a, b) -> a));
    }

    public List<DesignProductThemeRelationMaterialCategory> listCategory(Long merchantId, Long designProductId) {
        TkExample example = new TkExample(DesignProductThemeRelationMaterialCategory.class);
        example.createCriteria().andEqualTo("designProductId", designProductId)
                // 过滤掉无素材的分组
                .andEqualTo("merchantId", merchantId).andGreaterThan("num", 0);
        return dao.selectByExample(example);
    }

    public Map<Long, Map<Long, Map<String, Map<Long, List<DesignProductThemeCategoryImgDTO.MaterialImgDTO>>>>> mapMaterial(Long merchantId, Long designProductId) {
        List<DesignProductThemeRelationMaterialCategory> categoryList = this.listCategory(merchantId, designProductId);
        if (CollUtil.isEmpty(categoryList)) {
            return Collections.emptyMap();
        }
        // 素材
        DesignProductThemeRelImgReqDTO reqDTO = new DesignProductThemeRelImgReqDTO();
        reqDTO.setMerchantId(merchantId).setDesignProductId(designProductId);
        Map<Long, List<DesignProductThemeRelationMaterialDTO>> materialMap = designProductThemeRelationMaterialService.listMaterial(reqDTO).stream()
                .collect(Collectors.groupingBy(DesignProductThemeRelationMaterialDTO::getThemeRelationMaterialCategoryId));
        // 转map
        return categoryList.stream()
                // 主题
                .collect(Collectors.groupingBy(DesignProductThemeRelationMaterialCategory::getThemeId
                        // 风格
                        , Collectors.groupingBy(DesignProductThemeRelationMaterialCategory::getStyleId
                                // 选项
                                , Collectors.groupingBy(DesignProductThemeRelationMaterialCategory::getOptionId
                                        // 分类
                                        , Collectors.mapping(category -> {
                                            DesignProductThemeCategoryImgDTO imgDTO = new DesignProductThemeCategoryImgDTO();
                                            imgDTO.setCategoryId(category.getThemeRelationMaterialCategoryId())
                                                    .setName(category.getName()).setNum(category.getNum())
                                                    .setMaterialList(ListUtil.copyProperties(materialMap.get(imgDTO.getCategoryId())
                                                            , DesignProductThemeCategoryImgDTO.MaterialImgDTO.class));
                                            return imgDTO;
                                            // 素材列表
                                        }, Collectors.toMap(DesignProductThemeCategoryImgDTO::getCategoryId, DesignProductThemeCategoryImgDTO::getMaterialList))))));
    }
}
