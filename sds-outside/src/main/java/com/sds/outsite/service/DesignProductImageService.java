/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKMapperBaseService;
import com.sds.outsite.dao.DesignProductImageDao;
import com.sds.outsite.entity.DesignProductImage;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class DesignProductImageService extends TKMapperBaseService<DesignProductImageDao, DesignProductImage>{
	private final String talbeName = "design_product_image";
     
    public SearchBean list(SearchBean<DesignProductImage> searchBean) {
        searchBean.setTable(talbeName);
        searchBean.setItems(dao.list(searchBean));
        searchBean.setTotalCount(dao.count(searchBean));
        return searchBean;
    }

    public void deleteById(Long merchantId, Long id) {
        DesignProductImage data = new DesignProductImage();
        data.setId(id);
        data.setMerchantId(merchantId);
        dao.delete(data);
    }
    public DesignProductImage add(DesignProductImage designProductImage){
        DesignProductImage data = this.findByContentMd5(designProductImage.getMerchantId(),designProductImage.getDesignMd5());
        if(data != null){
            return data;
        }
        designProductImage.setCreatedTime(System.currentTimeMillis());
        return this.save(designProductImage);
    }
    public DesignProductImage findByContentMd5(Long merchantId,String md5){
        DesignProductImage data = new DesignProductImage();
        data.setDesignMd5(md5);
        data.setMerchantId(merchantId);
        return this.queryOne(data);
    }
    public boolean updateByMd5(String url,Long merchantId,String md5){
        return dao.updateImage(url,merchantId,md5) > 0 ;
    }
    public List<DesignProductImage> findByCompoundIds(Long merchantId, List<String> compoundIds){
        return dao.findByCompoundIds(merchantId,compoundIds);
    }

    public List<DesignProductImage> findByCompoundId(Long merchantId, String compoundId) {
        return dao.findMapCompoundId(merchantId, compoundId);
    }


}

