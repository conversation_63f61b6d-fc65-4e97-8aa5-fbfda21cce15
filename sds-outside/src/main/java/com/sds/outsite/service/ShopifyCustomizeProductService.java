package com.sds.outsite.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.parser.ParserConfig;
import com.sds.outsite.dto.ShopifyVariantDto;
import com.sds.outsite.shopify.ShopifyProductParam;
import com.sds.outsite.util.ShopifyUrlUtil;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.JsonUtil;
import com.sdsdiy.ecommerceapi.dto.shopify.ProductRespDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ShopifyCustomizeProductService {
    private static final Logger logger = LoggerFactory.getLogger(ShopifyCustomizeProductService.class);


    public ShopifyVariantDto getVariant(String shopName, long variantId, String token) {
        String url = ShopifyUrlUtil.getVariantUrl(shopName, variantId);
        HttpResponse response = HttpRequest.get(url)
            .header(ShopifyUrlUtil.TOKEN_HEADER, token)
            .execute();
        if (!response.isOk()) {
            logger.error("Get variant error, shopName={}, id={}, token={}, status={}, body={}", shopName, variantId, token, response.getStatus(), response.body());
            throw new BusinessException(response.body());
        }

        return variantResponseBodyToDto(response.body());
    }

    /**
     * 创建产品
     *
     * @param shopName      shopify店铺名字
     * @param merchantToken shopify店铺所需token
     * @param param         产品参数
     *
     * @return
     */
    public ProductRespDto createProduct(String shopName, String merchantToken, ShopifyProductParam param) {
        String url = ShopifyUrlUtil.createProductUrl(shopName);
        String bodyContent = getCreateProductRequestBody(param);
        logger.info("Create shopify product, shopName={}, body={}", shopName, bodyContent);
        HttpResponse response = HttpRequest.post(url)
            .header(ShopifyUrlUtil.TOKEN_HEADER, merchantToken)
            .body(bodyContent)
            .execute();
        if (!response.isOk()) {
            logger.error("Create shopify product error, shopName={}, status={}, body={}", shopName, response.getStatus(), bodyContent);
            throw new BusinessException(response.body());
        }

        return productResponseBodyToDto(response.body());
    }

    private static ProductRespDto productResponseBodyToDto(String body) {
        ParserConfig parserConfig = new ParserConfig();
        parserConfig.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
        JSONObject pageJson = JSON.parseObject(body).getJSONObject("product");
        return JSON.parseObject(pageJson.toJSONString(), ProductRespDto.class, parserConfig);
    }

    private static String getCreateProductRequestBody(ShopifyProductParam productParam) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("product", productParam);
        return JsonUtil.objToSnakeCaseJsonString(jsonObject);
    }


    private static ShopifyVariantDto variantResponseBodyToDto(String body) {
        ParserConfig parserConfig = new ParserConfig();
        parserConfig.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
        JSONObject pageJson = JSON.parseObject(body).getJSONObject("variant");
        return JSON.parseObject(pageJson.toJSONString(), ShopifyVariantDto.class, parserConfig);
    }
}
