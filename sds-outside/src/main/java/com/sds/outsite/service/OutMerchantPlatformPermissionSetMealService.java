package com.sds.outsite.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.sds.outsite.constant.MerchantPlatformPermissionSetMealConstant;
import com.sds.outsite.entity.MerchantPlatformPermissionSetMeal;
import com.sds.outsite.mapper.MerchantPlatformPermissionSetMealMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/26 10:36
 **/
@Service
public class OutMerchantPlatformPermissionSetMealService {
    @Resource
    private MerchantPlatformPermissionSetMealMapper merchantPlatformPermissionSetMealMapper;

    public BigDecimal getMemberLevel(Long merchantId){
        List<MerchantPlatformPermissionSetMeal> members = merchantPlatformPermissionSetMealMapper.getByMerchantId(
            merchantId,
            MerchantPlatformPermissionSetMealConstant.StatusEnum.STATUS_USABLE.getCode(),
            MerchantPlatformPermissionSetMealConstant.SetMealTypeEnum.MEMBER.getCode());
        if(CollUtil.isEmpty(members)){
            return MerchantPlatformPermissionSetMealConstant.MemberLevelEnum.V0.getValue();
        }
        MerchantPlatformPermissionSetMeal merchantPlatformPermissionSetMeal = members.stream().max(Comparator.comparing(MerchantPlatformPermissionSetMeal::getLevel)).orElse(null);
        return null!=merchantPlatformPermissionSetMeal?merchantPlatformPermissionSetMeal.getLevel():MerchantPlatformPermissionSetMealConstant.MemberLevelEnum.V0.getValue();
    }

    public boolean isCustomFontMember(Long merchantId){
        BigDecimal memberLevel = getMemberLevel(merchantId);
        return NumberUtil.isGreater(memberLevel,MerchantPlatformPermissionSetMealConstant.MemberLevelEnum.Vd.getValue());
    }

}
