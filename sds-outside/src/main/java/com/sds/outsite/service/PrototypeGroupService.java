

package com.sds.outsite.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sds.outsite.dao.PrototypeGroupDao;
import com.sds.outsite.entity.PrototypeGroup;
import org.springframework.stereotype.Service;


@Service
public class PrototypeGroupService extends ServiceImpl<PrototypeGroupDao, PrototypeGroup> {

    PrototypeGroup getByPrototypeId(Long prototypeId) {
        return baseMapper.getByPrototypeId(prototypeId);
    }


}

