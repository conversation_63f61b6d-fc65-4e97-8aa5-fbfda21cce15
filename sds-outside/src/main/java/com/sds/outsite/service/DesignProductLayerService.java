/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import cn.hutool.core.collection.CollUtil;
import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKMapperBaseService;
import com.ps.support.BeanUtils;
import com.sds.outsite.dao.DesignProductLayerDao;
import com.sds.outsite.entity.DesignProductLayer;
import com.sdsdiy.designproductdata.dto.DesignProductLayerMcDTO;
import com.sdsdiy.designproductdata.dto.DesignProductThemePrototypeStyleDTO;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class DesignProductLayerService extends TKMapperBaseService<DesignProductLayerDao, DesignProductLayer> {
	private final String talbeName = "design_product_layer";
     
    public SearchBean list(SearchBean<DesignProductLayer> searchBean) {

        searchBean.setTable(talbeName);
        searchBean.setItems(dao.list(searchBean));
        searchBean.setTotalCount(dao.count(searchBean));
        return searchBean;
    }

    public List<DesignProductLayer> findByCompoundId(String compoundId){
        DesignProductLayer designProductLayer = new DesignProductLayer();
        designProductLayer.setCompoundId(compoundId);
        return this.query(designProductLayer);
    }
    /***
     *
     *
     * *********/





    public void deleteById(Long merchantId, Long id) {
        DesignProductLayer data = new DesignProductLayer();
        data.setId(id);
        data.setMerchantId(merchantId);
        dao.delete(data);
    }

    public DesignProductLayer add(DesignProductLayerMcDTO layerDTO, List<DesignProductThemePrototypeStyleDTO> styleDTOList) {
        DesignProductLayer data = this.findByContentMd5(layerDTO.getMerchantId(), layerDTO.getContentMd5());
        if (data != null) {
            return data;
        }
        DesignProductLayer designProductLayer = BeanUtils.copyProperties(layerDTO, DesignProductLayer.class);
        this.save(designProductLayer);
        // 主题设计需要保存关联表信息
        if (CollUtil.isNotEmpty(layerDTO.getStyles())) {
            layerDTO.getStyles().forEach(s -> s.setDesignProductLayerId(designProductLayer.getId()));
            styleDTOList.addAll(layerDTO.getStyles());
        }
        return designProductLayer;
    }
    public DesignProductLayer findByContentMd5(Long merchantId,String md5){
        DesignProductLayer designProductLayer = new DesignProductLayer();
        designProductLayer.setMerchantId(merchantId);
        designProductLayer.setContentMd5(md5);
        return this.queryOne(designProductLayer);
    }
    public DesignProductLayer findById(Long merchantId,Long id){
        DesignProductLayer designProductLayer = new DesignProductLayer();
        designProductLayer.setMerchantId(merchantId);
        designProductLayer.setId(id);
        return this.queryOne(designProductLayer);
    }
    public void updateById(DesignProductLayer designProductLayer){
        dao.updateByPrimaryKeySelective(designProductLayer);
    }
    public void updateContentById(String content,DesignProductLayer designProductLayer){
        DesignProductLayer updateDesignLayer = new DesignProductLayer();
        updateDesignLayer.setContent(content);
        updateDesignLayer.setSrcFilecode(content);
        updateDesignLayer.setId(designProductLayer.getId());
        updateDesignLayer.setMerchantId(designProductLayer.getMerchantId());

        dao.updateByPrimaryKeySelective(designProductLayer);
    }
    public List<DesignProductLayer> findByCompoundId(Long merchantId, String compoundId){
        DesignProductLayer designProductLayer = new DesignProductLayer();
        designProductLayer.setCompoundId(compoundId);
        designProductLayer.setMerchantId(merchantId);
        return this.query(designProductLayer);
    }
    public List<DesignProductLayer> findByCompoundIds(Long merchantId, List<String> compoundIds){
        return dao.findByCompoundIds(merchantId,compoundIds);
    }
}

