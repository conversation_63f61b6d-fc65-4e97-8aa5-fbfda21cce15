package com.sds.outsite.service.theme;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.theme.DesignProductLayerThemeRelDao;
import com.sds.outsite.entity.theme.DesignProductLayerThemeRel;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.designproductdata.dto.DesignProductLayerThemeRelDTO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@Service
public class DesignProductLayerThemeRelService extends TKBaseService<DesignProductLayerThemeRelDao, DesignProductLayerThemeRel> {

    /**
     * 根据成品id和图层id批量查询数据
     *
     * @param compoundIds 成品id
     * @param layerIds    图层id
     * @return 图层和主题的关联关系
     */
    public Map<Long, DesignProductLayerThemeRelDTO> batchGet(List<String> compoundIds, List<Long> layerIds) {
        if (CollUtil.isEmpty(compoundIds) || CollUtil.isEmpty(layerIds)) {
            return Maps.newHashMap();
        }
        TkExample example = new TkExample(DesignProductLayerThemeRel.class);
        example.createCriteria().andIn("compoundId", compoundIds)
                .andIn("designProductLayerId", layerIds);
        List<DesignProductLayerThemeRel> layerThemeRels = dao.selectByExample(example);
        List<DesignProductLayerThemeRelDTO> layerThemeRelDTOList = ListUtil.copyProperties(layerThemeRels, DesignProductLayerThemeRelDTO.class);
        return layerThemeRelDTOList.stream().collect(Collectors.toMap(DesignProductLayerThemeRelDTO::getDesignProductLayerId, Function.identity()));
    }

}
