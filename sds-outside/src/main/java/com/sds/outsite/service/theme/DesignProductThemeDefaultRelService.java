package com.sds.outsite.service.theme;

import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.theme.DesignProductThemeDefaultRelDao;
import com.sds.outsite.entity.theme.DesignProductThemeDefaultRel;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/2/10
 */
@Service
@Log4j2
public class DesignProductThemeDefaultRelService extends TKBaseService<DesignProductThemeDefaultRelDao, DesignProductThemeDefaultRel> {

    public DesignProductThemeDefaultRel getDefaultDesignProduct(Long halfDesignProductId) {
        DesignProductThemeDefaultRel where = new DesignProductThemeDefaultRel();
        where.setHalfDesignProductId(halfDesignProductId);
        return dao.selectOne(where);
    }

    public void saveOne(Long halfDesignProductId, Long defaultDesignProductId, String defaultKeyId) {
        DesignProductThemeDefaultRel where = new DesignProductThemeDefaultRel();
        where.setCreateTime(new Date()).setHalfDesignProductId(halfDesignProductId)
                .setDefaultDesignProductId(defaultDesignProductId).setDefaultKeyId(defaultKeyId);
        dao.insert(where);
    }
}
