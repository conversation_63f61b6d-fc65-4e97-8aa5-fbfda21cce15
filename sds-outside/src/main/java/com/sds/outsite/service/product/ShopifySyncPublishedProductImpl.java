package com.sds.outsite.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sds.outsite.bo.PublishedProductBo;
import com.sds.outsite.entity.product.PublishedProduct;
import com.sds.outsite.entity.product.PublishedVariant;
import com.sds.platform.sdk.common.ApiRespResult;
import com.sds.platform.sdk.shopify.ShopifyApiClient;
import com.sds.platform.sdk.shopify.common.ShopifyIdEnum;
import com.sds.platform.sdk.shopify.product.ProductGetApi;
import com.sds.platform.sdk.shopify.product.ProductGetResp;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.ecommerceapi.dto.shopify.ImageRespDto;
import com.sdsdiy.ecommerceapi.dto.shopify.ProductRespDto;
import com.sdsdiy.ecommerceapi.dto.shopify.VariantRespDto;
import com.sdsdiy.ecommerceapi.shopify.constant.ProductStatus;
import com.sdsdiy.ecommerceapi.shopify.req.ProductReqParam;
import com.sdsdiy.ecommercedata.bo.OutsiteSyncProductMsg;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class ShopifySyncPublishedProductImpl implements ISyncPublishedProduct {

    @Override
    public String getPlatformCode() {
        return MerchantStorePlatformEnum.SHOPIFY.getCode();
    }

    public static ProductGetResp shopifyProductDetail(String shopName, String token, String productId) {
        ShopifyApiClient shopifyApiClient = new ShopifyApiClient(shopName, token, true);
        ProductGetApi api = new ProductGetApi(String.valueOf(productId));
        ApiRespResult execute = shopifyApiClient.execute(api);
        System.out.println(JSON.toJSONString(execute));
        return shopifyApiClient.executeAndParse(api);
    }

    public static void main(String[] args) {
        ProductGetResp shpatCd51b6b0ae3f90ef88847ed20264fd52 = shopifyProductDetail("hageuj-8w", "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", "8065827635394");
        System.out.println(JSON.toJSONString(shpatCd51b6b0ae3f90ef88847ed20264fd52));
    }

    @Override
    public PublishedProductBo analyseProductInfoFromPlatform(OutsiteSyncProductMsg msg) {
        ProductReqParam param = new ProductReqParam();
        param.setProductId(Long.parseLong(msg.getProductId()));
        param.setShopName(msg.getShopName());
        param.setToken(msg.getToken());

        ProductGetResp productGetResp = shopifyProductDetail(msg.getShopName(), msg.getToken(), msg.getProductId());
        if (productGetResp == null) {
            return null;
        }
        PublishedProduct publishedProduct = new PublishedProduct();
        publishedProduct.setProductId(msg.getProductId());
        publishedProduct.setPublishedAt(msg.getPublishedAt());
        publishedProduct.setPlatform(this.getPlatformCode());
        publishedProduct.setShopName(msg.getShopName());
        publishedProduct.setCurrencyCode(msg.getCurrencyCode());
        publishedProduct.setCurrencySymbol(msg.getCurrencySymbol());
        publishedProduct.setPublished(ProductStatus.ACTIVE.getCode().equalsIgnoreCase(productGetResp.getStatus()));

        publishedProduct.setTitle(productGetResp.getTitle());
        ProductGetResp.MediaDTO mediaResp = productGetResp.getMedia();
        if (Objects.nonNull(mediaResp)) {
            List<ProductGetResp.MediaDTO.NodesDTO> nodes = mediaResp.getNodes();
            if (CollUtil.isNotEmpty(nodes)) {
                ProductGetResp.MediaDTO.NodesDTO nodesDTO = nodes.get(0);
                if (Objects.nonNull(nodesDTO.getImage()) && StrUtil.isNotEmpty(nodesDTO.getImage().getUrl())) {

                    ImageRespDto image = new ImageRespDto();
                    image.setSrc(nodesDTO.getImage().getUrl());
                    publishedProduct.setImage(image.getSrc());
                }
            }
        }

        List<PublishedVariant> variants = CollUtil.newArrayList();
        Set<String> publishedVariantIds = msg.getPublishedVariantIds();
        ProductGetResp.VariantsDTO variantsRespList = productGetResp.getVariants();
        PublishedProductBo bo = new PublishedProductBo();
        bo.setProduct(publishedProduct);

        if (Objects.isNull(variantsRespList) || CollUtil.isEmpty(variantsRespList.getEdges())) {
            bo.setVariantList(variants);
            return bo;
        }
        List<ProductGetResp.VariantsDTO.EdgesDTO> edges = variantsRespList.getEdges();
        for (ProductGetResp.VariantsDTO.EdgesDTO edgesDTO : edges) {
            ProductGetResp.VariantsDTO.EdgesDTO.NodeDTO node = edgesDTO.getNode();
            if (Objects.isNull(node)) {
                continue;
            }
            Long variantId = ShopifyIdEnum.ProductVariant.parseIdLong(node.getId());
            if (!publishedVariantIds.contains(variantId.toString())) {
                continue;
            }
            PublishedVariant variantEntity = new PublishedVariant();
            variantEntity.setProductId(msg.getProductId());
            variantEntity.setVariantId(variantId.toString());
            variantEntity.setPrice(new BigDecimal(node.getPrice()));
            variantEntity.setPlatform(this.getPlatformCode());
            variants.add(variantEntity);
        }

        bo.setVariantList(variants);

        return bo;
    }


    private PublishedProduct productToEntity(ProductRespDto productRespDto, OutsiteSyncProductMsg msg) {
        PublishedProduct entity = new PublishedProduct();
        entity.setProductId(msg.getProductId());
        entity.setPublishedAt(msg.getPublishedAt());
        entity.setPlatform(this.getPlatformCode());
        entity.setShopName(msg.getShopName());
        entity.setCurrencyCode(msg.getCurrencyCode());
        entity.setCurrencySymbol(msg.getCurrencySymbol());
        entity.setPublished(productRespDto.onshelf());

        entity.setTitle(productRespDto.getTitle());

        ImageRespDto image = productRespDto.getImage();
        entity.setImage(image.getSrc());

        return entity;
    }

    private List<PublishedVariant> getVariants(ProductRespDto productRespDto, OutsiteSyncProductMsg msg) {
        List<PublishedVariant> variants = CollUtil.newArrayList();
        Set<String> publishedVariantIds = msg.getPublishedVariantIds();
        if (CollUtil.isEmpty(productRespDto.getVariants())) {
            return variants;
        }

        for (VariantRespDto variant : productRespDto.getVariants()) {
            if (!publishedVariantIds.contains(variant.getId().toString())) {
                continue;
            }
            PublishedVariant variantEntity = new PublishedVariant();
            variantEntity.setProductId(msg.getProductId());
            variantEntity.setVariantId(variant.getId().toString());
            variantEntity.setPrice(variant.getPrice());
            variantEntity.setPlatform(this.getPlatformCode());
            variants.add(variantEntity);
        }

        return variants;
    }
}
