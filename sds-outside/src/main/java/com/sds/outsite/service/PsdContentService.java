/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2020
 */


package com.sds.outsite.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.PsdContentDao;
import com.sds.outsite.entity.PsdContent;
import com.sdsdiy.productapi.dto.PsdContentDto;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 功能描述: <br>
 * @Author: lin_bin
 * @Date: 2021/4/28 21:32
 */

@Service
@Log4j2
public class PsdContentService extends TKBaseService<PsdContentDao, PsdContent> {

    @Resource
    PsdInfoService psdInfoService;
    /**
     * 保存生成的psd内容
     * @param psdContentDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePsdContent(PsdContentDto psdContentDto){
        JSONObject jsonObject = JSONObject.parseObject(psdContentDto.getValue(), Feature.OrderedField);
        JSONArray psdFrames = jsonObject.getJSONArray("psdFrames");
        Integer width = jsonObject.getInteger("width");
        Integer height = jsonObject.getInteger("height");
        psdInfoService.save(psdContentDto.getPsdCode(),width,height);
        this.deleteByCode(psdContentDto.getPsdCode());
        for(int i=0;i<psdFrames.size();i++){
            JSONObject data = psdFrames.getJSONObject(i);
            PsdContent psdContent = new PsdContent();
            psdContent.setPsdCode(psdContentDto.getPsdCode());
            psdContent.setValue(JSONObject.toJSONString(data));
            this.save(psdContent);
        }
    }

    public void deleteByCode(String psdCode){
        dao.deleteByCode(psdCode);
    }

    public List<PsdContent> getPsdContentByCode(List<String> psdCode){
        return dao.findByCodes(psdCode);
    }
}

