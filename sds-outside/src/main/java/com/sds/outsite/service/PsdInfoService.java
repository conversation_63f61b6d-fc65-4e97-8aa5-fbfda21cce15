/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2020
 */


package com.sds.outsite.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.PsdContentDao;
import com.sds.outsite.dao.PsdInfoDao;
import com.sds.outsite.entity.PsdContent;
import com.sds.outsite.entity.PsdInfo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功能描述: <br>
 * @Author: lin_bin
 * @Date: 2021/4/28 21:32
 */

@Service
@Log4j2
public class PsdInfoService extends TKBaseService<PsdInfoDao, PsdInfo> {
    public void save(String psdCode,Integer width,Integer height){
        this.deleteByCode(psdCode);
        PsdInfo psdInfo = new PsdInfo();
        psdInfo.setPsdCode(psdCode);
        psdInfo.setWidth(width);
        psdInfo.setHeight(height);
        this.save(psdInfo);
    }

    public void deleteByCode(String psdCode){
        PsdInfo psdInfo = new PsdInfo();
        psdInfo.setPsdCode(psdCode);
        dao.delete(psdInfo);
    }

    public List<PsdInfo> getByCodes(List<String> psdCodes){
        return dao.findByCodes(psdCodes);
    }
}

