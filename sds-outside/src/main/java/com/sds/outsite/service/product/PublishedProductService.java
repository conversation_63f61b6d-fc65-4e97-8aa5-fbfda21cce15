package com.sds.outsite.service.product;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.sds.outsite.dto.product.PublishedProductDto;
import com.sds.outsite.entity.product.PublishedProduct;
import com.sds.outsite.entity.product.PublishedVariant;
import com.sds.outsite.mapper.PublishedProductMapper;
import com.sds.outsite.mapper.PublishedVariantMapper;
import com.sds.outsite.param.PublishedProductQueryParam;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PublishedProductService {

    private final PublishedProductMapper publishedProductMapper;
    private final PublishedVariantMapper publishedVariantMapper;

    public PublishedProductService(
        PublishedProductMapper publishedProductMapper,
        PublishedVariantMapper publishedVariantMapper
    ) {
        this.publishedProductMapper = publishedProductMapper;
        this.publishedVariantMapper = publishedVariantMapper;
    }

    public PageListResultDTO<PublishedProductDto> getPage(PublishedProductQueryParam param) {
        PageListResultDTO<PublishedProductDto> result = new PageListResultDTO<>();
        result.setCurrentPage(param.getCurrentPage());
        result.setPageSize(param.getPageSize());

        int limit = param.getPageSize();
        int offset = (param.getCurrentPage() - 1) * limit;

        // 搜索产品时不需要处理第一个特定产品，正常分页
        if (StrUtil.isNotEmpty(param.getProductName())) {
            PageInfo<PublishedProduct> pageInfo = publishedProductMapper.findPageByShopNameAndOffset(param, limit, offset);
            result.setTotalCount((int) pageInfo.getTotal());
            result.setList(entityListToDto(param.getPlatform(), pageInfo.getList()));
            return result;
        }

        Optional<PublishedProduct> optionalPublishedProduct = getDesignatedProduct(param.getPlatform(), param.getVariantId());
        if (!optionalPublishedProduct.isPresent()) {
            PageInfo<PublishedProduct> pageInfo = publishedProductMapper.findPageByShopNameAndOffset(param, limit, offset);
            result.setTotalCount((int) pageInfo.getTotal());
            result.setList(entityListToDto(param.getPlatform(), pageInfo.getList()));
            return result;
        }

        PublishedProduct designatedProduct = optionalPublishedProduct.get();
        param.setDesignatedProductId(designatedProduct.getProductId());
        if (param.getCurrentPage() == 1) {
            limit = limit - 1;
            offset = 0;
        } else {
            offset = offset - 1;
        }

        PageInfo<PublishedProduct> pageInfo = publishedProductMapper.findPageByShopNameAndOffset(param, limit, offset);
        List<PublishedProduct> publishedProducts = pageInfo.getList();

        if (param.getCurrentPage() == 1) {
            publishedProducts.add(0, designatedProduct);
        }

        result.setTotalCount((int) pageInfo.getTotal() + 1);
        result.setList(entityListToDto(param.getPlatform(), publishedProducts));

        return result;
    }

    /**
     * 计算指定变体的对应产品信息，为了塞在第一页第一个
     *
     * @param platform
     * @param designatedVariantId
     *
     * @return
     */
    private Optional<PublishedProduct> getDesignatedProduct(String platform, String designatedVariantId) {
        if (StrUtil.isEmpty(designatedVariantId)) {
            return Optional.empty();
        }
        PublishedVariant designatedVariant = publishedVariantMapper.findOneByPlatformAndVariantId(platform, designatedVariantId);
        if (ObjectUtil.isNull(designatedVariant)) {
            return Optional.empty();
        }
        String designatedProductId = designatedVariant.getProductId();
        PublishedProduct designatedProduct = publishedProductMapper.findOne(platform, designatedProductId);
        return Optional.ofNullable(designatedProduct);
    }

    private List<PublishedProductDto> entityListToDto(String platform, List<PublishedProduct> publishedProducts) {
        List<PublishedProductDto> dtos = new ArrayList<>();

        Set<String> productIds = publishedProducts.stream().map(PublishedProduct::getProductId).collect(Collectors.toSet());
        List<PublishedVariant> variants = publishedVariantMapper.findAll(platform, productIds);

        Multimap<String, BigDecimal> priceMap = HashMultimap.create();
        Multimap<String, String> productIdToVariantIdsMap = HashMultimap.create();
        for (PublishedVariant variant : variants) {
            priceMap.put(variant.getProductId(), variant.getPrice());
            productIdToVariantIdsMap.put(variant.getProductId(), variant.getVariantId());
        }

        BigDecimal minPrice = BigDecimal.ZERO;
        BigDecimal maxPrice = BigDecimal.ZERO;
        for (PublishedProduct entity : publishedProducts) {
            maxPrice = BigDecimal.ZERO;
            minPrice = BigDecimal.ZERO;
            PublishedProductDto dto = new PublishedProductDto();
            BeanUtils.copyProperties(entity, dto);

            List<String> variantIds = new ArrayList<>(productIdToVariantIdsMap.get(entity.getProductId()));
            dto.setVariantIds(variantIds);

            List<BigDecimal> priceList = new ArrayList<>(priceMap.get(entity.getProductId()));
            for (int i = 0; i < priceList.size(); i++) {
                BigDecimal currentPrice = priceList.get(i);
                if (i == 0) {
                    minPrice = currentPrice;
                    maxPrice = currentPrice;
                }
                if (maxPrice.compareTo(currentPrice) < 0) {
                    maxPrice = currentPrice;
                }
                if (minPrice.compareTo(currentPrice) > 0) {
                    minPrice = currentPrice;
                }
            }
            dto.setMinPrice(minPrice);
            dto.setMaxPrice(maxPrice);

            dtos.add(dto);
        }

        return dtos;
    }
}
