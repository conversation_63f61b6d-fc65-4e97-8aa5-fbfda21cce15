/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.OldPrototypeDao;
import com.ps.ps.dao.PrototypeDao;
import com.ps.ps.dao.TaskDao;
import com.ps.ps.service.*;
import com.ps.ps.service.linstener.BaseEventService;
import com.ps.support.IdGenerator;
import com.ps.support.contant.SpecialUserId;
import com.sds.outsite.entity.DesignProductImage;
import com.sds.outsite.entity.DesignProductLayer;
import com.sds.outsite.entity.PsdContent;
import com.sds.outsite.entity.PsdInfo;
import com.sdsdiy.designproductapi.contant.DesignProductEventConstant;
import com.sdsdiy.designproductapi.dto.DesignProductCreateMessageDto;
import com.sdsdiy.designproductapi.myenum.EnumDesignProductEvenTags;
import com.sdsdiy.productapi.dto.PsdContentDto;
import com.ziguang.base.dto.*;
import com.ziguang.base.model.*;
import com.ziguang.base.support.JsonUtil;
import com.ziguang.base.support.ListUtils;
import com.ziguang.base.support.StringUtils;
import groovy.util.logging.Log;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Log4j2
public class PsdManageService {
    @Autowired
    PsdContentService psdContentService;

    @Autowired
    PsdInfoService psdInfoService ;

    public void addContent(String id) {
        String host = "https://cdn-engine-out.sdspod.com/cut?filecode=" + id;
        String psdValue = HttpUtil.get(host);
        com.sdsdiy.productapi.dto.PsdContentDto psdContentDto = new PsdContentDto();
        log.info("add content host {}",host);
        psdContentDto.setPsdCode(id);
        psdContentDto.setValue(psdValue);
        psdContentService.savePsdContent(psdContentDto);
    }

    public Map<String, Object> getPsdContentByCode(List<String> ids) {
        List<PsdContent> psdContentList = psdContentService.getPsdContentByCode(ids);
        List<PsdInfo> psdInfos = psdInfoService.getByCodes(ids);
        Map<String,Object> psdContentDtoResult = new HashMap<>();
        Map<String,List<PsdContent>> psdByPsdCode = psdContentList.stream().collect(Collectors.groupingBy(PsdContent::getPsdCode));
        Map<String,PsdInfo> psdInfoMap = psdInfos.stream().collect(Collectors.toMap(PsdInfo::getPsdCode, Function.identity()));
        for(String id:ids){
            List<PsdContent> psdContents = psdByPsdCode.get(id);
            if (null != psdContents){
                List<JSONObject>  jsonObjectList = Lists.newArrayList();
                for (PsdContent psdContent : psdContents) {
                    jsonObjectList.add(JSONObject.parseObject(psdContent.getValue(), Feature.OrderedField));
                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("psdFrames",jsonObjectList);
                PsdInfo psdInfo = psdInfoMap.get(id);
                if(psdInfo != null){
                    jsonObject.put("width",psdInfo.getWidth());
                    jsonObject.put("height",psdInfo.getHeight());
                }
                id = id.substring(0,id.length()-4);
                psdContentDtoResult.put(id,jsonObject);
            }
        }

        return psdContentDtoResult;
    }
}

