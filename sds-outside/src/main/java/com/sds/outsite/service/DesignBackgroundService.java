package com.sds.outsite.service;

import com.ps.base.service.TKMapperBaseService;
import com.ps.support.utils.ConvertUtil;
import com.ziguang.base.dto.DesignBackgroundDTO;
import com.sds.outsite.dao.DesignBackgroundDao;
import com.sds.outsite.entity.DesignBackground;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


/**
 * Created by yangyeze on 2020/9/12.
 */
@Service
public class DesignBackgroundService extends TKMapperBaseService<DesignBackgroundDao, DesignBackground> {
    private static Logger logger = LoggerFactory.getLogger(DesignBackgroundService.class);

    public DesignBackgroundDTO findDtoById(Long id){
        return ConvertUtil.dtoConvert(this.get(id), DesignBackgroundDTO.class);

    }

    public void updateRawFile(Long id , String rawFile){
        DesignBackground designBackground = new DesignBackground();
        designBackground.setId(id);
        designBackground.setRawFile(rawFile);
        update(designBackground);
    }


}
