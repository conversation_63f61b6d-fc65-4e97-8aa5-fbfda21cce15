/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKMapperBaseService;
import com.ps.exception.BusinessException;
import com.ps.ps.dao.MaterialKeywordDao;
import com.ps.ps.dao.OldPrototypeDao;
import com.ps.ps.service.*;
import com.ps.support.Assert;
import com.ps.support.IdGenerator;
import com.ps.support.MapListUtil;
import com.ps.support.utils.ConvertUtil;
import com.ps.support.utils.HttpClientUtil;
import com.ps.support.utils.StringUtils;
import com.ps.tool.cbt.common.util.ALGUtil;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sds.outsite.dao.DesignProductDao;
import com.sds.outsite.dto.DesignProductInsertDto;
import com.sds.outsite.entity.DesignProduct;
import com.sds.outsite.entity.DesignProductImage;
import com.sds.outsite.entity.DesignProductLayer;
import com.sds.outsite.entity.theme.DesignProductThemePrototypeStyle;
import com.sds.outsite.entity.theme.DesignProductThemeRelationMaterial;
import com.sds.outsite.entity.theme.DesignProductThemeRelationMaterialCategory;
import com.sds.outsite.service.theme.DesignProductThemePrototypeStyleService;
import com.sds.outsite.service.theme.DesignProductThemeRelationMaterialCategoryService;
import com.sds.outsite.service.theme.DesignProductThemeRelationMaterialService;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.core.mq.biz.ImgMqService;
import com.sdsdiy.designproductdata.constant.DesignProductTypeConstant;
import com.sdsdiy.designproductdata.dto.DesignProductThemePrototypeStyleDTO;
import com.ziguang.base.dto.DesignProductDTO;
import com.ziguang.base.dto.DesignProductImageDTO;
import com.ziguang.base.dto.ReplaceLayersContent;
import com.ziguang.base.model.*;
import com.ziguang.base.support.ListUtils;
import com.ziguang.base.support.contant.MaterialGroupType;
import com.ziguang.base.support.contant.SiteCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DesignProductService extends TKMapperBaseService<DesignProductDao, DesignProduct> {
    private final String talbeName = "design_product";
    private static final int STATUS_WAIT = 1;
    private static final long MAX_ENDPRODUCT_ID = 60000000000000000L;
    private static final int STATUS_FINISH = 2;
    private static final int STATUS_DELETE = 99;
    private static final String ES_INDEX = "sds_design_product";
    private static final Long ZIGUANG_ID = 1L;

    @Value("${sds.design.host}")
    private String designHost;

    @Autowired
    TaskChildLayerService taskChildLayerService;
    @Autowired
    private CompounService compounService;

    public String getDesignHost() {
        return this.designHost;
    }

    public void setDesignHost(String designHost) {
        this.designHost = designHost;
    }

    @Autowired
    PrototypeLayerService prototypeLayerService;
    @Resource
    private PrototypeLensService prototypeLensService;

    public static int getMaterialGroupTypeNormal() {
        return MaterialGroupType.CHILD.getStatus();
    }

    public static int getMaterialGroupTypeParent() {
        return MaterialGroupType.PARENT.getStatus();
    }

    public static int getMaterialGroupTypeSingle() {
        return MaterialGroupType.SINGLE.getStatus();
    }


    /***************************
     *
     * 有用的放这前面，没用的放后面
     * 没用的找时间全删了
     *
     *
     *
     * *****************************************/

    @Autowired
    DesignProductDeleteHistoryService designProductDeleteHistoryService;
    @Autowired
    private DesignProductThemePrototypeStyleService designProductThemePrototypeStyleService;
    @Autowired
    private DesignProductThemeRelationMaterialCategoryService designProductThemeRelationMaterialCategoryService;
    @Autowired
    private DesignProductThemeRelationMaterialService designProductThemeRelationMaterialService;


    public DesignProductInsertDto findDesinProductInsertDto(Long merchantId, String keyIds) {
        List<String> keyIdLIst = com.ziguang.base.support.StringUtils.stringToStringList(keyIds);
        List<DesignProduct> designProducts = this.dao.findByKeyIds(merchantId, keyIdLIst);
        if (CollectionUtils.isEmpty(designProducts)) {
            return null;
        }
        List<String> compoundIds = Lists.newArrayList();
//        List<Long> designProductIds = Lists.newArrayList();
        for (DesignProduct designProduct : designProducts) {
            compoundIds.add(designProduct.getCompoundId());
//            designProductIds.add(designProduct.getId());
        }
        List<Long> prototypeIds = designProducts.stream().map(dp -> dp.getPrototypeId()).collect(Collectors.toList());
        List<DesignProductImage> designProductImages = this.designProductImageService.findByCompoundIds(merchantId, compoundIds);
        List<DesignProductLayer> designProductLayers = this.designProductLayerService.findByCompoundIds(merchantId, compoundIds);
        List<PrototypeLayer> prototypeLayers = this.prototypeLayerService.findByPrototypeIds(prototypeIds);

        DesignProductInsertDto insertDto = new DesignProductInsertDto();
        insertDto.setDesignProducts(designProducts);
        insertDto.setDesignProductImages(designProductImages);
        insertDto.setDesignProductLayers(designProductLayers);
        // style
        List<DesignProductThemePrototypeStyle> styleList = this.designProductThemePrototypeStyleService.listByLayerIds(
                designProductLayers.stream().map(DesignProductLayer::getId).collect(Collectors.toList()));
        styleList.forEach(s -> s.setId(null));
        insertDto.setDesignProductThemePrototypeStyleList(styleList);
        insertDto.setPrototypeLayers(prototypeLayers);
        return insertDto;

    }

    public DesignProduct findById(Long id) {
        return this.dao.selectByPrimaryKey(id);
    }

    public DesignProduct findByKeyId(String keyId) {
        return this.dao.findByKeyId(keyId);
    }

    public static String getEsIndex() {
        return ES_INDEX;
    }

    public SearchBean list(SearchBean<DesignProduct> searchBean) {
        searchBean.setTable(this.talbeName);
        searchBean.setItems(this.dao.list(searchBean));
        searchBean.setTotalCount(this.dao.count(searchBean));
        return searchBean;
    }

    public static int getStatusWait() {
        return STATUS_WAIT;
    }

    public static int getStatusFinish() {
        return STATUS_FINISH;
    }

    public static int getStatusDelete() {
        return STATUS_DELETE;
    }

    public void save(List<DesignProductDTO> designProductDTOS) {
        for (DesignProductDTO designProductDTO : designProductDTOS) {
            DesignProduct data = ConvertUtil.dtoConvert(designProductDTO, DesignProduct.class);
            Long id = IdGenerator.nextId();
            data.setId(id);
            designProductDTO.setId(id);
            data.setCreatedTime(System.currentTimeMillis());
            String keyId = ALGUtil.toSerialCode(id);
            data.setKeyId(keyId);
            designProductDTO.setKeyId(keyId);
            this.save(data);
        }
    }

    @Autowired
    @Lazy
    private ProductService productService;

    public void formatProduct(List<DesignProductDTO> designProductDTOS) {
        List<Long> productIds = designProductDTOS.stream().map(DesignProductDTO::getProductId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }
        List<Product> products = this.productService.findByIdsAll(productIds);
        Map<Long, Product> productMap = Maps.newHashMap();
        for (Product product : products) {
            productMap.put(product.getId(), product);
        }
        for (DesignProductDTO data : designProductDTOS) {
            data.setProduct(productMap.get(data.getProductId()));
        }
    }


    @Autowired
    private MaterialService materialService;
    @Autowired
    private PrototypeService prototypeService;

    public void formatPrototype(List<DesignProductDTO> designProductDTOS) {
        List<Long> prototypeIds = designProductDTOS.stream().map(DesignProductDTO::getPrototypeId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(prototypeIds)) {
            return;
        }
        List<Prototype> prototypes = this.prototypeService.findByIds(prototypeIds);
        Map<Long, Prototype> prototypeMap = Maps.newHashMap();
        for (Prototype prototype : prototypes) {
            prototypeMap.put(prototype.getId(), prototype);
        }
        for (DesignProductDTO data : designProductDTOS) {
            data.setPrototype(prototypeMap.get(data.getPrototypeId()));
        }
    }

    public void formatSortkey(List<DesignProductDTO> designProductDTOS) {
        List<Long> materialIds = Lists.newArrayList();
        MapListUtil<Long, Long> mapListUtil = MapListUtil.instance();
        List<Long> ids = Lists.newArrayList();
        for (DesignProductDTO data : designProductDTOS) {
            List<Long> list = JSONUtil.toList(JSONUtil.parseArray(data.getMaterialIds()), Long.class);
            list.removeAll(Collections.singleton(null));


            list = ListUtils.distinct(list);
            Collections.sort(list);
            mapListUtil.setList(data.getId(), list);
            materialIds.addAll(list);
            ids.add(data.getId());
        }
        materialIds = ListUtils.distinct(materialIds);
        if (CollectionUtils.isEmpty(materialIds)) {
            return;
        }

        List<Material> materials = this.materialService.findByIds(materialIds);
        Map<Long, Material> materialMap = materials.stream().collect(Collectors.toMap(Material::getId, a -> a, (k1, k2) -> k1));

        for (DesignProductDTO data : designProductDTOS) {
            StringBuilder materialImagesName = new StringBuilder("");
            for (Long materialId : mapListUtil.getList(data.getId())) {
                if (materialMap.get(materialId) != null) {
                    materialImagesName.append(" " + materialMap.get(materialId).getName());
                }
            }
            String sorkey = data.getMaterialIds() == null ? "" : data.getMaterialIds() + materialImagesName.toString();
            data.setSortKey(sorkey);
        }
    }


    @Autowired
    private DesignProductImageService designProductImageService;


    public void formatDesignProductImages(Long merchantId, List<DesignProductDTO> designProductDTOS) {
        List<String> compoundIds = Lists.newArrayList();
        for (DesignProductDTO designProductDTO : designProductDTOS) {
            compoundIds.add(designProductDTO.getCompoundId());
        }

        if (CollectionUtils.isNotEmpty(compoundIds)) {
            List<DesignProductImage> designProductImages = this.designProductImageService.findByCompoundIds(merchantId, compoundIds);
            Collections.sort(designProductImages);
            Map<String, List<DesignProductImageDTO>> listMap = Maps.newHashMap();
            for (DesignProductImage designProductImage : designProductImages) {
                if (listMap.get(designProductImage.getCompoundId()) == null) {
                    listMap.put(designProductImage.getCompoundId(), Lists.newArrayList());
                }
                listMap.get(designProductImage.getCompoundId()).add(ConvertUtil.dtoConvert(designProductImage, DesignProductImageDTO.class));
            }
            for (DesignProductDTO designProductDTO : designProductDTOS) {
                if (listMap.get(designProductDTO.getCompoundId()) != null) {
                    for (DesignProductImageDTO designProductImageDTO : listMap.get(designProductDTO.getCompoundId())) {
                        designProductDTO.addImageDtoUrl(designProductImageDTO);
                        designProductDTO.addImgUrl(designProductImageDTO.getImgUrl());
                    }

                }
            }
            for (DesignProductDTO designProductDto : designProductDTOS) {
                if (designProductDto.getStatus().equals(STATUS_FINISH)) {
                    designProductDto.setImageCompounStatus(TaskService.STATUS_SUCCESS);
                } else if (designProductDto.getDesignProductImageDTOS() == null || designProductDto.getDesignProductImageDTOS().size() < designProductDto.getImagesTotal()) {
                    designProductDto.setImageCompounStatus(TaskService.STATUS_WAITING);
                } else {
                    designProductDto.setImageCompounStatus(TaskService.STATUS_COMPLETE);
                    for (DesignProductImageDTO designProductImageDTO : designProductDto.getDesignProductImageDTOS()) {
                        if (com.ziguang.base.support.StringUtils.isBlank(designProductImageDTO.getImgUrl())) {
                            designProductDto.setImageCompounStatus(TaskService.STATUS_WAITING);
                            break;
                        }
                    }
                }

            }
        }

    }


    public List<DesignProduct> findByShceduledTaskId(Long merchantId, Long taskId) {
        if (taskId == null) {
            return Lists.newArrayList();
        }
        DesignProduct designProduct = new DesignProduct();
        designProduct.setMerchantId(merchantId);
        designProduct.setScheduledTaskId(taskId);
        return this.query(designProduct);

    }

    public DesignProduct createParent(Long id, Long childId, Long merchantId, int materialGroupSize) {
        DesignProduct designProduct = this.getById(childId, merchantId);
        designProduct.setId(id);
        designProduct.setType(TaskChildFinished.TYPE_PARENT);
        designProduct.setStatus(STATUS_FINISH);
        designProduct.setSize("");
        designProduct.setColor("");
        designProduct.setProductSize("");
        designProduct.setProductColor("");
        designProduct.setPid(0L);
        designProduct.setParentAttribute(0);
        designProduct.setMaterialGroupPid(0L);
        designProduct.setMaterialGroupSize(materialGroupSize);
        designProduct.setMaterialGroupType(getMaterialGroupTypeNormal());
        designProduct.setCreatedTime(System.currentTimeMillis());
        designProduct.setFinishTime(System.currentTimeMillis());

        String keyId = ALGUtil.toSerialCode(id);
        designProduct.setKeyId(keyId);
        return this.save(designProduct);

    }

    public DesignProduct initMaterialParent(Long id, Long pid, Long childId, Long merchantId, String materialColor) {
        DesignProduct designProduct = this.getById(childId, merchantId);
        designProduct.setId(id);
        designProduct.setType(0 < pid ? TaskChildFinished.TYPE_CHILD : TaskChildFinished.TYPE_PARENT);
        designProduct.setStatus(STATUS_FINISH);
        designProduct.setSize("");
        designProduct.setColor("");
        designProduct.setProductSize("");
        designProduct.setProductColor("");
        designProduct.setPid(pid);
        designProduct.setParentAttribute(0);
        designProduct.setMaterialGroupPid(0L);
        designProduct.setMaterialGroupType(getMaterialGroupTypeParent());
        designProduct.setCreatedTime(System.currentTimeMillis());
        designProduct.setFinishTime(designProduct.getFinishTime());
        designProduct.setMaterialColor(materialColor);

        String keyId = ALGUtil.toSerialCode(id);
        designProduct.setKeyId(keyId);
        return this.save(designProduct);

    }

    public DesignProduct getById(Long id, Long merchantId) {
        DesignProduct designProduct = new DesignProduct();
        designProduct.setId(id);
        designProduct.setMerchantId(merchantId);
        return this.queryOne(designProduct);

    }

    @Autowired
    @Lazy
    private MerchantProductParentService merchantProductParentService;
    @Autowired
    private OldPrototypeDao oldPrototypeDao;
    @Autowired
    private DesignProductLayerService designProductLayerService;

    @Autowired
    private UserProductParentService userProductParentService;

    public void finished(DesignProductDTO designProductDTO, Long pid, String color, int type, Long materialGroupId, int materialGroupType, String keyword, String materialImagesName) {

        //TODO:从task_child_service 改造过来的性能依旧不好
        Long merchantId = designProductDTO.getMerchantId();
        Product product = this.productService.findById(designProductDTO.getProductId());
        MerchantProductParent merchantProductParent = this.merchantProductParentService.findByMerchantIdProductId(designProductDTO.getMerchantId(), product.getParentId(), null);
        OldPrototype oldPrototype = this.oldPrototypeDao.findInfoById(designProductDTO.getPrototypeId());
        String exportName = oldPrototype.getSku();
        UserProductParent userProductParent = this.userProductParentService.findByProductUser(merchantProductParent.getProductId(), designProductDTO.getUserId());
        if (userProductParent != null && com.ziguang.base.support.StringUtils.isNotBlank(userProductParent.getExportName())) {
            merchantProductParent.setExportName(userProductParent.getExportName());
        }

        if (merchantProductParent != null && StringUtils.isNotBlank(merchantProductParent.getExportName())) {
            exportName = merchantProductParent.getExportName();
        } else {
            if (product != null) {
                Product parent = this.productService.findById(product.getParentId());
                if (parent != null) {
                    exportName = StringUtils.isBlank(parent.getEnglishName()) ? parent.getName() : parent.getEnglishName();
                } else {
                    exportName = StringUtils.isBlank(product.getEnglishName()) ? product.getName() : product.getEnglishName();
                }
            }
        }

        DesignProduct designProduct = new DesignProduct();
        designProduct.setPid(pid);
        designProduct.setMaterialGroupType(materialGroupType);
        designProduct.setMaterialGroupPid(materialGroupId);
        designProduct.setPid(pid);
        designProduct.setStatus(STATUS_FINISH);
        designProduct.setColor(color);
        designProduct.setType(type);
        designProduct.setParentAttribute(designProductDTO.getParentAttribute());
        designProduct.setMaterialColor(designProductDTO.getMaterialColor());
        designProduct.setFinishTime(System.currentTimeMillis());
//       this.imgUrls = imgUrls;  es从prototype中取
        designProduct.setKeyword(keyword);
        designProduct.setMaterialImgName(materialImagesName);
        designProduct.setExportName(exportName);

        this.updateById(designProduct, designProductDTO.getId(), merchantId);

    }

    @Autowired
    private MaterialKeywordDao materialKeywordDao;
    public static final int MATERIAL_KEYWORD_YES_STATUS = 1;

    public String formatGetKeyword(List<Long> materialIds) {
        String stringKeyword = "";
        if (CollectionUtils.isNotEmpty(materialIds)) {
            List<Long> distinctList = materialIds.stream().distinct().collect(Collectors.toList());
            List<Keyword> keywordList = this.materialKeywordDao.findUseByMaterialIds(distinctList, MATERIAL_KEYWORD_YES_STATUS);
            if (CollectionUtils.isNotEmpty(keywordList)) {
                List<String> keyWorks = keywordList.stream().map(Keyword::getMkeyword).distinct().collect(Collectors.toList());
                stringKeyword = StringUtils.listToString(keyWorks);
            }

        }
        return stringKeyword;
    }

    public boolean updateById(DesignProduct designProduct, Long id, Long merchantId) {
        designProduct.setId(id);
        designProduct.setMerchantId(merchantId);
        return this.dao.updateByPrimaryKeySelective(designProduct) > 0;
    }


    public void deleteById(Long merchantId, Long id) {
        DesignProduct designProduct = new DesignProduct();
        designProduct.setId(id);
        designProduct.setMerchantId(merchantId);
        this.dao.delete(designProduct);
    }

    @Autowired
    private S3ServiceV2 s3ServiceV2;

    @Autowired
    private ImgMqService imgMqService;

    public String copyToOther(long merchantId, String keyId, String code) {
        SiteCode siteCode = SiteCode.findByCode(code);
        DesignProductInsertDto designProductInsertDto = this.findDesinProductInsertDto(merchantId, keyId);
        Assert.validateNull(designProductInsertDto, "数据异常：" + keyId);
        boolean flagFinishImage = true;
        for (DesignProductImage designProductImage : designProductInsertDto.getDesignProductImages()) {
            if (StringUtils.isBlank(designProductImage.getImgUrl())) {
                //没有完成图片，需要去完成图片
                flagFinishImage = false;
                break;
            }
        }
        log.info("zmykeyId:{},同步layer到国内1{},flagFinishImage={}", keyId, JSON.toJSONString(designProductInsertDto.getDesignProductLayers()),flagFinishImage);

        if (!flagFinishImage) {
            this.finishLayerAndImages(designProductInsertDto);
        }
        log.info("zmykeyId:{},同步layer到国内2{}", keyId, JSON.toJSONString(designProductInsertDto.getDesignProductLayers()));
//        for (DesignProductLayer designProductLayer : designProductInsertDto.getDesignProductLayers()) {
//            String fileName = this.taskChildLayerService.getImageFilePath(designProductLayer.getSrcFilecode());
//            this.s3ServiceV2.copyToOtherS3(fileName, siteCode);
//        }
//        for (DesignProductImage designProductImage : designProductInsertDto.getDesignProductImages()) {
//            String fileCode = designProductImage.getImgUrl().substring(designProductImage.getImgUrl().lastIndexOf("/") + 1);
//            String fileName = this.s3ServiceV2.getOutputFilePath(fileCode);
//            this.s3ServiceV2.copyToOtherS3(fileName, siteCode);
//        }
        ObjectMapper mapper = new ObjectMapper();
        String json = null;
        try {
            designProductInsertDto.setKeyId(keyId);
            log.info("keyId:{},同步layer到国内3{}", keyId, JSON.toJSONString(designProductInsertDto.getDesignProductLayers()));
            json = mapper.writeValueAsString(designProductInsertDto);
            HttpClientUtil.doPostJson(siteCode.getDesignHost() + "/design_products/add", json, true);
        } catch (JsonProcessingException e) {
            Assert.wrong("数据有问题，请重试：" + keyId);
        }
        return json;

    }

    /**
     * 补充图层与images的图片信息
     *
     * @param designProductInsertDto
     * @return
     */
    public void finishLayerAndImages(DesignProductInsertDto designProductInsertDto) {
        this.finishLayer(designProductInsertDto);
        this.finishImage(designProductInsertDto);

    }

    /**
     * 补充图层与images的图片信息
     *
     * @param designProductInsertDto
     * @return
     */
    public void finishLayer(DesignProductInsertDto designProductInsertDto) {
        List<DesignProductLayer> designProductLayers = Lists.newArrayList();
        Map<String, String> designMd5ContentMap = Maps.newHashMap();
        for (DesignProductLayer designProductLayer : designProductInsertDto.getDesignProductLayers()) {
            if (StringUtil.isBlank(designProductLayer.getSrcFilecode())) {
                if (StringUtil.isNotBlank(designProductLayer.getDesignData())) {
                    String md5 = StringUtils.getMd5(designProductLayer.getDesignData());
                    String content = designMd5ContentMap.get(md5);
                    if (content == null) {
                        content = this.usCanvas(designProductLayer.getDesignData());
                        designMd5ContentMap.put(md5, content);
                    }
                    designProductLayer.setSrcFilecode(content);
                    designProductLayer.setContent(content);
                    designProductLayers.add(designProductLayer);

                    this.designProductLayerService.updateContentById(content, designProductLayer);
                } else {
                    designProductLayers.add(designProductLayer);
                }
            } else {
                designProductLayers.add(designProductLayer);
            }
        }
        designProductInsertDto.setDesignProductLayers(designProductLayers);

    }

    public static final Integer LAYER_TYPE_IMAGE = 1;

    /**
     * 补充图层与images的图片信息
     *
     * @param designProductInsertDto
     * @return
     */
    public void finishImage(DesignProductInsertDto designProductInsertDto) {
        boolean isCustomImage = designProductInsertDto.getDesignProducts().stream().anyMatch(i -> DesignProductTypeConstant.isCustomImage(i.getType()));
        if (isCustomImage) {
            return;
        }
        this.logger.error("finishimage start");
        List<ReplaceLayersContent> replaceLayersContentElements = Lists.newArrayList();
        List<DesignProductLayer> designProductLayers = designProductInsertDto.getDesignProductLayers();
        Map<Long, DesignProductLayer> prototypeLayerIdDesignProductLayerMap = designProductLayers.stream().collect(Collectors.toMap(dp -> dp.getPrototypeLayerId(), dp -> dp, (a, b) -> b));
        List<PrototypeLayer> prototypeLayers = designProductInsertDto.getPrototypeLayers();

        List<Integer> lensIds = CollUtil.isEmpty(prototypeLayers) ? Lists.newArrayList() : prototypeLayers.stream().filter(pl -> null != pl.getLensId() && pl.getLensId() > 0).map(pl -> pl.getLensId()).collect(Collectors.toList());
        Map<Integer, PrototypeLens> lensIdProtoTypeLensMap = this.prototypeLensService.findMapByLocalIds(lensIds);

        for (PrototypeLayer prototypeLayer : prototypeLayers) {
            ReplaceLayersContent replaceLayersContentElement = new ReplaceLayersContent();
            DesignProductLayer layerDto = prototypeLayerIdDesignProductLayerMap.get(prototypeLayer.getId());
            if (null == layerDto) {
                replaceLayersContentElement.setReplaceContent("");
                replaceLayersContentElement.setReplaceType(prototypeLayer.getType());
                replaceLayersContentElement.setLayerName(prototypeLayer.getName());
            } else {
                replaceLayersContentElement.setLayerName(layerDto.getPrototypeLayerName());
                replaceLayersContentElement.setResizeMode("1");
                replaceLayersContentElement.setReplaceType(layerDto.getType());
                if (prototypeLayer.getLensId() != null && prototypeLayer.getLensId() > 0) {
                    PrototypeLens prototypeLens = lensIdProtoTypeLensMap.get(prototypeLayer.getLensId());
                    if (prototypeLens != null) {
                        replaceLayersContentElement.setImageFilter(prototypeLens.getCode());
                    }
                }
                if (layerDto.getType().equals(LAYER_TYPE_IMAGE)) {
                    replaceLayersContentElement.setReplaceContent("91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/" + this.s3ServiceV2.getFileCode(layerDto.getContent()));
                    replaceLayersContentElement.setImageWidth(StrUtil.isNotBlank(layerDto.getImageWidth()) ? layerDto.getImageWidth() : "1200");
                    replaceLayersContentElement.setImageHeight(StrUtil.isNotBlank(layerDto.getImageHeight()) ? layerDto.getImageHeight() : "1200");
                } else {
                    replaceLayersContentElement.setReplaceContent(layerDto.getContent());
                    replaceLayersContentElement.setFont(layerDto.getFontName());
                }
            }
            replaceLayersContentElements.add(replaceLayersContentElement);
        }
        Map<String, String> md5ImageUrlMap = Maps.newHashMap();
        for (DesignProductImage designProductImage : designProductInsertDto.getDesignProductImages()) {
            String md5 = com.ziguang.base.support.StringUtils.getMd5(designProductImage.getCompoundId() + designProductImage.getPsdFileCode());
            if (StringUtils.isNotBlank(designProductImage.getImgUrl())) {
                //md5 相同直接用
                md5ImageUrlMap.put(md5, designProductImage.getImgUrl());
            }
        }
        for (DesignProductImage designProductImage : designProductInsertDto.getDesignProductImages()) {
            if (StringUtils.isNotBlank(designProductImage.getImgUrl())) {
                continue;
            }
            String md5 = com.ziguang.base.support.StringUtils.getMd5(designProductImage.getCompoundId() + designProductImage.getPsdFileCode());
            String imageUrl = md5ImageUrlMap.get(md5);
            if (imageUrl == null) {
                imageUrl = this.compounService.preview(designProductImage.getPsdFileCode(), replaceLayersContentElements, "jpg");
                md5ImageUrlMap.put(md5, imageUrl);
                this.designProductImageService.updateByMd5(imageUrl, designProductImage.getMerchantId(), md5);
            }
            designProductImage.setImgUrl(imageUrl);
        }

    }

    public String usCanvas(String designData) {
        JSONObject init = JSONObject.parseObject(designData);
        JSONObject data = new JSONObject();
        data.put("init", init);
        data.put("zoom", 2);
        String url = "https://cdn-canvas-out.sdspod.com/";
        String reponse = HttpClientUtil.doPostJson(url, data.toJSONString());
        JSONObject jsonObject = JSONObject.parseObject(reponse);
        try {
            String fileCode = jsonObject.getString("fileCode");
            if (fileCode != null) {
                return fileCode;
            } else {
                this.logger.error("image error" + jsonObject.toJSONString());
                throw new com.sdsdiy.common.base.exception.BusinessException("图片生成失败");
            }
        } catch (Exception e) {
            this.logger.error("image error", e);
            throw new com.sdsdiy.common.base.exception.BusinessException("图片生成失败");
        }


    }

    public void mapperAdd(Long merchantId, Long id) {
        String urlLink = this.designHost + "/design_products/findProductInsertDto?id=" + id + "&merchant_id=" + merchantId;

        ObjectMapper objectMapper = new ObjectMapper();
        DesignProductInsertDto designProductInsertDto = null;
        try {
            URL url = new URL(urlLink);
            designProductInsertDto = objectMapper.readValue(url, DesignProductInsertDto.class);
        } catch (Exception e) {
            log.error("findProductInsertDto 请求错误", e);

        }

        if (designProductInsertDto == null) {
            log.error("{} findProductInsertDto 请求错误", urlLink);
            Assert.wrong("findProductInsertDto 请求错误");
        }
        this.mapperAdd(designProductInsertDto);


    }

    public void mapperAdd(DesignProductInsertDto insertDto) {
        TransactionStatus transactionStatus = null;
        List<String> syncCodes = Lists.newArrayList();
        Long merchantId = null;
        try {
            transactionStatus = this.dataSourceTransactionManager.getTransaction(this.transactionDefinition);
            for (DesignProduct designProduct : insertDto.getDesignProducts()) {
                if (designProduct.getId() == null) {
                    throw new BusinessException("数据异常");
                }
                if (merchantId == null) {
                    merchantId = designProduct.getMerchantId();
                }
                designProduct.setCompoundJson("");
                this.deleteById(designProduct.getMerchantId(), designProduct.getId());
                this.save(designProduct);
            }

            for (DesignProductImage designProductImage : insertDto.getDesignProductImages()) {
                log.info("designProductImage={}", JSON.toJSONString(designProductImage));
                designProductImage.setImgUrl(designProductImage.getImgUrl());
                if (designProductImage.getId() == null) {
                    throw new BusinessException("数据异常");
                }
                this.designProductImageService.deleteById(designProductImage.getMerchantId(), designProductImage.getId());
                log.info("designProductImage SAVE={}", JSON.toJSONString(designProductImage));
                this.designProductImageService.save(designProductImage);
            }

            for (DesignProductLayer designProductLayer : insertDto.getDesignProductLayers()) {
                if (designProductLayer.getId() == null) {
                    throw new BusinessException("数据异常");
                }
                List<String> layerCodes = this.syncImages(designProductLayer);
                syncCodes.addAll(layerCodes);

                this.designProductLayerService.deleteById(designProductLayer.getMerchantId(), designProductLayer.getId());
                DesignProductLayer data = designProductLayerService.findByContentMd5(designProductLayer.getMerchantId(), designProductLayer.getContentMd5());
                if (data == null) {
                    this.designProductLayerService.save(designProductLayer);
                }
            }
            // 保存主题相关
            if (CollUtil.isNotEmpty(insertDto.getDesignProductThemePrototypeStyleList())) {
                this.designProductThemePrototypeStyleService.batchSave(ListUtil.copyProperties(insertDto.getDesignProductThemePrototypeStyleList(), DesignProductThemePrototypeStyleDTO.class));
            }
            if (CollUtil.isNotEmpty(insertDto.getDesignProductThemeRelationMaterialList())) {
                List<Long> materialIds = insertDto.getDesignProductThemeRelationMaterialList().stream().map(DesignProductThemeRelationMaterial::getId).collect(Collectors.toList());
                this.designProductThemeRelationMaterialService.deleteByIds(materialIds);
                this.designProductThemeRelationMaterialService.saveBatch(insertDto.getDesignProductThemeRelationMaterialList());
            }
            if (CollUtil.isNotEmpty(insertDto.getDesignProductThemeRelationMaterialCategoryList())) {
                List<Long> categoryIds = insertDto.getDesignProductThemeRelationMaterialCategoryList().stream().map(DesignProductThemeRelationMaterialCategory::getId).collect(Collectors.toList());
                this.designProductThemeRelationMaterialCategoryService.deleteByIds(categoryIds);
                this.designProductThemeRelationMaterialCategoryService.saveBatch(insertDto.getDesignProductThemeRelationMaterialCategoryList());
            }
            this.dataSourceTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            if (transactionStatus != null) {
                this.dataSourceTransactionManager.rollback(transactionStatus);
            }
            log.error("outside-mapperAdd:{}:{}", insertDto.getKeyId(), e.getMessage());
            throw new BusinessException(e);
        }

        if (CollectionUtils.isNotEmpty(syncCodes)) {
            this.imgMqService.imageToUsWest(syncCodes);
        }
    }


    public List<String> syncImages(DesignProductLayer layer) {

        List<String> images = Lists.newArrayList();
        images.add(this.taskChildLayerService.fillFilePath(layer.getContent()));
        if (StringUtils.isBlank(layer.getDesignData())) {
            return images;
        }
        JSONObject designData = JSONObject.parseObject(layer.getDesignData());

        JSONArray jsonArray = designData.getJSONArray("objects");
        double maxPrint = Math.max(layer.getPrintHeight(), layer.getPrintWidth());
        for (int index = 0; index < jsonArray.size(); index++) {
            JSONObject object = jsonArray.getJSONObject(index);
            String type = object.getString("type");
            if (type != null && type.equals("group")) {
                List<String> fileCodes = this.groupObject(object, object.getJSONArray("objects"));
                if (CollectionUtils.isNotEmpty(fileCodes)) {
                    for (String fileCode : fileCodes) {
                        images.add(this.taskChildLayerService.getImageFilePath(fileCode));
                    }
                }
            } else {
                String src = object.getString("src");
                //如果是背景图，先找到大的背景图
                if (StringUtils.isNotBlank(src)) {
                    String[] srcArray = src.split("\\?");
                    String srcUrl = srcArray[0];
                    String uri = "";
                    if (1 < srcArray.length) {
                        uri = srcArray[1];
                    }
//                    String fileCode = StringUtils.urlCode(srcUrl);
                    String filePath = this.taskChildLayerService.filePath(srcUrl);
                    src = this.s3ServiceV2.getDownloadUrlByFilePath(filePath);
                    if (S3ServiceV2.is1000Thumb(srcUrl)) {
                        src = S3ServiceV2.get1000ThumbImage(src);
                        String fileCode = StringUtils.urlCode(srcUrl);
                        images.add(this.s3ServiceV2.get1000ThumbFilePath(fileCode));
                    }
                    object.put("src", src + "?" + uri);
                    images.add(filePath);
                }
                jsonArray.set(index, object);
            }
        }
        designData.put("objects", jsonArray);
        layer.setDesignData(JSONObject.toJSONString(designData));
        return ListUtils.distinct(images);

    }


    public List<String> groupObject(JSONObject object, JSONArray jsonArray) {
        JSONArray jsonArr = new JSONArray();
        List<String> filePaths = Lists.newArrayList();
        for (int index = 0; index < jsonArray.size(); index++) {
            JSONObject objectIndex = jsonArray.getJSONObject(index);

            String src = objectIndex.getString("src");
            String filePath = null;
            if (StringUtils.isNotBlank(src)) {
                String[] srcArray = src.split("\\?");
                String srcUrl = srcArray[0];
                filePath = this.taskChildLayerService.filePath(srcUrl);
                src = this.s3ServiceV2.getDownloadUrlByFilePath(filePath);
                if (S3ServiceV2.is1000Thumb(srcUrl)) {
                    src = S3ServiceV2.get1000ThumbImage(src);
                }
                objectIndex.put("src", src);
                filePaths.add(src);
            }
            objectIndex.put("src", src);
            jsonArr.add(objectIndex);
        }
        object.put("objects", jsonArr);
        return filePaths;

    }


    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

}

