/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.DesignTaskDao;
import com.sds.outsite.entity.DesignBackground;
import com.sds.outsite.entity.DesignTask;
import com.ziguang.base.dto.CompounDTO;
import com.ziguang.base.dto.DesignBackgroundDTO;
import com.ziguang.base.dto.DesignProductDTO;
import com.ziguang.base.dto.TaskDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


@Service
public class DesignTaskService extends TKBaseService<DesignTaskDao, DesignTask> {
    private final String talbeName = "design_task";
    private static final int IS_DELETE_YES = 1;
    private static final int IS_DELETE_NO = 0;

    public SearchBean list(SearchBean<DesignTask> searchBean) {
        searchBean.setTable(talbeName);
        searchBean.setItems(dao.list(searchBean));
        searchBean.setTotalCount(dao.count(searchBean));
        return searchBean;
    }

    @Autowired
    private DesignBackgroundService designBackgroundService;

    public static final int STATUS_DEALING = 2;
    public static final int STATUS_REBUILD = 6;


    public DesignProductDTO save(Long merchantId, CompounDTO item, DesignBackgroundDTO designBackgroundDTO) {
        //外站的userId都为0
        Long userId = 0L;
        // 插入背景图相关信息
        Long designBackgroundId = 0L;
        if (designBackgroundDTO != null && designBackgroundDTO.getFile() != null) {
            DesignBackground designBackground = new DesignBackground();
            designBackground.setFile(designBackgroundDTO.getFile());
            designBackground.setJson(designBackgroundDTO.getJson());
            designBackground.setCreateUid(userId);
            designBackground.setMerchantId(merchantId);
            designBackground.setCreateTime(new Date());
            designBackgroundService.save(designBackground);
            designBackgroundId = designBackground.getId();

        }
        TaskDto taskDto = item.getPrototypes().get(0);
        taskDto.setProductId(item.getProductId());
        return designService.compound(merchantId, userId, taskDto, designBackgroundId, item.getDesignType(), item.getOldEndProductId(),true);
    }

    public DesignProductDTO saveTheme(Long merchantId, CompounDTO item, DesignBackgroundDTO designBackgroundDTO) {
        //外站的userId都为0
        Long userId = 0L;
        // 插入背景图相关信息
        Long designBackgroundId = 0L;
        if (designBackgroundDTO != null && designBackgroundDTO.getFile() != null) {
            DesignBackground designBackground = new DesignBackground();
            designBackground.setFile(designBackgroundDTO.getFile());
            designBackground.setJson(designBackgroundDTO.getJson());
            designBackground.setCreateUid(userId);
            designBackground.setMerchantId(merchantId);
            designBackground.setCreateTime(new Date());
            designBackgroundService.save(designBackground);
            designBackgroundId = designBackground.getId();

        }
        TaskDto taskDto = item.getPrototypes().get(0);
        taskDto.setProductId(item.getProductId());
        return designService.compound(merchantId, userId, taskDto, designBackgroundId, item.getDesignType(), item.getOldEndProductId(),false);
    }
    public DesignProductDTO saveWordAndCustomImage(Long merchantId, CompounDTO item, DesignBackgroundDTO designBackgroundDTO) {
        //外站的userId都为0
        Long userId = 0L;
        // 插入背景图相关信息
        Long designBackgroundId = 0L;
        if (designBackgroundDTO != null && designBackgroundDTO.getFile() != null) {
            DesignBackground designBackground = new DesignBackground();
            designBackground.setFile(designBackgroundDTO.getFile());
            designBackground.setJson(designBackgroundDTO.getJson());
            designBackground.setCreateUid(userId);
            designBackground.setMerchantId(merchantId);
            designBackground.setCreateTime(new Date());
            designBackgroundService.save(designBackground);
            designBackgroundId = designBackground.getId();

        }
        TaskDto taskDto = item.getPrototypes().get(0);
        taskDto.setProductId(item.getProductId());
        return designService.compound(merchantId, userId, taskDto, designBackgroundId, item.getDesignType(), item.getOldEndProductId(),false);
    }


    @Autowired
    private DesignService designService;





}

