package com.sds.outsite.service;


import com.sds.outsite.dto.OutSitePrototypePsdDTO;
import com.sds.outsite.dto.ShopifyProductCompoundDto;
import com.sdsdiy.core.file.storage.util.FileUtils;
import com.sdsdiy.designproductdata.dto.DesignStyleReqDTO;
import com.sdsdiy.designproductdata.dto.theme.DesignThemePrototypeDTO;
import com.ziguang.base.dto.MerchantProductCompoundDto;
import com.ziguang.base.model.PrototypeLayer;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OutDesignLayerDataFreshService {
        public static void refreshOutSite(ShopifyProductCompoundDto shopifyProductCompoundDto){
                for (PrototypeLayer prototypeLayer : shopifyProductCompoundDto.getPrototypeLayers()) {
                        String designData = FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getDesignData());
                        if(prototypeLayer.getMaterial() != null){
                                prototypeLayer.getMaterial().setUrl(FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getMaterial().getUrl()));
                                prototypeLayer.getMaterial().setImgUrl(FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getMaterial().getImgUrl()));
                                prototypeLayer.getMaterial().setPhotoServiceFileUrl(FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getMaterial().getPhotoServiceFileUrl()));
                        }
                        prototypeLayer.setMaskShowUrl(FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getMaskShowUrl()));
                        prototypeLayer.setMaskUrl(FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getMaskUrl()));
                        prototypeLayer.setThumbnailUrl(FileUtils.imgUrlToOutSiteCdn(prototypeLayer.getThumbnailUrl()));
                        prototypeLayer.setDesignData(designData);
                        if(prototypeLayer.getThemePrototypes() != null) {
                                for (DesignThemePrototypeDTO themePrototype : prototypeLayer.getThemePrototypes()) {
                                        refreshThemePrototypes(themePrototype);
                                }
                        }
                }
                for (OutSitePrototypePsdDTO prototypePsd : shopifyProductCompoundDto.getPrototypePsds()) {
                        prototypePsd.setThumbnailUrl(FileUtils.imgUrlToOutSiteCdn(prototypePsd.getThumbnailUrl()));
                }
        }

        public static void refreshThemePrototypes(DesignThemePrototypeDTO designThemePrototypeDTO){
                if(designThemePrototypeDTO == null){
                        return;
                }
                if(designThemePrototypeDTO.getThemePrototypeStyles() == null){
                        return;
                }
                for (DesignStyleReqDTO themePrototypeStyle : designThemePrototypeDTO.getThemePrototypeStyles()) {
                        themePrototypeStyle.setFrontPic(FileUtils.imgUrlToOutSiteCdn(themePrototypeStyle.getFrontPic()));
                }

        }


}
