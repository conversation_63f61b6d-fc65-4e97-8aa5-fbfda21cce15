package com.sds.outsite.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.sds.outsite.bo.PublishedProductBo;
import com.sds.outsite.config.SqsConfig;
import com.sds.outsite.mapper.PublishedProductMapper;
import com.sds.outsite.mapper.PublishedVariantMapper;
import com.sds.outsite.service.OutSiteSqsService;
import com.sdsdiy.ecommerceapi.exception.ShopifyApiException;
import com.sdsdiy.ecommerceapi.exception.ShoplazzaApiException;
import com.sdsdiy.ecommercedata.bo.OutsiteSyncProductMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SyncProductMsgConsumeService {

    @Autowired
    private PublishedProductMapper publishedProductMapper;

    @Autowired
    private PublishedVariantMapper publishedVariantMapper;

    @Autowired
    @Lazy
    private SyncProductMsgConsumeService syncProductMsgConsumeService;

    @Autowired
    private OutSiteSqsService outSiteSqsService;

    @Value("${api_host}")
    private String apiHost;

    private static final String DELETE_PUBLISHED_PATH = "/platformProducts/updatePlatformDelete?platform={platform}&platformShop={shop}&platformProductId={id}";

    public void consume(OutsiteSyncProductMsg msg) {
        ISyncPublishedProduct consumeService = SyncPublishedProductFactory.getConsumeService(msg.getPlatform());
        if (ObjectUtil.isNull(consumeService)) {
            log.error("sync published product msg error consume impl not exist");
            return;
        }

        boolean productNotFound = false;
        boolean rateLimit = false;
        PublishedProductBo productBo = null;
        try {
            productBo = consumeService.analyseProductInfoFromPlatform(msg);
            if (productBo == null) {
                productNotFound = true;
            }
            log.info("sync published product analyse result productId={}, result={}", msg.getProductId(), JSON.toJSONString(productBo));
        } catch (ShopifyApiException e) {
            if (e.isAuthExpired()) {
                log.error("sync published product msg error token invalid");
                return;
            }
            productNotFound = e.isNotFound();
            rateLimit = e.isRateLimit();
        } catch (ShoplazzaApiException e) {
            if (e.isAuthExpired()) {
                log.error("sync published product msg error token invalid");
                return;
            }
            productNotFound = e.isHttpNotFound();
            rateLimit = e.isRateLimit();
        }
        if (productNotFound) {
            log.error("sync published product msg error not found");
            removeOldProductRecord(msg.getPlatform(), msg.getProductId());
            syncProductMsgConsumeService.notifyMerchantProductRemoved(msg.getPlatform(), msg.getShopName(), msg.getProductId());
            return;
        }
        if (rateLimit || productBo.isNeedRepeatGetProductInfo()) {
            resendMsgToSqs(msg);
            return;
        }

        saveNewAndRemoveOld(msg.getPlatform(), productBo);
        log.info("sync published product success product={}", msg.getProductId());
    }

    private void resendMsgToSqs(OutsiteSyncProductMsg msg) {
        msg.setUuid(UUID.randomUUID().toString());
        msg.setCreateTime(LocalDateTime.now().toString());
        outSiteSqsService.send(SqsConfig.OUTSITE_SYNC_PRODUCT_URL, msg);
    }

    private void saveNewAndRemoveOld(String platform, PublishedProductBo bo) {
        String productId = bo.getProduct().getProductId();
        removeOldProductRecord(platform, productId);
        publishedProductMapper.save(bo.getProduct());
        if (CollUtil.isNotEmpty(bo.getVariantList())) {
            publishedVariantMapper.saveBatch(bo.getVariantList());
        }
    }

    private void removeOldProductRecord(String platform, String productId) {
        publishedProductMapper.deleteByProductId(platform, productId);
        publishedVariantMapper.deleteByProductId(platform, productId);
    }

    @Async
    public void notifyMerchantProductRemoved(String platform, String shopName, String productId) {
        String url = UriComponentsBuilder.fromUriString(apiHost + DELETE_PUBLISHED_PATH)
            .buildAndExpand(platform, shopName, productId)
            .toString();
        HttpResponse response = HttpRequest.get(url).execute();
        if (response.isOk()) {
            return;
        }
        log.error("published product notify merchant error status={}, body={}", response.getStatus(), response.body());
    }
}
