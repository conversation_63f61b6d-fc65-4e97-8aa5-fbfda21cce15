/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ps.base.entity.SearchBean;
import com.ps.base.service.TKBaseService;
import com.ps.exception.BusinessException;
import com.ps.ps.service.*;
import com.ps.ps.service.linstener.BaseEventService;
import com.ps.ps.service.userconfig.CombineConfigService;
import com.ps.support.IdGenerator;
import com.ps.support.MapListUtil;
import com.ps.support.contant.UserConfigCode;
import com.ps.support.utils.ConvertUtil;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.designproduct.DesignProductTagConst;
import com.sdsdiy.designproductapi.contant.DesignProductEventConstant;
import com.sdsdiy.designproductapi.dto.DesignProductFinishMessageDto;
import com.ziguang.base.dto.CombineConfigDto;
import com.ziguang.base.dto.DesignProductDTO;
import com.ziguang.base.model.CompounGroup;
import com.ziguang.base.model.Material;
import com.ziguang.base.model.TaskChildFinished;
import com.ps.support.DesignProductCombineHelper;
import com.ziguang.base.support.JsonUtil;
import com.ziguang.base.support.StringUtils;
import com.ziguang.base.support.contant.MsgModule;
import com.sds.outsite.dao.DesignScheduledTaskDao;
import com.sds.outsite.entity.DesignProduct;
import com.sds.outsite.entity.DesignProductLayer;
import com.sds.outsite.entity.DesignScheduledTask;
import com.sds.outsite.entity.DesignTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Service
public class DesignScheduledTaskService extends TKBaseService<DesignScheduledTaskDao, DesignScheduledTask> {
    private final String talbeName = "design_scheduled_task";
    private static final int DESIGN_SCOPE_HALF = 0;
    private static final int STATUS_SUCCESS = 2;
    private static final int STATUS_NONE = 0;
    private static final int STATUS_FINISH = 2;
    private static final int STATUS_WAIT = 4;
    //86400 * 2* 1000
    private static final long CRON_TIME = 172800000L;
    //1普通
    private static final int TYPE_NORMAL = 1;
    //2同步的
    private static final int TYPE_SYNC = 2;

    public static int getDesignScopeHalf() {
        return DESIGN_SCOPE_HALF;
    }

    public SearchBean list(SearchBean<DesignScheduledTask> searchBean) {
        searchBean.setTable(talbeName);
        searchBean.setItems(dao.list(searchBean));
        searchBean.setTotalCount(dao.count(searchBean));
        return searchBean;
    }


    @Transactional(rollbackFor = Exception.class)
    public List<DesignProductDTO> genByCompoundHistory(List<DesignProductDTO> designProducts) {
        designProductService.save(designProducts);
//        List<DesignScheduledTask> designScheduledTasks = this.findByTaskId(designTask.getId());
//        for (DesignScheduledTask schideledTask : designScheduledTasks) {
//            this.finish(schideledTask);
//        }

        return designProducts;


    }

    @Autowired
    @Lazy
    private DesignTaskService designTaskService;

    @Autowired
    @Lazy
    private DesignProductService designProductService;

    public DesignScheduledTask save(Integer combineRuleType, DesignTask task, int type) {
        DesignScheduledTask designScheduledTask = new DesignScheduledTask();
        designScheduledTask.setCreatedTime(System.currentTimeMillis());
        designScheduledTask.setStatus(STATUS_WAIT);
        designScheduledTask.setUserId(task.getUserId());
        designScheduledTask.setTaskId(task.getId());
        designScheduledTask.setMerchantId(task.getMerchantId());
        designScheduledTask.setCombineRule("");
        designScheduledTask.setCombineRuleType(combineRuleType);
        designScheduledTask.setType(type);
        designScheduledTask.setId(IdGenerator.nextId());
        this.save(designScheduledTask);
        return designScheduledTask;
    }


    @Autowired
    private UserConfigService userConfigService;

    public void autoFinish() {
        logger.info("AutoFinishTask begin ");
        List<DesignScheduledTask> tasks = this.waitFinishih();
        for (DesignScheduledTask task : tasks) {
            try {
                this.finish(task);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        logger.info("AutoFinishTask end");
    }

    public List<DesignScheduledTask> waitFinishih() {
        Example example = new Example(DesignScheduledTask.class);
        example.createCriteria().andEqualTo("status", STATUS_WAIT).andEqualTo("type", TYPE_NORMAL).andGreaterThan("createdTime", System.currentTimeMillis() - CRON_TIME);
        return dao.selectByExample(example);
    }

    @Autowired
    private MaterialService materialService;

    @Autowired
    private DesignProductLayerService designProductLayerService;

    public void finish(DesignScheduledTask designScheduledTask) {
        if (designScheduledTask == null) {
            return;
        }
        List<DesignProduct> designProducts = designProductService.findByShceduledTaskId(designScheduledTask.getMerchantId(), designScheduledTask.getId());
        List<DesignProductDTO> designProductDTOS = ConvertUtil.dtoConvert(designProducts, DesignProductDTO.class);
        CombineConfigDto configDto = (CombineConfigDto) userConfigService.findConfigByCode(designScheduledTask.getUserId(), UserConfigCode.COMBINE_CONFIG);
        designProductService.formatProduct(designProductDTOS);
        designProductService.formatSortkey(designProductDTOS);
        designProductService.formatPrototype(designProductDTOS);
        designProductService.formatDesignProductImages(designScheduledTask.getMerchantId(), designProductDTOS);
        Collections.sort(designProductDTOS);
        Map<String, String> colorMap = Maps.newHashMap();
        Integer colorSize = 0;

        for (DesignProductDTO data : designProductDTOS) {
            //有产品未完成，直接退出
            if (data.getImageCompounStatus() != TaskService.STATUS_COMPLETE && data.getImageCompounStatus() != TaskService.STATUS_SUCCESS) {
                return;
            }
            data.setParentAttribute(EndProductService.PARENT_ATTRIBUTE_NO);

        }
        Long pid = 0L;
        Long childId = 0L;
        TransactionStatus transactionStatus = null;
        int type = TaskChildFinished.TYPE_SIMPLE;
        List<Long> notifyIds = Lists.newArrayList();
        Map<String, DesignProductDTO> materialKeyMaps = Maps.newHashMap();
        MapListUtil<Long, String> colorMaps = MapListUtil.instance();
        //已完成的成品清除redis缓存

        for (DesignProductDTO designProductDTO : designProductDTOS) {
            DesignProductDTO designProductMaterialParent = materialKeyMaps.get(designProductDTO.getMaterialKey());
            designProductDTO.setColor(designProductDTO.getProduct().getColorName());
            if (designProductMaterialParent == null) {
                designProductMaterialParent = new DesignProductDTO();
                designProductMaterialParent.setId(IdGenerator.nextId());
                StringBuilder materialImagesName = new StringBuilder();
                List<DesignProductLayer> layers = designProductLayerService.findByCompoundId(designProductDTO.getMerchantId(), designProductDTO.getCompoundId());

                List<Long> materialIds = Lists.newArrayList();
                for (DesignProductLayer layer : layers) {
                    if (layer.getMaterialId() != null && layer.getMaterialId() > 0) {
                        materialIds.add(layer.getMaterialId());
                    }
                }
                if (com.ps.support.utils.StringUtils.isNotBlank(designProductDTO.getMaterialIds())) {
                    List<Double> mids = JsonUtil.toList(designProductDTO.getMaterialIds(), Double.class);
                    mids.removeAll(Collections.singleton(null));
                    for (Double mid : mids) {
                        materialIds.add(mid.longValue());
                    }
                }
                if (materialIds.size() > 0) {
                    List<Material> materials = materialService.findByIds(materialIds);
                    for (Material material : materials) {
                        materialImagesName.append(" " + material.getName());
                    }
                }


                String materialName = materialImagesName.toString().trim();
                designProductMaterialParent.setKeyword(designProductService.formatGetKeyword(materialIds));
                designProductMaterialParent.setMaterialImgName(materialName);
                materialKeyMaps.put(designProductDTO.getMaterialKey(), designProductMaterialParent);
                if (configDto.getColorPrefixType() == CombineConfigService.getColrPrefixTypeMaterial() && StringUtils.isNotBlank(materialName)) {
                    designProductMaterialParent.setMaterialColor(com.ps.support.utils.StringUtils.sub(materialName, 45));
                } else {
                    designProductMaterialParent.setMaterialColor(com.ps.support.utils.StringUtils.sub(configDto.getColorPrefix(), 45));
                }

            }
            designProductMaterialParent.addVariant(designProductDTO);
            colorMaps.addDistinctValue(designProductMaterialParent.getId(), designProductDTO.getColor());
        }

        List<DesignProductCombineHelper> helpers = Lists.newArrayList();
        DesignProductCombineHelper designProductCombineHelper = new DesignProductCombineHelper();
        int size = 0;

        for (DesignProductDTO designProductDTO : materialKeyMaps.values()) {
            if (configDto.getMaterialMax() <= size) {
                helpers.add(designProductCombineHelper);
                designProductCombineHelper = new DesignProductCombineHelper();
                size = 0;
            }
            designProductCombineHelper.addDesignProduct(designProductDTO);
            size++;
        }
        helpers.add(designProductCombineHelper);


        try {
            transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
            for (DesignProductCombineHelper helper : helpers) {
                if (helper.getTotalSize() > 1) {
                    helper.getFirstChildProduct().setParentAttribute(EndProductService.PARENT_ATTRIBUTE_YES);
                    pid = IdGenerator.nextId();
                    childId = helper.getFirstChildProduct().getId();
                    type = TaskChildFinished.TYPE_CHILD;
                    notifyIds.add(pid);
                } else {
                    type = TaskChildFinished.TYPE_SIMPLE;
                    pid = 0L;
                }
                Integer materialGroupSize = 0;
                Set<String> colors = Sets.newHashSet();
                for (DesignProductDTO designProductDTO : helper.getDesignProductDTOList()) {
                    materialGroupSize++;
                    String colorTmp = designProductDTO.getMaterialColor() ;
                    int colorIndex = 0;
                    while (colors.contains(colorTmp)){
                        colorIndex ++;
                        colorTmp = designProductDTO.getMaterialColor() + "-" + colorIndex;
                    }
                    designProductDTO.setMaterialColor(colorTmp);
                    Boolean multyVariant = 1 < designProductDTO.getVariant().size();
                    Long variantChildId = null;
                    for (DesignProductDTO productDTO : designProductDTO.getVariant()) {
                        productDTO.setMaterialColor(designProductDTO.getMaterialColor());
                        variantChildId = productDTO.getId();
                        if (!multyVariant) {
                            designProductService.finished(productDTO, pid, productDTO.getColor(), type, 0L, DesignProductService.getMaterialGroupTypeSingle(), designProductDTO.getKeyword(), designProductDTO.getMaterialImgName());
                            if (pid == 0L) {
                                notifyIds.add(productDTO.getId());
                            }
                        } else {
                            designProductService.finished(productDTO, pid, productDTO.getColor(), type, designProductDTO.getId(), DesignProductService.getMaterialGroupTypeNormal(), designProductDTO.getKeyword(), designProductDTO.getMaterialImgName());
                        }

                    }
                    if (multyVariant) {
                        designProductService.initMaterialParent(designProductDTO.getId(), pid, variantChildId, designScheduledTask.getMerchantId(), designProductDTO.getMaterialColor());
                        if (pid == 0L) {
                            notifyIds.add(designProductDTO.getId());
                        }
                    }

                }
                if (pid > 0) {
                    designProductService.createParent(pid, childId, designScheduledTask.getMerchantId(), materialGroupSize);
                }
            }

            if (!this.updateStatus(designScheduledTask.getId(), STATUS_SUCCESS)) {
                throw new BusinessException("已合成过了");
            }

            dataSourceTransactionManager.commit(transactionStatus);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            if (transactionStatus != null) {
                dataSourceTransactionManager.rollback(transactionStatus);
            }
            throw new BusinessException(e);
        }
        webPushService.pushMsgToWebByUserId(MsgModule.PRODUCT_FINISH, designScheduledTask.getUserId());
        // 批量发送完成消息
        this.createBatchNotify(designProductDTOS);
    }

    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    public void createBatchNotify(List<DesignProductDTO> designProductDTOS) {
        try {
            // 发送设计完成消息
            Map<String, List<DesignProductDTO>> messages = Maps.newHashMap();
            designProductDTOS.forEach(item -> {

                String key = item.getMerchantId() + "_" + item.getUserId();
                if (messages.get(key) == null) {
                    messages.put(key, Lists.newArrayList());
                }
                messages.get(key).add(item);
            });

            for (Map.Entry<String, List<DesignProductDTO>> entry : messages.entrySet()) {
                DesignProductFinishMessageDto message = new DesignProductFinishMessageDto();
                List<String> ids = Lists.newArrayList();
                entry.getValue().forEach(item -> {
                    message.setMerchantId(item.getMerchantId());
                    message.setUserId(item.getUserId());
                    ids.add(String.valueOf(item.getId()));
                });
                message.setEndProductIds(String.join(",", ids));
                message.setCreatedTime(System.currentTimeMillis());
                //baseEventService.sendMessage(message, DesignProductEventConstant.EVEN_FINISH_DESIGN_TOPIC);
                rocketMQTemplate.sendNormal(RocketMqTopicConst.DESIGN_PRODUCT, DesignProductTagConst.EVEN_FINISH_DESIGN_TOPIC, message);
            }


        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }


    }


    @Autowired
    private ApplicationEventPublisher publisher;

    public boolean updateStatus(Long id, Integer status) {
        DesignScheduledTask designScheduledTask = new DesignScheduledTask();
        designScheduledTask.setStatus(status);

        Example example = new Example(CompounGroup.class);
        example.createCriteria().andEqualTo("id", id).andNotEqualTo("status", status);
        return dao.updateByExampleSelective(designScheduledTask, example) > 0;
    }

    public List<DesignScheduledTask> findByTaskId(Long taskId) {
        DesignScheduledTask designScheduledTask = new DesignScheduledTask();
        designScheduledTask.setTaskId(taskId);
        return dao.select(designScheduledTask);
    }

    @Autowired
    private EndProductService endProductService;
    @Autowired
    private WebPushService webPushService;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;

    public boolean useProductColorName(DesignProductDTO designProductDTO) {
        if (designProductDTO.getProduct() == null) {
            return false;
        }
        if (StringUtils.isBlank(designProductDTO.getProduct().getColorName())) {
            return false;
        }
        if (designProductDTO.getPrototype() == null) {
            return false;
        }
        return designProductDTO.getPrototype().getDesignScope() == DESIGN_SCOPE_HALF;
    }


}

