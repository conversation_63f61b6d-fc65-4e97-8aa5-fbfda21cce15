/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.sds.outsite.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ps.amazon.s3.S3ServiceV2;
import com.ps.dto.SqlMsg;
import com.ps.ps.service.ConfigService;
import com.ps.ps.service.MapInsertService;
import com.ps.support.utils.HttpClientUtil;
import com.ps.tool.cbt.common.util.StringUtil;
import com.sdsdiy.common.base.helper.ExceptionPrintUtil;
import com.sdsdiy.common.base.helper.IdGenerator;
import com.sdsdiy.core.mq.SendTemplate;
import com.sdsdiy.core.mq.biz.ImgMqService;
import com.sdsdiy.core.mq.core.FifoMsg;
import com.sdsdiy.core.mq.core.RocketMQTemplate;

import com.sdsdiy.core.mq.queue.OutsiteTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.materialdata.enums.OfficialMaterialImgFolder;
import com.sdsdiy.materialdata.util.MaterialUtils;
import com.sdsdiy.productdata.constants.ProductConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;


@Service
@RequiredArgsConstructor
@Slf4j
public class DataSyncService {
    private final ConfigService configService;
    private final MapInsertService mapInsertService;
    private final S3ServiceV2 s3ServiceV2;
    private final ImgMqService imgMqService;
    private final static String GET_URL = "%s/mapInsert?secret=%s&table=%s&id=%d&database=%s";
    private final static String DELETE_TASK_URL = "%s/mapInsert/task?secret=%s&table=%s&id=%d";
    private final RocketMQTemplate rocketMQTemplate;

    public void listen(SqlMsg msg) {
        List<String> syncFileCodes = Lists.newArrayList();
        //模板要做特殊的处理
        if (msg.getType().equalsIgnoreCase(MapInsertService.getTypeDelete())) {
            this.mapInsertService.deleteById(msg.getTable(), msg.getId());
        } else {
            String table = StringUtil.isNotBlank(msg.getDatabase()) ? msg.getDatabase() + "." + msg.getTable() : msg.getTable();
            String url = String.format(GET_URL, msg.getApiHost(), MapInsertService.getGetSecret(), table, msg.getId(), msg.getDatabase());
            log.info("outsite_sql_accept listen " + url);
            String res = "";

            res = HttpClientUtil.doGet(url);
            if (!JSONUtil.isJson(res)) {
                log.error("sqs获取表数据请求返回非json:" + url + " " + res);
                return;
            }
            JSONObject jsonObject = JSON.parseObject(res);
            Map<String, Object> map = jsonObject.getInnerMap();
            if (org.springframework.util.CollectionUtils.isEmpty(map)) {
                log.error("sqs获取表数据请求返回为空:" + url);
                return;
            }
            //图片传到国外
            if ("material".equalsIgnoreCase(msg.getTable())) {
                String imgUrl = map.get("img_url").toString();
                String fileCode = map.get("file_code").toString();
                if (!OfficialMaterialImgFolder.isOfficialMaterial(imgUrl)) {
                    map.put("photo_service_file_url", this.s3ServiceV2.getDownloadUrl(fileCode));
                    map.put("img_url", this.s3ServiceV2.getDownloadThumbUrl(fileCode));
                    // 非官方素材才要同步
                    syncFileCodes.add(this.s3ServiceV2.getImagesFilePath(fileCode));
                } else {
                    // 官方素材的fileCode是1000的原图
                    map.put("photo_service_file_url", MaterialUtils.replaceUrlToCdnUs(OfficialMaterialImgFolder.BASE_THUMB_1000_IMG.getUrl(fileCode)));
                    map.put("img_url", MaterialUtils.replaceUrlToCdnUs(OfficialMaterialImgFolder.BASE_THUMB_IMG.getUrl(
                            OfficialMaterialImgFolder.BASE_THUMB_IMG.getFileCodeByBase(
                                    OfficialMaterialImgFolder.WATERMARK_THUMB_IMG.getFileCode(imgUrl)))));
                }
            }
            if ("custom_font".equalsIgnoreCase(msg.getTable())) {
                String previewImage = map.get("preview_image").toString();
                String hkPreviewImage = previewImage.replaceAll("cdn.sdspod.com", "cdn-bucket-hk.sdspod.com");
                map.put("preview_image", hkPreviewImage);
            }

            this.mapInsertService.insertInfo(msg.getTable(), map);

            if ("product".equalsIgnoreCase(msg.getTable())) {
                try {
                    HttpUtil.createRequest(Method.DELETE, String.format(DELETE_TASK_URL, msg.getApiHost()
                                    , MapInsertService.getGetSecret(), msg.getTable(), msg.getId()))
                            .execute();
                } catch (Exception e) {
                    ExceptionPrintUtil.printErrOneLine(e);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(syncFileCodes)) {
            this.imgMqService.imageToUsWest(syncFileCodes);
        }
    }

    public void sendToSqs(String table, String type, Collection<Long> ids, String database) {
        for (Long id : ids) {
            com.sdsdiy.common.base.entity.dto.SqlMsg sqlMsg = new com.sdsdiy.common.base.entity.dto.SqlMsg();
            sqlMsg.setId(id);
            sqlMsg.setTable(table);
            sqlMsg.setApiHost(this.configService.getAdminApiHost());
            sqlMsg.setDatabase(database);
            sqlMsg.setType(type);
            String md5 = IdGenerator.nextStringId();
            sqlMsg.setMd5(md5);

            FifoMsg fifoMsg = new FifoMsg(sqlMsg, sqlMsg.getTable());
            rocketMQTemplate.sendDelay(RocketMqTopicConst.EVENT_OUT_DELAY, OutsiteTagConst.FIFO_OUTSITE_SQL_ACCEPT, fifoMsg, Duration.ofSeconds(1));
        }
    }

    public void sendProductSyncMsg(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        this.sendToSqs(ProductConstant.TABLE_PRODUCT, MapInsertService.TYPE_UPDATE,
                ids, MapInsertService.PRODUCT_DATABASE);
    }
}

