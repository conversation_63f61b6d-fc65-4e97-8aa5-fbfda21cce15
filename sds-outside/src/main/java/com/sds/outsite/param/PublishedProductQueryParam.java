package com.sds.outsite.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/1/12
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PublishedProductQueryParam {

    @ApiModelProperty(value = "变体ID")
    private String variantId;

    @ApiModelProperty(value = "指定的产品ID")
    private String designatedProductId;

    @ApiModelProperty(value = "第三方平台店铺名字")
    private String shopName;

    @ApiModelProperty(value = "第三方平台code")
    private String platform;

    @ApiModelProperty(value = "第三方平台产品ID")
    private String productName;

    @ApiModelProperty(value = "当前页数")
    private Integer currentPage;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;
}
