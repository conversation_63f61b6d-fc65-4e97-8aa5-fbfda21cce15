package com.sds.outsite.listener.sqlaccept;

import com.alibaba.fastjson.JSON;

import com.sds.outsite.service.product.SyncProductMsgConsumeService;
import com.sdsdiy.common.dtoconvert.annotation.LogTraceId;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;

import com.sdsdiy.core.mq.queue.EcommerceTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.sdsdiy.ecommercedata.bo.OutsiteSyncProductMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMqTopicConst.EVENT_ECO_DELAY_ORDER, consumerGroup = "GID_SyncPublishedProductListener",tag = EcommerceTagConst.DELAY_OUTSITE_PUBLISHED_SYNC_PRODUCT)
public class SyncPublishedProductListener implements RocketMQListener {

    private final SyncProductMsgConsumeService syncProductMsgConsumeService;

//    @LogTraceId
//    //@RabbitListener(queues = EcommerceTagConst.DELAY_OUTSITE_PUBLISHED_SYNC_PRODUCT)
//    public void receiveFromQueue(Message rabbitMsg, Channel channel) throws IOException {
//        String body = new String(rabbitMsg.getBody());
//        this.handle(body);
//        channel.basicAck(rabbitMsg.getMessageProperties().getDeliveryTag(), false);
//    }

    public void handle(String body) {
        log.info("receive published product sync msg={}", body);
        OutsiteSyncProductMsg msg = JSON.parseObject(body, OutsiteSyncProductMsg.class);
        syncProductMsgConsumeService.consume(msg);
    }

    @Override
    public void consumeMsg(MessageView messageView) {
        String body = RocketMQUtil.getBodyString(messageView);
        handle(body);
        
    }
}