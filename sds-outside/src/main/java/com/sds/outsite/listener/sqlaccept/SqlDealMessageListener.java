package com.sds.outsite.listener.sqlaccept;

import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.ps.amazon.s3.SqsService;
import com.sdsdiy.core.mq.SendTemplate;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * Etsy队列监听
 *
 * <AUTHOR>
 */
//@ConditionalOnProperty(name = "listener_register_condition", matchIfMissing = false)
//@Component
//@Log4j2
public class SqlDealMessageListener {


}