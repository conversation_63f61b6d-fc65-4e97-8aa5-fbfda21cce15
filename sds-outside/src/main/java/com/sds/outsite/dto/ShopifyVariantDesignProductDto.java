/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dto;

import com.ziguang.base.model.Product;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ShopifyVariantDesignProductDto implements Comparable<ShopifyVariantDesignProductDto> {

    Long id;
    String shopifyVariantId;
    String shopifyProductId;
    String designModel;
    Long designProductId;
    String sku;
    String keyId;
    Long sdsDesignProductGroupId;
    Long merchantStoreId;
    Long merchantId;
    Date createTime;
    Long sdsProductId;
    @ApiModelProperty(hidden = true)
    Product product;

    @ApiModelProperty("第三方平台价格")
    private BigDecimal platformPrice;

    @Override
    public int compareTo(ShopifyVariantDesignProductDto o) {
        if (o.getProduct() == null && this.getProduct() == null) {
            return 0;
        }
        if (o.getProduct() == null) {
            return 1;
        }
        if (this.getProduct() == null) {
            return -1;
        }
        int res = this.getProduct().getSizeSort().compareTo(o.getProduct().getSizeSort());
        if (res == 0) {
            return this.getProduct().getColorSort().compareTo(o.getProduct().getColorSort());
        }
        return res;
    }
}

