package com.sds.outsite.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PublishedProductDto {
    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "平台产品ID")
    private String productId;

    @ApiModelProperty(value = "产品标题")
    private String title;

    @ApiModelProperty(value = "产品图片")
    private String image;

    @ApiModelProperty(value = "所属变体最小售价")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "所属变体最大售价")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "汇出时间-微秒")
    private Long publishedAt;

    @ApiModelProperty("货币编码")
    private String currencyCode;

    @ApiModelProperty("变体ID")
    private List<String> variantIds;

    @ApiModelProperty("货币")
    private String currencySymbol;
}
