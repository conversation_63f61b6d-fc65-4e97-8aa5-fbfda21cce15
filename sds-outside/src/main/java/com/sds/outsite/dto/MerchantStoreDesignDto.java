package com.sds.outsite.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class MerchantStoreDesignDto implements Serializable {

	private String status;
	private String buttonName;
	@ApiModelProperty(value = "素材列表开关 online 开 offline 关")
	private String materialListStatus;
	@ApiModelProperty(value = "产品列表开关 online 开 offline 关")
	private String productListStatus;
}

