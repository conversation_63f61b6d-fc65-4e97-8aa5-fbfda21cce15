package com.sds.outsite.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ziguang.base.model.Product;
import com.ziguang.base.model.PrototypeLayer;
import com.ziguang.base.model.PrototypePsd;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ShopifyProductCompoundDto {
    @JsonProperty("psd_ids")
    List<Long> psdIds;
    @JsonProperty("design_status")
    Integer designStatus;
    //不可设计的原因
    private String disableDesignReason;
    @JsonProperty("layers")
    List<PrototypeLayer> prototypeLayers;
    //
    @JsonProperty(value = "design_scope")
    private Integer designScope;
    @JsonProperty("psds")
    private List<OutSitePrototypePsdDTO> prototypePsds;
    private List<OutSitePrototypePsdDTO> designFiles;
    @ApiModelProperty(hidden = true)
    private Product product;
    @JsonProperty("prototype_id")
    private Long prototypeId;
    ShopifyVariantDesignProductAttributeDto subproducts;

}
