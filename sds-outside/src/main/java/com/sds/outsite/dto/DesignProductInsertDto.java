package com.sds.outsite.dto;

import com.sds.outsite.entity.DesignProduct;
import com.sds.outsite.entity.DesignProductImage;
import com.sds.outsite.entity.DesignProductLayer;
import com.sds.outsite.entity.theme.DesignProductThemePrototypeStyle;
import com.sds.outsite.entity.theme.DesignProductThemeRelationMaterial;
import com.sds.outsite.entity.theme.DesignProductThemeRelationMaterialCategory;
import com.ziguang.base.model.PrototypeLayer;
import lombok.Data;

import java.util.List;

@Data
public class DesignProductInsertDto {
    /**
     * 用来打印日志用的
     */
    private String keyId;
    List<DesignProduct> designProducts;
    List<DesignProductImage> designProductImages;
    List<DesignProductLayer> designProductLayers;
    List<DesignProductThemePrototypeStyle> designProductThemePrototypeStyleList;
    List<DesignProductThemeRelationMaterial> designProductThemeRelationMaterialList;
    List<DesignProductThemeRelationMaterialCategory> designProductThemeRelationMaterialCategoryList;
    List<PrototypeLayer> prototypeLayers;

    public List<DesignProduct> getDesignProducts() {
        return designProducts;
    }

    public void setDesignProducts(List<DesignProduct> designProducts) {
        this.designProducts = designProducts;
    }

    public List<DesignProductImage> getDesignProductImages() {
        return designProductImages;
    }

    public void setDesignProductImages(List<DesignProductImage> designProductImages) {
        this.designProductImages = designProductImages;
    }

    public List<DesignProductLayer> getDesignProductLayers() {
        return designProductLayers;
    }

    public void setDesignProductLayers(List<DesignProductLayer> designProductLayers) {
        this.designProductLayers = designProductLayers;
    }

    public List<PrototypeLayer> getPrototypeLayers() {
        return prototypeLayers;
    }

    public void setPrototypeLayers(List<PrototypeLayer> prototypeLayers) {
        this.prototypeLayers = prototypeLayers;
    }
}
