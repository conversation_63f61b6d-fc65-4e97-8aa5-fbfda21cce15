package com.sds.outsite.dto;

import com.sdsdiy.common.base.entity.dto.BaseDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
@Data
public class ShopifyStoreDesignRespDto extends BaseDTO {

	String shopifyProductId;
	String keyId;
	String productId;
	String variantId;
	List<String> imageUrls;
	String imageUrl;
	String thumbImageUrl;

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public void setThumbImageUrl(String thumbImageUrl) {
		this.thumbImageUrl = thumbImageUrl;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getVariantId() {
		return variantId;
	}

	public void setVariantId(String variantId) {
		this.variantId = variantId;
	}

	public void setKeyId(String keyId) {
		this.keyId = keyId;
	}

}

