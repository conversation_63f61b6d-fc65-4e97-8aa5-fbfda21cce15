package com.sds.outsite.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ShopifyVariantDto {

    private Long id;

    private Long productId;

    private String title;

    private BigDecimal price;

    private String sku;

    private Integer position;

    private String inventoryPolicy;

    private BigDecimal compareAtPrice;

    private String fulfillmentService;

    private String inventoryManagement;

    private String option1;

    private String option2;

    private String option3;

    private String createdAt;

    private String updatedAt;

    private Boolean taxable;

    private String barcode;

    private Integer grams;

    private Long imageId;

    private Integer weight;

    private String weightUnit;

    private Long inventoryItemId;

    private Integer inventoryQuantity;

    private Integer oldInventoryQuantity;

    private Boolean requiresShipping;

    private String adminGraphqlApiId;
}
