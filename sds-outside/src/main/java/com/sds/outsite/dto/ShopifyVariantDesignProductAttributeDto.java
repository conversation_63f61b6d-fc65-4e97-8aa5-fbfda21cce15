/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dto;

import com.google.common.collect.Lists;
import com.ziguang.base.dto.ProductAttributeDto;
import com.ziguang.base.model.Product;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ShopifyVariantDesignProductAttributeDto {
    List<ProductAttributeDto> attributers;
    List<ShopifyVariantDesignProductDto> items;

    public void addItem(ShopifyVariantDesignProductDto item) {
        if (attributers == null) {
            attributers = new ArrayList<>();
        }
        if (items == null) {
            items = Lists.newArrayList();
        }
        Product product = item.getProduct();
        if (product == null) {
            return;
        }

        items.add(item);
        for (ProductAttributeDto attributer : attributers) {
            if (attributer.getSize().equals(product.getSize())) {
                attributer.addColor(product.uniqueColor());
                return;
            }
        }
        ProductAttributeDto productAttributeDto = new ProductAttributeDto();
        productAttributeDto.setSize(product.getSize());
        productAttributeDto.setSizeId(product.getSizeId());
        productAttributeDto.addColor(product.uniqueColor());
        attributers.add(productAttributeDto);
    }

}

