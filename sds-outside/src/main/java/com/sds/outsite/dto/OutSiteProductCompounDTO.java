/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sdsdiy.designproductdata.constant.DesignTypeConstant;
import com.ziguang.base.dto.TaskDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
public class OutSiteProductCompounDTO {

    //可以直接使用: @Length(max=50,message="用户名长度不能大于50")显示错误消息
    //columns START
    @JsonProperty("id")
    private String id;
    String platform;
    @JsonProperty("product_id")
    private Long productId;
    @JsonProperty("combine_rule")
    private String combineRule;
    @JsonProperty("combine_rule_type")
    private Integer combineRuleType;
    private Integer num;
    @JsonProperty("prototypes")
    private List<TaskDto> prototypes;
    private String previewUrl;
    /**
     * {@link DesignTypeConstant}
     */
    @ApiModelProperty("设计类型：theme-主题，material-素材，word-文字，customImage-来图")
    private String designType;
    @ApiModelProperty("操作类型：无设计直接加入购物车-directAddCart")
    private String operateType;

    //columns END
}

