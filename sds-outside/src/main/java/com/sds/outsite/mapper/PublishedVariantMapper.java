package com.sds.outsite.mapper;

import cn.hutool.core.collection.CollUtil;
import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.product.PublishedVariantDao;
import com.sds.outsite.entity.product.PublishedVariant;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class PublishedVariantMapper extends TKBaseService<PublishedVariantDao, PublishedVariant> {

    /**
     * 查询列表
     *
     * @param platform
     * @param productIds
     *
     * @return
     */
    public List<PublishedVariant> findAll(String platform, Set<String> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        TkExample example = new TkExample(PublishedVariant.class);
        example.createCriteria()
            .andEqualTo("platform", platform)
            .andIn("productId", productIds);
        return this.dao.selectByExample(example);
    }

    public PublishedVariant findOneByPlatformAndVariantId(String platform, String variantId) {
        TkExample example = new TkExample(PublishedVariant.class);
        example.createCriteria()
            .andEqualTo("platform", platform)
            .andEqualTo("variantId", variantId);
        return this.dao.selectOneByExample(example);
    }


    /**
     * 根据平台和产品ID查询变体列表
     *
     * @param platform
     * @param productId
     *
     * @return
     */
    public List<PublishedVariant> findAllByPlatformAndProductId(String platform, String productId) {
        TkExample example = new TkExample(PublishedVariant.class);
        example.createCriteria()
            .andEqualTo("platform", platform)
            .andEqualTo("productId", productId);
        return this.dao.selectByExample(example);
    }

    public int deleteByProductId(String platform, String productId) {
        TkExample example = new TkExample(PublishedVariant.class);
        example.createCriteria()
            .andEqualTo("platform", platform)
            .andEqualTo("productId", productId);
        return this.dao.deleteByExample(example);
    }
}
