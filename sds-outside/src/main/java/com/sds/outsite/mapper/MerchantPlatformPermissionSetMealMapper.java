package com.sds.outsite.mapper;

import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.font.OutMerchantPlatformPermissionSetMealDao;
import com.sds.outsite.entity.MerchantPlatformPermissionSetMeal;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MerchantPlatformPermissionSetMealMapper extends TKBaseService<OutMerchantPlatformPermissionSetMealDao, MerchantPlatformPermissionSetMeal> {

    public List<MerchantPlatformPermissionSetMeal> getByMerchantId(Long merchantId, Integer status,Integer setMealType) {
        if (null==merchantId||merchantId.equals(0L)) {
            return Collections.emptyList();
        }

        TkExample example = new TkExample(MerchantPlatformPermissionSetMeal.class);
        example.createCriteria()
            .andEqualTo("merchantId", merchantId)
            .andEqualTo("isDelete", 0L)
            .andEqualTo("setMealType", setMealType)
            .andEqualTo("status", status);
        return this.dao.selectByExample(example);
    }

}
