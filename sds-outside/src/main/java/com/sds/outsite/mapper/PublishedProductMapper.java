package com.sds.outsite.mapper;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ps.base.dao.TkExample;
import com.ps.base.service.TKBaseService;
import com.sds.outsite.dao.product.PublishedProductDao;
import com.sds.outsite.entity.product.PublishedProduct;
import com.sds.outsite.param.PublishedProductQueryParam;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PublishedProductMapper extends TKBaseService<PublishedProductDao, PublishedProduct> {

    /**
     * 查询单个
     *
     * @param platform
     * @param productId
     *
     * @return
     */
    public PublishedProduct findOne(String platform, String productId) {
        TkExample example = new TkExample(PublishedProduct.class);
        example.createCriteria()
            .andEqualTo("platform", platform)
            .andEqualTo("productId", productId);
        return this.dao.selectOneByExample(example);
    }

    public int deleteByProductId(String platform, String productId) {
        TkExample example = new TkExample(PublishedProduct.class);
        example.createCriteria()
            .andEqualTo("platform", platform)
            .andEqualTo("productId", productId);
        return this.dao.deleteByExample(example);
    }

    /**
     * 查询分页
     *
     * @param param
     *
     * @return
     */
    public PageInfo<PublishedProduct> getPage(PublishedProductQueryParam param) {
        TkExample example = new TkExample(PublishedProduct.class);
        example.orderBy("published_at").desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("platform", param.getPlatform())
            .andEqualTo("published", true)
            .andEqualTo("shopName", param.getShopName());
        if (StrUtil.isNotEmpty(param.getDesignatedProductId())) {
            criteria.andNotEqualTo("productId", param.getDesignatedProductId());
        }
        if (StrUtil.isNotEmpty(param.getProductName())) {
            criteria.andLike("title", "%" + param.getProductName() + "%");
        }

        PageMethod.startPage(param.getCurrentPage(), param.getPageSize());
        return new PageInfo<>(this.dao.selectByExample(example));
    }

    public PageInfo<PublishedProduct> findPageByShopNameAndOffset(
        PublishedProductQueryParam queryParam,
        int limit, int offset
    ) {
        PageInfo<PublishedProduct> pageInfo = new PageInfo<>();

        TkExample example = new TkExample(PublishedProduct.class);
        Example.Criteria criteria = example.createCriteria();
        example.orderBy("publishedAt").desc();
        if (StrUtil.isNotEmpty(queryParam.getDesignatedProductId())) {
            criteria.andNotEqualTo("productId", queryParam.getDesignatedProductId());
        }
        criteria.andEqualTo("platform", queryParam.getPlatform())
            .andEqualTo("published", true)
            .andEqualTo("shopName", queryParam.getShopName());
        if (StrUtil.isNotEmpty(queryParam.getProductName())) {
            criteria.andLike("title", "%" + queryParam.getProductName() + "%");
        }

        example.setRowBounds(limit,offset);
        List<PublishedProduct> entityList = example.selectByExampleAndRowBounds(this.dao) ;

        pageInfo.setList(entityList);
        pageInfo.setTotal(this.dao.selectCountByExample(example));
        return pageInfo;
    }
}
