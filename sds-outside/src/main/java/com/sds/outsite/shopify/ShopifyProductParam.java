package com.sds.outsite.shopify;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ShopifyProductParam {

    /**
     * 产品标题
     */
    private String title;

    /**
     * 产品描述，支持html
     */
    private String bodyHtml;

    private String templateSuffix;

    /**
     * 产品厂商
     */
    private String vendor;

    /**
     * 产品分类
     */
    private String productType;

    /**
     * 产品标签
     */
    private List<String> tags;

    /**
     * SEO title
     */
    private String metafieldsGlobalTitleTag;

    /**
     * seo描述
     */
    private String metafieldsGlobalDescriptionTag;

    /**
     * 变体
     */
    private List<ShopifyVariantParam> variants;

    /**
     * 选项，如大小、颜色等，如果有定义，变体必须包含该选项
     */
    private List<ShopifyProductOptionParam> options;

    /**
     * 图片
     */
    private List<ShopifyProductImageParam> images;

    /**
     * 自定义属性
     */
    private List<ShopifyMetafieldParam> metafields;
}
