package com.sds.outsite.shopify;

import com.sdsdiy.ecommerceapi.constant.ShopifyInventoryPolicyEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ShopifyVariantParam {

    private String sku;

    private BigDecimal price;

    /**
     * 库存
     */
    private Integer inventoryQuantity;

    /**
     * 是否需要配送
     */
    private Boolean requiresShipping = true;

    /**
     * {@link com.sds.outsite.constant.ShopifyWeightUnitEnum}
     */
    private String weightUnit;

    private BigDecimal weight;

    /**
     * 原价
     */
    private BigDecimal compareAtPrice;

    /**
     * 库存类型
     * {@link ShopifyInventoryPolicyEnum}
     */
    private String inventoryPolicy;

    private String inventoryManagement;

    private String option1;

    private String option2;

    private String option3;
}
