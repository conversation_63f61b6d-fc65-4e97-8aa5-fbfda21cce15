/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.dao.BaseMapper;
import com.ps.base.entity.SearchBean;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignTaskDesignText;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;


public interface DesignTaskDesignTextDao extends BaseMapper<DesignTaskDesignText>{
	
	 @SelectProvider(type = BaseDaoProvider.class,method = "count")
     int count(SearchBean<DesignTaskDesignText> searchBean);

     @SelectProvider(type = BaseDaoProvider.class,method = "list")
     List<DesignTaskDesignText> list(SearchBean<DesignTaskDesignText> searchBean);	

}
