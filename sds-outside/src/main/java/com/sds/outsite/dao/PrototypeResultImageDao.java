package com.sds.outsite.dao;

import com.ps.base.dao.BaseMapper;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.sds.outsite.entity.PrototypeResultImage;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 功能描述: <br>
 *
 * @Author: lin_bin
 * @Date: 2020/11/26 19:52
 */
public interface PrototypeResultImageDao extends BaseMapper<PrototypeResultImage> {


    @Select("select * from   `prototype_result_image` where prototype_id=#{prototypeId} and psd_file_id in (#{ids})")
    @Lang(SimpleSelectInLangDriver.class)
    List<PrototypeResultImage> getByPsdFileIds(@Param("ids") List<Long> ids, @Param("prototypeId") Long prototypeId);

}
