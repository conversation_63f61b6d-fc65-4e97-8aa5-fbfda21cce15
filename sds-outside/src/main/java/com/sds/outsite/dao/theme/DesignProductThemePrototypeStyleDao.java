package com.sds.outsite.dao.theme;

import com.ps.base.dao.BaseMapper;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.sds.outsite.entity.theme.DesignProductThemePrototypeStyle;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/1/6
 */
public interface DesignProductThemePrototypeStyleDao extends BaseMapper<DesignProductThemePrototypeStyle> {
    @Delete("delete from design_product_theme_prototype_style where design_product_id in(#{designProductIds})")
    @Lang(SimpleSelectInLangDriver.class)
    void deleteByDesignProductIds(@Param("designProductIds") Collection<Long> designProductIds);
}
