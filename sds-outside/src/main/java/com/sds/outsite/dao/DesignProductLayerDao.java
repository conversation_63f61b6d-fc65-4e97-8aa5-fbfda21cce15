/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.entity.SearchBean;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignProductLayer;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;


public interface DesignProductLayerDao extends tk.mybatis.mapper.common.Mapper<DesignProductLayer>{
	
	 @SelectProvider(type = BaseDaoProvider.class,method = "count")
     int count(SearchBean<DesignProductLayer> searchBean);

     @SelectProvider(type = BaseDaoProvider.class,method = "list")
     List<DesignProductLayer> list(SearchBean<DesignProductLayer> searchBean);


    @Select("SELECT * FROM design_product_layer where merchant_id = #{merchantId} and compound_id in (#{compoundIds})")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProductLayer> findByCompoundIds(Long merchantId, List<String> compoundIds);

}
