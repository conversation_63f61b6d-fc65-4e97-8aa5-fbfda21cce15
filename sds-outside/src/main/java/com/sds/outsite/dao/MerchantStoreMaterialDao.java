package com.sds.outsite.dao;

import com.ps.base.dao.BaseMapper;
import com.sds.outsite.entity.MerchantStoreMaterial;
import com.ziguang.base.model.Material;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 功能描述: <br>
 *
 * @Author: lin_bin
 * @Date: 2020/11/26 19:52
 */
public interface MerchantStoreMaterialDao extends BaseMapper<MerchantStoreMaterial> {

    /**
     * 查询条件
     *
     * @param merchantId
     * @param merchantStoreId
     * @param merchantStoreMaterialCategoryId
     * @param search
     * @return
     */

    @Select({"<script>" +
            "SELECT m.* FROM  merchant_store_material  msm  LEFT JOIN material m ON msm.material_id =m.id " +
            "WHERE msm.merchant_id = #{merchantId} AND  msm.merchant_store_id = #{merchantStoreId}" +
            " and msm.is_delete = 0 AND  m.is_delete = 0" +
            "<if test=\"merchantStoreMaterialCategoryId != null\">" +
            " AND msm.merchant_store_material_category_id =#{merchantStoreMaterialCategoryId}" +
            "</if>" +
            "<if test=\"search != null\">" +
            " AND m.name  like concat('%',#{search},'%') " +
            "</if>" +
            " group by m.id" +
            " ORDER BY msm.id" +
            "</script>"})
    List<Material> selectByCondition(@Param("merchantId") Long merchantId,
                                     @Param("merchantStoreId") Long merchantStoreId,
                                     @Param("merchantStoreMaterialCategoryId") Long merchantStoreMaterialCategoryId,
                                     @Param("search") String search);

    @Select("SELECT * FROM( SELECT id, material_id, merchant_store_material_category_id FROM merchant_store_material" +
            " WHERE is_delete = 0 AND merchant_id=#{merchantId} AND merchant_store_id =#{merchantStoreId}" +
            " ORDER BY id DESC LIMIT 10000 ) t" +
            " GROUP BY merchant_store_material_category_id")
    List<MerchantStoreMaterial> groupCatrgoryId(@Param("merchantId") Long merchantId,@Param("merchantStoreId") Long merchantStoreId);
}
