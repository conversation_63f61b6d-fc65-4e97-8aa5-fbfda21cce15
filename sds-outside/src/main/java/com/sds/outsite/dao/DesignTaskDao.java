/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.dao.BaseMapper;
import com.ps.base.entity.SearchBean;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignTask;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;


public interface DesignTaskDao extends BaseMapper<DesignTask>{
	
	 @SelectProvider(type = BaseDaoProvider.class,method = "count")
     int count(SearchBean<DesignTask> searchBean);

     @SelectProvider(type = BaseDaoProvider.class,method = "list")
     List<DesignTask> list(SearchBean<DesignTask> searchBean);	

}
