package com.sds.outsite.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sds.outsite.entity.PrototypeGroup;
import org.apache.ibatis.annotations.Select;

/**
 * 功能描述: <br>
 *
 * @Author: lin_bin
 * @Date: 2020/11/26 19:52
 */
public interface PrototypeGroupDao extends BaseMapper<PrototypeGroup> {
    @Select("select * from prototype_group where id in( " +
            "SELECT  prototype_group_id FROM `prototype_product_rel` where prototype_id = #{prototypeId}) limit 1")
    PrototypeGroup getByPrototypeId(Long prototypeId);
}
