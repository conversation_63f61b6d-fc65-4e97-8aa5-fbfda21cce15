package com.sds.outsite.dao;

import com.ps.base.dao.BaseMapper;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.sds.outsite.entity.PsdContent;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
public interface PsdContentDao extends BaseMapper<PsdContent> {

    @Delete("delete from psd_content where psd_code = #{psdCode}")
    int deleteByCode(String psdCode);

    @Select("select * from psd_content where psd_code in (#{psdCodes})")
    @Lang(SimpleSelectInLangDriver.class)
    List<PsdContent> findByCodes(@Param("psdCodes") List<String> psdCodes);
}
