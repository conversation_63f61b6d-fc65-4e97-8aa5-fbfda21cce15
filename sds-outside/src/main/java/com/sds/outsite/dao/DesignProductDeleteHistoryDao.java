/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.dao.BaseMapper;
import com.ps.base.entity.SearchBean;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignProduct;
import com.sds.outsite.entity.DesignProductDeleteHistory;
import org.apache.ibatis.annotations.SelectProvider;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;


public interface DesignProductDeleteHistoryDao extends Mapper<DesignProductDeleteHistory>, BaseMapper<DesignProductDeleteHistory> {
    @SelectProvider(type = BaseDaoProvider.class, method = "count")
    int count(SearchBean<DesignProduct> searchBean);

    @SelectProvider(type = BaseDaoProvider.class, method = "shardList")
    List<DesignProduct> list(SearchBean<DesignProduct> searchBean);

}
