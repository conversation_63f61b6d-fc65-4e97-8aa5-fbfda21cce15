/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.entity.SearchBean;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignProduct;
import com.ziguang.base.dto.StatisticsShowDto;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;



public interface DesignProductDao extends tk.mybatis.mapper.common.Mapper<DesignProduct> {
    @Select({"<script>" +
            "SELECT DATE_FORMAT(FROM_UNIXTIME(tc.created_time/1000),'${dateType}') `date`,COUNT(tc.id) `COUNT` FROM " +
            " design_product  as tc where tc.created_time &gt;= #{timeStart} AND tc.type in(1,3,4) AND tc.created_time &lt;= #{timeEnd} and tc.merchant_id = #{merchantId} and tc.status = 2" +
            "<if test=\"userIds != null\">"+
            " and tc.user_id in (${userIds})" +
            "</if>" +
            " GROUP BY `date`"+
            "</script>"})
    List<StatisticsShowDto> forTask(String userIds, Long merchantId, Long timeStart, Long timeEnd, String dateType);
    @SelectProvider(type = BaseDaoProvider.class, method = "count")
    int count(SearchBean<DesignProduct> searchBean);

    @SelectProvider(type = BaseDaoProvider.class, method = "shardList")
    List<DesignProduct> list(SearchBean<DesignProduct> searchBean);
    @Select("select id from design_product where merchant_id = #{merchantId} limit #{limit},#{size}")
    List<Long> findByMerchantIdSize(Long merchantId,int limit,int size);

    @Update("update design_product set pid =#{pid},type =#{type} where merchant_id = #{merchantId} and  id in (#{ids}) ")
    @Lang(SimpleSelectInLangDriver.class)
    void updatePidType(@Param("merchantId")Long merchantId,@Param("ids")List<Long> ids, @Param("type") Integer type,@Param("pid") Long pid);

    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and pid in (#{parentIds}) and `status` != 99")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProduct> findByParentIds(Long merchantId, List<Long> parentIds);
    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and pid in (#{parentIds}) and material_group_pid in (#{groupIds}) and `status` != 99")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProduct> findByMaterialParentIds(Long merchantId, List<Long> parentIds, List<Long> groupIds);

    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and design_task_id in (#{taskIds}) and type in(#{types})")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProduct> findByTaskIds(Long merchantId, List<Long> taskIds, List<Integer> types);

    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and id in (#{ids})")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProduct> findByIds(Long merchantId, List<Long> ids);
    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and key_id in (#{keyIds})")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProduct> findByKeyIds(Long merchantId, List<String> keyIds);
    @Select("SELECT * FROM design_product where key_id= #{keyId}")
    DesignProduct findByKeyId(String keyId);
    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and key_id in (#{keyIds})")
    @Lang(SimpleSelectInLangDriver.class)
    Map<String,Object> findMapByKeyIds(Long merchantId, List<String> keyIds);

    @Update("update design_product set export_time = export_time + 1 where  merchant_id = #{merchantId} and id in (#{ids}) ")
    @Lang(SimpleSelectInLangDriver.class)
    void increaseExportTime(Long merchantId,List<Long> ids);
    @Update("update design_product set status = #{status} where  merchant_id = #{merchantId} and id in (#{ids}) ")
    @Lang(SimpleSelectInLangDriver.class)
    void updateStatusByIds(Integer status,Long merchantId,List<Long> ids);


    @Update("update design_product set status = #{status} where  merchant_id = #{merchantId} and pid in (#{ids}) ")
    @Lang(SimpleSelectInLangDriver.class)
    void updateStatusByPids(Integer status,Long merchantId,List<Long> ids);


    @Update("update design_product set material_color = #{color} where  merchant_id = #{merchantId} and material_group_pid = #{materialGroupPid} and  design_task_id = #{designTaskId}")
    @Lang(SimpleSelectInLangDriver.class)
    int updateMaterialColor(String color,Long merchantId,Long materialGroupPid,Long designTaskId);

    @Select("SELECT * FROM design_product where merchant_id = #{merchantId} and pid = 0 and sync_es_version < #{version} and `status` != 99 limit #{size}")
    List<DesignProduct> findToEs(Long merchantId, int version, int size);

    @Update("update design_product set sync_es_version = #{version} where merchant_id = #{merchantId} and id in (#{ids})")
    @Lang(SimpleSelectInLangDriver.class)
    void syncEsSuccess(Long merchantId, List<Long> ids, int version);


}
