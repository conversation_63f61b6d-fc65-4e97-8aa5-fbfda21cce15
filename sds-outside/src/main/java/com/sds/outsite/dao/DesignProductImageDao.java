/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.entity.SearchBean;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignProductImage;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;


public interface DesignProductImageDao extends tk.mybatis.mapper.common.Mapper<DesignProductImage>{
	
	 @SelectProvider(type = BaseDaoProvider.class,method = "count")
     int count(SearchBean<DesignProductImage> searchBean);

     @SelectProvider(type = BaseDaoProvider.class,method = "list")
     List<DesignProductImage> list(SearchBean<DesignProductImage> searchBean);


    @Select("SELECT * FROM design_product_image where merchant_id = #{merchantId} and compound_id in (#{compoundIds})")
    @Lang(SimpleSelectInLangDriver.class)
    List<DesignProductImage> findByCompoundIds(Long merchantId, List<String> compoundIds);


    @Select("SELECT * FROM design_product_image where merchant_id = #{merchantId} and compound_id = #{compoundId} order by psd_sort")
    List<DesignProductImage> findMapCompoundId(Long merchantId, String compoundId);

    @Update("UPDATE design_product_image  SET img_url = #{url} WHERE   merchant_id = #{merchantId} and design_md5 = #{md5} ")
    int updateImage(String url,Long merchantId,String md5);

}
