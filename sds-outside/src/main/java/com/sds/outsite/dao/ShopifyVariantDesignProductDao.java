/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.sds.outsite.dao;

import com.ps.base.entity.SearchBean;
import com.ps.mybatis.languageDriver.SimpleSelectInLangDriver;
import com.ps.ps.dao.Provider.BaseDaoProvider;
import com.sds.outsite.entity.DesignProduct;
import com.sds.outsite.entity.ShopifyVariantDesignProduct;
import com.ziguang.base.dto.StatisticsShowDto;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;


public interface ShopifyVariantDesignProductDao extends tk.mybatis.mapper.common.Mapper<ShopifyVariantDesignProduct> {




}
