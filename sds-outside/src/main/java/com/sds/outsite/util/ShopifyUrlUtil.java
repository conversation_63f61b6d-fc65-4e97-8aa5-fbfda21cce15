package com.sds.outsite.util;

import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */
public class ShopifyUrlUtil {

    public static final String TOKEN_HEADER = "X-Shopify-Access-Token";

    private static final String API_VERSION = "2020-10";

    private class Url {
        private static final String BASE_URL = "https://{shop_name}.myshopify.com/admin/api/{api_version}";
        private static final String CREATE_PRODUCT = "/products.json";
        private static final String GET_VARIANT = "/variants/{variant_id}.json";
    }

    /**
     * 创建产品的URL
     *
     * @param shopId
     * @return
     */
    public static String createProductUrl(String shopId) {
        return UriComponentsBuilder.fromUriString(Url.BASE_URL + Url.CREATE_PRODUCT)
            .buildAndExpand(shopId, API_VERSION)
            .toString();
    }

    public static String getVariantUrl(String shopName, Long variantId) {
        return UriComponentsBuilder.fromUriString(Url.BASE_URL + Url.GET_VARIANT)
            .buildAndExpand(shopName, API_VERSION, variantId)
            .toString();
    }
}
