package com.sds.outsite.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * sqs配置
 *
 * <AUTHOR>
 */
@Configuration
public class SqsConfig {

    public static String SQS_ENV;

    @Value("${env:#{null}}")
    public void setSqsEnv(String sqsEnv) {
        SQS_ENV = sqsEnv;
        OUTSITE_SYNC_PRODUCT_URL = OUTSITE_SYNC_PRODUCT_URL + SQS_ENV;
    }

    public static String OUTSITE_SYNC_PRODUCT_URL = "https://sqs.cn-north-1.amazonaws.com.cn/786823027346/outsite_sync_published_product_";
}
